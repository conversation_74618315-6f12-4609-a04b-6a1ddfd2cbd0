services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: qldt_vov
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - /home/<USER>/qldt_vov:/var/www
      - /home/<USER>/qldt_vov/storage:/var/www/storage
      - /home/<USER>/qldt_vov/bootstrap/cache:/var/www/bootstrap/cache
      - /home/<USER>/qldt_vov/public:/var/www/public

    ports:
      - "8005:9000"
    environment:
      - DB_HOST=mysql       # Tên container MySQL đã chạy trong network external
      - DB_PORT=3306
      - DB_USERNAME=remotevov
      - DB_PASSWORD=remotevov@cyberuni
      - DB_DATABASE=qldt_vov
    networks:
      - qldt-vov_network
      - mysql-docker_internal_net

  redis:
    image: redis:alpine
    container_name: laravel_redis_vov2
    restart: unless-stopped
    ports:
      - "6381:6379"
    networks:
      - qldt-vov_network

networks:
  qldt-vov_network:
    external: true
  mysql-docker_internal_net:
    external: true
