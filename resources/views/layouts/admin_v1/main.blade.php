<!DOCTYPE html>
<html lang="en">
<!-- begin::Head -->

<head><!--begin::Base Path (base relative path for assets of this page) -->
    <meta charset="utf-8" />
    <title>@yield('title') | {{ config('app.name') }}</title>
    <meta name="description" content="Aside light skin example">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!--begin::Page Vendors Styles(used by this page) -->
    <link href="{{ asset('/theme/admin_v1/vendors/custom/fullcalendar/fullcalendar.bundle.css') }}" rel="stylesheet"
        type="text/css" />
    <!--end::Page Vendors Styles -->


    <!--begin::Global Theme Styles(used by all pages) -->
    <link href="{{ asset('/theme/admin_v1/vendors/global/vendors.bundle.css') }}" rel="stylesheet" type="text/css" />
    <!--end::Global Theme Styles -->

    <!--begin::Layout Skins(used by all pages) -->

    <link href="{{ asset('/theme/admin_v1/css/demo1/skins/header/base/' . session('color') . '.css') }}" rel="stylesheet"
        type="text/css" />
    <link href="{{ asset('/theme/admin_v1/css/demo1/skins/header/menu/' . session('color') . '.css') }}" rel="stylesheet"
        type="text/css" />
    <link href="{{ asset('/theme/admin_v1/css/demo1/skins/brand/' . session('color') . '.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('/theme/admin_v1/css/demo1/skins/aside/' . session('color') . '.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('/theme/admin_v1/vendors/custom/datatables/datatables.bundle.css') }}" rel="stylesheet" type="text/css">
    <!-- Font Awesome 6.4.2 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!--end::Layout Skins -->
    <link href="{{ asset('css/web.css') }}" rel="stylesheet">
    {{-- <link href="{{ asset('css/app.css') }}" rel="stylesheet">--}}

    <!-- Notification -->
    <link href="{{ asset('/theme/admin_v1/css/popup.css') }}" rel="stylesheet" type="text/css">
    <!-- end:Notification -->

    <link rel="shortcut icon" href="{{ config('app.favicon_path') }}" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="{{ asset('storage/local.js') }}" type="text/javascript"></script>
    @yield('css')
    <link href="{{ asset('/theme/admin_v1/css/demo1/style.bundle.css') }}" rel="stylesheet" type="text/css" />
    <script>
        var campus_code = "{{session('campus_db')}}";
        var user = {
            user_code: "{{auth()->user()->user_code}}",
            user_login: "{{auth()->user()->user_login}}",
        };
    </script>
</head>
<!-- end::Head -->
<!-- begin::Body -->

<body class="kt-quick-panel--right kt-demo-panel--right kt-offcanvas-panel--right kt-header--fixed kt-header-mobile--fixed kt-subheader--enabled kt-subheader--fixed kt-subheader--solid kt-aside--enabled kt-aside--fixed kt-page--loading">
    <div id="app">

        <!-- begin:: Page -->
        <!-- begin:: Header Mobile -->
        <div id="kt_header_mobile" class="kt-header-mobile  kt-header-mobile--fixed ">
            <div class="kt-header-mobile__logo">
                <a href="{{ route('admin.home') }}">
                    <img alt="Logo" src="{{ asset(config('app.logo_path')) }}" width="110" />
                </a>
            </div>
            <div class="kt-header-mobile__toolbar">
                <button class="kt-header-mobile__toggler kt-header-mobile__toggler--left" id="kt_aside_mobile_toggler">
                    <span></span></button>

                <button class="kt-header-mobile__topbar-toggler" id="kt_header_mobile_topbar_toggler"><i
                        class="flaticon-more"></i></button>
            </div>
        </div>
        <!-- end:: Header Mobile -->
        <div class="kt-grid kt-grid--hor kt-grid--root">
            <div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--ver kt-page">
                <div class="kt-aside  kt-aside--fixed  kt-grid__item kt-grid kt-grid--desktop kt-grid--hor-desktop"
                    id="kt_aside">
                    <!-- begin:: Aside -->
                    <div class="kt-aside__brand kt-grid__item " id="kt_aside_brand">
                        <div class="kt-aside__brand-logo">
                            <a href="{{ route('admin.home') }}">
                                <img alt="Logo" src="{{ asset(config('app.logo_path')) }}" width="135" />
                            </a>
                        </div>

                        <div class="kt-aside__brand-tools">
                            <button class="kt-aside__brand-aside-toggler" id="kt_aside_toggler">
                                <span><svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24"
                                        version="1.1" class="kt-svg-icon">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <polygon points="0 0 24 0 24 24 0 24" />
                                            <path d="M5.29288961,6.70710318 C4.90236532,6.31657888 4.90236532,5.68341391 5.29288961,5.29288961 C5.68341391,4.90236532 6.31657888,4.90236532 6.70710318,5.29288961 L12.7071032,11.2928896 C13.0856821,11.6714686 13.0989277,12.281055 12.7371505,12.675721 L7.23715054,18.675721 C6.86395813,19.08284 6.23139076,19.1103429 5.82427177,18.7371505 C5.41715278,18.3639581 5.38964985,17.7313908 5.76284226,17.3242718 L10.6158586,12.0300721 L5.29288961,6.70710318 Z"
                                                fill="#000000" fill-rule="nonzero"
                                                transform="translate(8.999997, 11.999999) scale(-1, 1) translate(-8.999997, -11.999999) " />
                                            <path d="M10.7071009,15.7071068 C10.3165766,16.0976311 9.68341162,16.0976311 9.29288733,15.7071068 C8.90236304,15.3165825 8.90236304,14.6834175 9.29288733,14.2928932 L15.2928873,8.29289322 C15.6714663,7.91431428 16.2810527,7.90106866 16.6757187,8.26284586 L22.6757187,13.7628459 C23.0828377,14.1360383 23.1103407,14.7686056 22.7371482,15.1757246 C22.3639558,15.5828436 21.7313885,15.6103465 21.3242695,15.2371541 L16.0300699,10.3841378 L10.7071009,15.7071068 Z"
                                                fill="#000000" fill-rule="nonzero" opacity="0.3"
                                                transform="translate(15.999997, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-15.999997, -11.999999) " />
                                        </g>
                                    </svg></span>
                                <span><svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24"
                                        version="1.1" class="kt-svg-icon">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <polygon points="0 0 24 0 24 24 0 24" />
                                            <path d="M12.2928955,6.70710318 C11.9023712,6.31657888 11.9023712,5.68341391 12.2928955,5.29288961 C12.6834198,4.90236532 13.3165848,4.90236532 13.7071091,5.29288961 L19.7071091,11.2928896 C20.085688,11.6714686 20.0989336,12.281055 19.7371564,12.675721 L14.2371564,18.675721 C13.863964,19.08284 13.2313966,19.1103429 12.8242777,18.7371505 C12.4171587,18.3639581 12.3896557,17.7313908 12.7628481,17.3242718 L17.6158645,12.0300721 L12.2928955,6.70710318 Z"
                                                fill="#000000" fill-rule="nonzero" />
                                            <path d="M3.70710678,15.7071068 C3.31658249,16.0976311 2.68341751,16.0976311 2.29289322,15.7071068 C1.90236893,15.3165825 1.90236893,14.6834175 2.29289322,14.2928932 L8.29289322,8.29289322 C8.67147216,7.91431428 9.28105859,7.90106866 9.67572463,8.26284586 L15.6757246,13.7628459 C16.0828436,14.1360383 16.1103465,14.7686056 15.7371541,15.1757246 C15.3639617,15.5828436 14.7313944,15.6103465 14.3242754,15.2371541 L9.03007575,10.3841378 L3.70710678,15.7071068 Z"
                                                fill="#000000" fill-rule="nonzero" opacity="0.3"
                                                transform="translate(9.000003, 11.999999) rotate(-270.000000) translate(-9.000003, -11.999999) " />
                                        </g>
                                    </svg></span>
                            </button>
                            <!--
			<button class="kt-aside__brand-aside-toggler kt-aside__brand-aside-toggler--left" id="kt_aside_toggler"><span></span></button>
			-->
                        </div>
                    </div>
                    <!-- end:: Aside -->
                    <!-- begin:: Aside Menu -->
                    @include('layouts.admin_v1.aside')
                    <!-- end:: Aside Menu -->
                </div>
                <!-- end:: Aside -->

                <div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-wrapper crm-main" id="kt_wrapper">
                    @include('layouts.admin_v1.header')
                    <div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor p-0" id="kt_content">
                        <div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
                            @if(session('status'))
                                <div class="alert alert-{{ session('status.type') }} alert-dismissible fade show" role="alert">
                                    <strong>
                                        @if(session('status.type') == 'success')
                                            <i class="fa fa-check-circle"></i> Thành công!
                                        @elseif(session('status.type') == 'danger')
                                            <i class="fa fa-exclamation-triangle"></i> Lỗi!
                                        @elseif(session('status.type') == 'warning')
                                            <i class="fa fa-exclamation-circle"></i> Cảnh báo!
                                        @else
                                            <i class="fa fa-info-circle"></i> Thông báo!
                                        @endif
                                    </strong>
                                    {{ session('status.messages') }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            @endif
                            @yield('content')
                            <modal-check-status />
                        </div>
                    </div>
                    @include('layouts.admin_v1.footer')
                </div>
            </div>
        </div>
        <!-- end:: Page -->
        <!-- begin::Scrolltop -->
        <div id="kt_scrolltop" class="kt-scrolltop">
            <i class="fa fa-arrow-up"></i>
        </div>
        <!-- end::Scrolltop -->
        @include('layouts.admin_v1.main_search')
    </div>

    <!-- begin::Global Config(global config for global JS sciprts) -->
    <script>
        var KTAppOptions = {
            "colors": {
                "state": {
                    "brand": "#5d78ff",
                    "dark": "#282a3c",
                    "light": "#ffffff",
                    "primary": "#5867dd",
                    "success": "#34bfa3",
                    "info": "#36a3f7",
                    "warning": "#ffb822",
                    "danger": "#fd3995"
                },
                "base": {
                    "label": ["#c5cbe3", "#a1a8c3", "#3d4465", "#3e4466"],
                    "shape": ["#f0f3ff", "#d9dffa", "#afb4d4", "#646c9a"]
                }
            }
        };
    </script>
    <!-- end::Global Config -->

    <!--begin::Global Theme Bundle(used by all pages) -->
    <script src="{{ asset('/theme/admin_v1/vendors/global/vendors.bundle.js') }}" type="text/javascript"></script>
    <script src="{{ asset('/theme/admin_v1/js/demo1/scripts.bundle.js') }}" type="text/javascript"></script>
    <script src="{{ asset('/theme/admin_v1/js/demo1/jquery-ui-1.9.2.custom.min.js') }}" type="text/javascript"></script>
    <!--end::Global Theme Bundle -->

    <!--begin::Page Vendors(used by this page) -->
    <script src="{{ asset('/theme/admin_v1/vendors/custom/fullcalendar/fullcalendar.bundle.js') }}" type="text/javascript"></script>
    <script src="{{ asset('/theme/admin_v1/vendors/custom/gmaps/gmaps.js') }}" type="text/javascript"></script>
    <!--end::Page Vendors -->

    <!--begin::Page Scripts(used by this page) -->
    <script src="{{ asset('/theme/admin_v1/js/demo1/pages/dashboard.js') }}" type="text/javascript"></script>
    <script src="{{ asset('/theme/admin_v1/vendors/custom/datatables/datatables.bundle.js') }}" type="text/javascript"></script>
    <script src="{{ asset('/js/web.js') }}" type="text/javascript"></script>
    {{-- <script src="{{ asset('/js/app.js') }}" type="text/javascript"></script> --}}
    @php
    $currentTime = time();
    $versionJs = $currentTime - ($currentTime % 1000);
    @endphp
    <script src="{{ (asset('/js/app.js') . "?v=" . $versionJs) }}" type="text/javascript"></script>

    @yield('js')
    <!--end::Page Scripts -->
    <script src="{{ asset('/js/snowfall.jquery.min.js') }}"></script>
    <script>
        // jQuery(document).snowfall({image :"{{ asset('/images/tuyet.png') }}", minSize: 10, maxSize:40, maxSpeed : 1});
    </script>

    {{-- ChatMe Widget --}}
    <x-chatme />

</body>
<!-- end::Body -->

</html>
