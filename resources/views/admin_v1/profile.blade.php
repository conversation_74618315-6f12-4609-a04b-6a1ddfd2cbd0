@extends('layouts.admin_v1.main')
@section('title', 'Thông tin sinh viên')
@section('content')
    @if (session('status'))
        <div class="alert alert-{{ session('status')['type'] }} alert-bold" role="alert">
            <div class="alert-text">{!! session('status')['messages'] !!}</div>
        </div>
    @endif
    @php
        $oldUser = $user->oldUser();
    @endphp
    <div class="row  loading-data responsive-stack">
        <div class="responsive-left col-lg-3 col-md-12 ">
            <div class="card2">
                <div class="card-header-profile">
                    <div class="avatar-border">
{{--                        <img src="https://i.pravatar.cc/100" alt="Avatar" class="avatar-img">--}}
                    </div>
                    <div class="info">
                        <div class="name">
                            <h2>{{ $user->full_name }}</h2>
                            <p class="contact-info-small p-3">
                                @if ($user->gender == 1)
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke="#3b82f6" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mars-icon lucide-mars"><path d="M16 3h5v5"/><path d="m21 3-6.75 6.75"/><circle cx="10" cy="14" r="6"/></svg>
                                @else
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="red" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-venus-icon lucide-venus"><path d="M12 15v7"/><path d="M9 19h6"/><circle cx="12" cy="9" r="6"/></svg>
                                @endif
                            </p>
                        </div>
                        <p class="msv">MSV: {{ $user->user_code }}</p>
                    </div>
                    <div class="contact-info">
                        <p><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone-icon lucide-phone"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/></svg>
                        {{ $user->user_telephone }}</p>
                        <p><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail-icon lucide-mail"><rect width="20" height="16" x="2" y="4" rx="2"/><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/></svg>
                        {{ $user->user_email }}</p>
                        <p class="contact-info-small"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cake-icon lucide-cake"><path d="M20 21v-8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8"/><path d="M4 16s.5-1 2-1 2.5 2 4 2 2.5-2 4-2 2.5 2 4 2 2-1 2-1"/><path d="M2 21h20"/><path d="M7 8v3"/><path d="M12 8v3"/><path d="M17 8v3"/><path d="M7 4h.01"/><path d="M12 4h.01"/><path d="M17 4h.01"/></svg>
                        {{ $user->dob()->format('d-m-Y') }}</p>
                        <p class="contact-info-small"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin-house-icon lucide-map-pin-house"><path d="M15 22a1 1 0 0 1-1-1v-4a1 1 0 0 1 .445-.832l3-2a1 1 0 0 1 1.11 0l3 2A1 1 0 0 1 22 17v4a1 1 0 0 1-1 1z"/><path d="M18 10a8 8 0 0 0-16 0c0 4.993 5.539 10.193 7.399 11.799a1 1 0 0 0 .601.2"/><path d="M18 22v-3"/><circle cx="10" cy="10" r="3"/></svg>
                        {{ $user->user_address }}</p>

                    </div>
                </div>

                <div class="card-body">
                    <div class="card-body-lable">Thông tin chi tiết</div>
                    <div class="info-section">
                        <div class="info-section-box">
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Giới tính:</span>
                                <span class="value">{{ $user->gender == 1 ? 'Nam' : 'Nữ' }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Ngày sinh:</span>
                                <span class="value">{{ $user->dob()->format('d-m-Y') }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Địa chỉ:</span>
                                <span class="value">{{ $user->user_address }}</span>
                            </div>
                        </div>
                        <div class="info-section-box">
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Trạng thái học:</span>
                                <span class="status value">{{ $user->study_status_code }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Tên đăng nhập:</span>
                                <span class="value">{{ $user->user_login }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Mã sinh viên:</span>
                                <span class="value">{{ $user->user_code }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Mã sinh viên cũ:</span>
                                <span class="value">{{ $user->old_msv }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Mã lớp:</span>
                                <span class="value">{{ $user->administrative_class }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Tên lớp:</span>
                                <span class="value">{{ $user->administrative_class_name }}</span>
                            </div>
                        </div>
                        <div class="info-section-box">
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">CMND/CCCD:</span>
                                <span class="value">{{ $user->cmt ?? "--" }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Ngày cấp:</span>
                                <span class="value">{{ $user->ngay_cap_cmt }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Nơi cấp:</span>
                                <span class="value">{{ $user->noicap }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Số bằng cấp 3:</span>
                                <span class="value">{{ $user->so_bang ?? "--" }}</span>
                            </div>
                        </div>
                        <div class="info-section-box">
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Khoá nhập học:</span>
                                <span class="value">{{ $user->grade_create ?? null }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Ngày nhập học:</span>
                                <span class="value">{{ $user->admission_date }}</span>
                            </div>
                            <div class="info-section-detail " >
                                <span class="title font-weight-bold">Khung đào tạo:</span>
                                <span class="text-ellipsis value" title="{{ $user->curriculum->name ?? '' }}">{{ $user->curriculum->name ?? '' }}</span>
                            </div>
                        </div>
                        <div class="info-section-box">
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Mã ngành:</span>
                                <span class="value">{{ $user->curriculum->brand_code ?? null }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Chuyên ngành:</span>
                                <span class="text-ellipsis value" title="{{ $user->curriculum->name ?? '' }}">{{ $user->curriculum->chuyen_nganh ?? null }}</span>
                            </div>
                            <div class="info-section-detail">
                                <span class="title font-weight-bold">Chuyên ngành hẹp:</span>
                                <span class="text-ellipsis value" title="{{ $user->curriculum->name ?? '' }}">{{ $user->curriculum->noi_dung ?? null }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-section-small">
                        <div class="row">
                            <div class="col-md-4 info-section-box">
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Trạng thái học:</span>
                                    <span class="status value">{{ $user->study_status_code }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Tên đăng nhập:</span>
                                    <span class="value">{{ $user->user_login }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Mã sinh viên:</span>
                                    <span class="value">{{ $user->user_code }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Mã sinh viên cũ:</span>
                                    <span class="value">{{ $user->old_msv }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Mã lớp:</span>
                                    <span class="value">{{ $user->administrative_class }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Tên lớp:</span>
                                    <span class="value">{{ $user->administrative_class_name }}</span>
                                </div>
                            </div>
                            <div class="col-md-4 info-section-box">
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">CMND/CCCD:</span>
                                    <span class="value">{{ $user->cmt ?? "--" }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Ngày cấp:</span>
                                    <span class="value">{{ $user->ngay_cap_cmt }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Nơi cấp:</span>
                                    <span class="value">{{ $user->noicap }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Số bằng cấp 3:</span>
                                    <span class="value">{{ $user->so_bang ?? "--" }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Khoá nhập học:</span>
                                    <span class="value">{{ $user->grade_create ?? null }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Ngày nhập học:</span>
                                    <span class="value">{{ $user->admission_date }}</span>
                                </div>
                            </div>
                            <div class="col-md-4 info-section-box">
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Mã ngành:</span>
                                    <span class="value">{{ $user->curriculum->brand_code ?? null }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Tên ngành:</span>
                                    <span class="value">{{ $user->curriculum->nganh ?? null }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Chuyên ngành:</span>
                                    <span class="value">{{ $user->curriculum->chuyen_nganh ?? null }}</span>
                                </div>
                                <div class="info-section-detail">
                                    <span class="title font-weight-bold">Chuyên ngành hẹp:</span>
                                    <span class="value">{{ $user->curriculum->noi_dung ?? null }}</span>
                                </div>
                                <div class="info-section-detail " >
                                    <span class="title font-weight-bold">Khung đào tạo:</span>
                                    <span class="value">{{ $user->curriculum->name ?? '' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="responsive-right col-lg-9 col-md-12 ">
            @if ($user->user_level == 3 && $user->curriculum_id != null)
                <profile user_code="{{ request('user_code') }}" user_login="{{ $user->user_login }}"></profile>
            @endif
        </div>
    </div>
@endsection
@section('js')
    <script>
        $('.btn-update-crm').click((e) => {
            e.preventDefault();
            $.ajax({
                dataType: 'json',
                type: 'POST',
                url: '{{ route('api.v1.user.update_info_from_crm') }}',
                data: {
                    '_token': '{{ csrf_token() }}',
                    'student_login': '{{ $user->user_login }}'
                },
                beforeSend: function() {
                    KTApp.block('.loading-data', {
                        overlayColor: '#000000',
                        type: 'v2',
                        message: 'Đang Xử lý dữ liệu (Quá trình có thể mất thời gian, vui lòng không tải lại trang) ...',
                        state: 'success'
                    });
                },
                success: function(data) {
                    KTApp.unblock('.loading-data');
                    alert(data.message);
                    window.location.href = window.location.href;
                },
                error: function(data) {
                    KTApp.unblock('.loading-data');
                    alert(data.responseJSON.message);
                    console.log(2, data);
                }
            });
        });
    </script>
@endsection
@section('css')
    <style >
        .info-section-small{
            display: none;
        }
        .contact-info-small{
            display: none !important;
        }
        .btn-transfer-campus {
            border: none;
            margin: 0;
            padding: 0;
            background: transparent;
            width: 100%;
        }

        .kt-widget.kt-widget--user-profile-1 .kt-widget__body .kt-widget__items .kt-widget__item.kt-widget__item--active:hover {
            background: #ebedf2;
            color: white !important;
        }

        .card2 {
            background-color: #fff;
            padding: 94px 20px 30px 20px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
                        0 4px 6px -2px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .card-header-profile {
            background-color: #F4F7FC;
            border-radius:8px;
            text-align: center;
            padding: 2rem 1rem 1rem;
            position: relative;
        }

        .contact-info {
            margin-top: 1rem;
        }

        .contact-info p {
            background-color: white;
            border: 1px solid #DDDDDD;
            color: #333;
            margin: 0.5rem auto;
            padding: 12px 10px 10px 24px;
            border-radius: 6px;
            width: 90%;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1rem 1.5rem;
        }

        .info-section {
            margin-bottom: 1rem;
        }

        /* .info-section p {
            margin: 0.25rem 0;
            font-size: 0.9rem;
            display: flex;
            justify-content: space-between;
            border-bottom: 1px dotted #ddd;
            padding-bottom: 4px;
        } */

        /* .info-section p span {
            font-weight: bold;
        } */

        .status {
            color: green;
            font-weight: bold;
        }
        .avatar-border {
            border: 3px solid #3b82f6;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            position: absolute;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
        }

        .avatar-img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
        }
        .name {
            font-size: 26px;
            font-weight: bold;
            color: #0f172a;
            margin: 0;
            margin-top: 60px;
            margin-bottom: 0.25rem;
        }
        .msv {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 1.25rem;
        }
        .contact-info svg {
            color: #3b82f6;
        }
        .contact-info p {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 1.25rem;
        }
        .info-section-box{
            border-bottom: 1px dashed transparent;
            border-image: repeating-linear-gradient(
                to right,
                rgba(221, 221, 221, 0.4) 0,
                rgba(221, 221, 221, 0.4) 4px,
                transparent 4px,
                transparent 10px
            ) 1;
            padding-bottom: 4px;
        }
        .info-section-detail{
            margin: 0.25rem 0;
            font-size: 0.9rem;
            display: flex;
            justify-content: space-between;
            font-size: 14px;

        }
        .info-section-detail span{
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 1.25rem;
        }
        .title{
            width: 30%;
        }
        .value{
            width: 70%;
            text-align: right;
        }
        .card-body-lable{
            font-size: 18px;
            color: #000;
            margin-bottom: 1.25rem;
            font-weight: 700;
        }
        .text-ellipsis {
            max-width: 100px;        /* hoặc width: 100%; tùy layout */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;   /* hoặc block nếu bạn cần chiếm full chiều ngang */
            vertical-align: middle;
            cursor: pointer;
            font-size: 16px;
            color: #111111;
        }
        @media (max-width: 2000px) {
            .responsive-stack {
                flex-direction: column !important;
            }
            .col-lg-3 {
                max-width: 100% !important;
            }
            .col-lg-9 {
                max-width: 100% !important;
            }
            .responsive-left{
                width: 100% !important;
                margin-bottom: 20px !important;
            }
            .responsive-right{
                width: 100% !important;
            }
            .card2{
                padding: 94px 20px 0px 20px;

            }
            .card-header-profile{
                text-align: left;
                display: flex;
            }
            .avatar-border {
                left: 10%;
            }
            .info-section{
                display: none;
            }
            .info-section-small{
                display: block;
            }
            .info-section-detail{
                display: block;
            }
            .info-section-box{
                border:none;
            }
            .info{
                position: absolute;
                top: -46px;
                left: 25%;
                transform: translateX(-50%);

            }
            .contact-info {
                display: flex;
                align-items: center;
                width: 100%;
                margin-top: 50px;
            }
            .contact-info p {
                background-color: #F4F7FC;
                border: none;
                color: #333;
                margin: 0.5rem auto;
                padding: 12px 10px 10px 24px;
                border-radius: 6px;
                width: 90%;
                font-size: 15px;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
            .name{
                display: flex;
                align-items: center;
                height: 40px;
            }
            .contact-info-small{
                display: block !important;
            }
            .text-ellipsis {
                max-width: none;
                white-space: nowrap;
                overflow: visible;
                text-overflow: clip;
                display: block;
                vertical-align: baseline;
                cursor: auto;
                font-size: 14px;
                color: inherit;
            }
            .info-section-detail span{
                font-size: 16px;
                color: #6b7280;
                margin-bottom: 0px;
            }

        }
    </style>
@endsection
