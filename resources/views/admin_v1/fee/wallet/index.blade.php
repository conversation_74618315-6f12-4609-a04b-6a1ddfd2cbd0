@extends('layouts.admin_v1.main')
@push('styles')
@endpush
@section('title', 'Danh sách ví sinh viên')
@section('content')
<div class="kt-container--fluid  kt-grid__item kt-grid__item--fluid">
    <div class="kt-portlet kt-portlet--mobile mb-0">
        <div class="kt-portlet__body pb-0">
            <!-- Thống kê tổng quan -->
            <div class="row">
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="kt-portlet kt-portlet--height-fluid kt-portlet--border-bottom-primary px-3">
                        <div class="kt-widget26">
                            <div class="kt-widget26__number kt-font-primary">
                                <h5>
                                    {{ number_format($statistics['total_wallets'] ?? 0) }}
                                    <span>ví</span>
                                </h5>
                            </div>
                            <div class="kt-widget26__desc">
                                <span class="kt-widget26__subtitle">Tất cả ví trong hệ thống</span>
                            </div>
                            <div class="kt-widget26__icon">
                                <i class="flaticon-piggy-bank kt-font-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="kt-portlet kt-portlet--height-fluid kt-portlet--border-bottom-success px-3">
                        <div class="kt-widget26">
                            <div class="kt-widget26__number kt-font-success">
                                <h5>
                                    {{ number_format($statistics['active_wallets'] ?? 0) }}
                                    <span>ví</span>
                                </h5>
                            </div>
                            <div class="kt-widget26__desc">
                                <span class="kt-widget26__subtitle">Đang hoạt động</span>
                            </div>
                            <div class="kt-widget26__icon">
                                <i class="flaticon2-checkmark kt-font-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="kt-portlet kt-portlet--height-fluid kt-portlet--border-bottom-danger px-3">
                        <div class="kt-widget26">
                            <div class="kt-widget26__number kt-font-danger">
                                <h5>
                                    {{ number_format($statistics['locked_wallets'] ?? 0) }}
                                    <span>ví</span>
                                </h5>
                            </div>
                            <div class="kt-widget26__desc">
                                <span class="kt-widget26__subtitle">Bị khóa tạm thời</span>
                            </div>
                            <div class="kt-widget26__icon">
                                <i class="flaticon2-lock kt-font-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="kt-portlet kt-portlet--height-fluid kt-portlet--border-bottom-warning px-3">
                        <div class="kt-widget26">
                            <div class="kt-widget26__number kt-font-warning">
                                <h5>
                                    {{ number_format($statistics['total_balance'] ?? 0) }}
                                    <span>VNĐ</span>
                                </h5>
                            </div>
                            <div class="kt-widget26__desc">
                                <span class="kt-widget26__subtitle">Tổng tiền trong tất cả các ví</span>
                            </div>
                            <div class="kt-widget26__icon">
                                <i class="flaticon-coins kt-font-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="kt-portlet kt-portlet--mobile">
        <div class="kt-portlet__head kt-portlet__head--lg pb-3">
            <form method="GET" action="{{ route('admin.admin.wallet.list') }}" class="w-100">
                <div class="row align-items-center">
                    <div class="col-md-2 kt-margin-b-20-tablet-and-mobile">
                        <div class="kt-input-icon kt-input-icon--left">
                            <input type="text" class="form-control" placeholder="Tìm kiếm mã SV, tài khoản..." name="keyword" value="{{ $keyword ?? '' }}">
                            <span class="kt-input-icon__icon kt-input-icon__icon--left">
                                <span><i class="la la-search"></i></span>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2 kt-margin-b-20-tablet-and-mobile">
                        <select class="form-control" name="status">
                            <option value="">Tất cả trạng thái</option>
                            <option value="0" {{ ($status ?? '') === '0' ? 'selected' : '' }}>Hoạt động</option>
                            <option value="1" {{ ($status ?? '') === '1' ? 'selected' : '' }}>Đã khóa</option>
                        </select>
                    </div>

                    <div class="col-md-2 kt-margin-b-20-tablet-and-mobile">
                        <input type="number" class="form-control" placeholder="Số dư từ" name="balance_from" value="{{ $balance_from ?? '' }}" min="0">
                    </div>
                    <div class="col-md-2 kt-margin-b-20-tablet-and-mobile">
                        <input type="number" class="form-control" placeholder="Số dư đến" name="balance_to" value="{{ $balance_to ?? '' }}" min="0">
                    </div>
                    <div class="col-md-2 kt-margin-b-20-tablet-and-mobile">
                        <input type="date" class="form-control" name="date_from" value="{{ $date_from ?? '' }}" placeholder="Từ ngày" id="date_from">
                    </div>
                    <div class="col-md-2 kt-margin-b-20-tablet-and-mobile">
                        <input type="date" class="form-control" name="date_to" value="{{ $date_to ?? '' }}" placeholder="Đến ngày" id="date_to">
                    </div>
                </div>
                <div class="row align-items-center kt-margin-t-10">
                    <div class="col-md-2 kt-margin-b-20-tablet-and-mobile">
                        <button type="submit" class="btn btn-success btn-elevate w-100">
                            <i class="la la-search"></i>
                        </button>
                    </div>
                    <div class="col-md-2 kt-margin-b-20-tablet-and-mobile">
                        <a href="{{ route('admin.admin.wallet.list') }}" class="btn btn-clean btn-elevate">
                            <i class="la la-refresh"></i>
                            Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
        <div class="kt-portlet__body" style="min-height: 500px">
            <div class="d-flex mb-3">
                <h4>
                    <span class="kt-portlet__head-icon">
                        <i class="flaticon2-list-3"></i>
                    </span>
                    Danh sách ví sinh viên
                </h4>
            </div>

            <div class="kt-form kt-form--label-right kt-margin-b-10 d-flex justify-content-between">
                <div class="kt-form__group">
                    <div class="kt-form__control">
                        <button type="button" class="btn btn-warning" onclick="bulkLockWallets()">
                            <i class="la la-lock"></i> Khóa ví đã chọn
                        </button>
                        <button type="button" class="btn btn-success" onclick="bulkUnlockWallets()">
                            <i class="la la-unlock"></i> Mở khóa ví đã chọn
                        </button>
                    </div>
                </div>
                <div class="kt-form__group">
                    <div class="kt-form__control d-flex">
                        <div class="dropdown mr-1">
                            <button class="btn btn-primary btn-elevate btn-icon-sm dropdown-toggle" type="button" data-toggle="dropdown">
                                <i class="la la-upload"></i>
                                Import
                            </button>
                            <div class="dropdown-menu dropdown-menu-right">
                                <h6 class="dropdown-header">Chọn loại import</h6>
                                <a class="dropdown-item" href="{{ route('admin.admin.wallet.import.wallets.form') }}">
                                    <i class="la la-plus kt-font-success"></i> Import tạo ví
                                </a>
                                <a class="dropdown-item" href="{{ route('admin.admin.wallet.import.deposits.form') }}">
                                    <i class="la la-money kt-font-warning"></i> Import nạp tiền
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ route('admin.admin.wallet.import.template', ['type' => 'create']) }}">
                                    <i class="la la-download kt-font-info"></i> Tải template tạo ví
                                </a>
                                <a class="dropdown-item" href="{{ route('admin.admin.wallet.import.template', ['type' => 'deposit']) }}">
                                    <i class="la la-download kt-font-info"></i> Tải template nạp tiền
                                </a>
                            </div>
                        </div>
                        <a href="{{ route('admin.admin.wallet.export') }}" class="btn btn-info btn-elevate btn-icon-sm mr-1" data-toggle="tooltip" title="Xuất danh sách ví ra Excel">
                            <i class="la la-download"></i>
                            Xuất Excel
                        </a>
                        <a href="{{ route('admin.admin.wallet.create.form') }}" class="btn btn-success btn-elevate btn-icon-sm" data-toggle="tooltip" title="Tạo ví mới cho sinh viên">
                            <i class="la la-plus"></i>
                            Tạo ví mới
                        </a>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th width="50" class="text-center">#</th>
                            <th>Mã sinh viên</th>
                            <th>Tài khoản</th>
                            <th>Họ & tên</th>
                            <th>Số dư ví</th>
                            <th>Trạng thái</th>
                            <th>Ghi chú</th>
                            <th>Ngày tạo</th>
                            <th></th>
                        </tr>
                    </thead>
                    @if(isset($wallets) && $wallets->count() > 0)
                        <tbody>
                            @foreach($wallets as $index => $wallet)
                            <tr >
                                <td class="text-center">{{ $wallets->firstItem() + $index }}</td>
                                <td><strong>{{ $wallet->user_code }}</strong></td>
                                <td>{{ $wallet->user_login }}</td>
                                <td>{{ $wallet->student_name }}</td>
                                <td>
                                    <span class="text-success font-weight-bold">
                                        {{ number_format($wallet->balance) }} VNĐ
                                    </span>
                                </td>
                                <td>
                                    @if($wallet->is_locked)
                                        <span class="text-danger"><i class="fa-solid fa-lock"></i> Đã khóa</span>
                                        {{-- @if($wallet->lock_reason)
                                            <br><small class="text-muted">{{ Str::limit($wallet->lock_reason, 30) }}</small>
                                        @endif --}}
                                    @else
                                        <span class="text-success"><i class="fa-solid fa-unlock"></i> Hoạt động</span>
                                    @endif
                                </td>
                                <td>{{$wallet->lock_reason}}</td>
                                <td>{{ $wallet->created_at->format('d/m/Y') }}</td>
                                <td class="text-right">
                                    <form action="{{ route('admin.admin.wallet.unlock', $wallet->id) }}" method="POST">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdown{{ $wallet->id }}" data-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdown{{ $wallet->id }}">
                                                @csrf
                                                <a class="dropdown-item" href="{{ route('admin.admin.wallet.deposit.form', $wallet->id) }}" title="Nạp tiền">
                                                    <i class="la la-plus"></i> Nạp tiền
                                                </a>
                                                <a class="dropdown-item" href="{{ route('admin.admin.wallet.transactions', $wallet->id) }}" title="Lịch sử">
                                                    <i class="la la-history"></i> Lịch sử
                                                </a>
                                                @if($wallet->is_locked)
                                                    <button type="submit" class="dropdown-item btn btn-sm btn-warning" title="Mở khóa ví"
                                                            onclick="return confirm('Bạn có chắc muốn mở khóa ví này?')">
                                                        <i class="fa-solid fa-unlock"></i> <span class="text-danger">Mở khóa</span>
                                                    </button>
                                                @else
                                                    <a class="dropdown-item" href="{{ route('admin.admin.wallet.lock.form', $wallet->id) }}" title="Khóa ví">
                                                        <i class="fa-solid fa-lock"></i> <span class="text-danger">Khóa</span>
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </form>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                        @else
                            <div class="text-center py-5">
                                <i class="flaticon2-wallet text-muted" style="font-size: 64px;"></i>
                                <h4 class="mt-3">Chưa có ví nào</h4>
                                <p class="text-muted">Hãy tạo ví đầu tiên cho sinh viên</p>
                                <a href="{{ route('admin.admin.wallet.create.form') }}" class="btn btn-primary">
                                    <i class="la la-plus"></i> Tạo ví mới
                                </a>
                            </div>
                        @endif
                    </table>
                </div>
            <!-- Pagination -->
            @if(isset($wallets) && $wallets->count() > 0)
            <div class="kt-pagination kt-pagination--brand">
                <div class="kt-pagination__toolbar">
                    <span class="pagination__desc">
                        Hiển thị {{ $wallets->firstItem() }} - {{ $wallets->lastItem() }} của {{ $wallets->total() }} bản ghi
                    </span>
                </div>
                {{ $wallets->appends(request()->except('page'))->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Bulk Lock Modal -->
<div class="modal fade" id="bulkLockModal" tabindex="-1" role="dialog" aria-labelledby="bulkLockModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkLockModalLabel">Khóa ví hàng loạt</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="bulkLockForm" method="POST" action="{{ route('admin.admin.wallet.bulk.lock') }}">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="lock_reason">Lý do khóa <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="lock_reason" name="reason" rows="3" placeholder="Nhập lý do khóa ví" required></textarea>
                    </div>
                    <div class="alert alert-info">
                        <strong>Lưu ý:</strong> Bạn đã chọn <span id="selected-count">0</span> ví để khóa.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-danger">Khóa ví</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Select all checkbox functionality
        $('#select-all').change(function() {
            $('.wallet-checkbox').prop('checked', this.checked);
        });

        // Individual checkbox change
        $('.wallet-checkbox').change(function() {
            if (!this.checked) {
                $('#select-all').prop('checked', false);
            } else {
                if ($('.wallet-checkbox:checked').length === $('.wallet-checkbox').length) {
                    $('#select-all').prop('checked', true);
                }
            }
        });
        $('.unlock_wallet').click(function() {
            var walletId = $(this).data('wallet-id');
            var url = $(this).data('url');

            if (confirm('Bạn có chắc muốn mở khóa ví này?')) {
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Khóa ví thành công!');
                            location.reload();
                        } else {
                            alert('Lỗi khi khóa ví: ' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('Lỗi khi khóa ví: ' + error);
                    }
                });
            }
        });
    });

    function getSelectedWallets() {
        var selected = [];
        $('.wallet-checkbox:checked').each(function() {
            selected.push($(this).val());
        });
        return selected;
    }

    function bulkLockWallets() {
        var selected = getSelectedWallets();
        if (selected.length === 0) {
            alert('Vui lòng chọn ít nhất một ví để khóa.');
            return;
        }

        $('#selected-count').text(selected.length);
        $('#bulkLockModal').modal('show');
    }

    function bulkUnlockWallets() {
        var selected = getSelectedWallets();
        if (selected.length === 0) {
            alert('Vui lòng chọn ít nhất một ví để mở khóa.');
            return;
        }

        if (confirm('Bạn có chắc muốn mở khóa ' + selected.length + ' ví đã chọn?')) {
            var form = $('<form>', {
                'method': 'POST',
                'action': '{{ route("admin.admin.wallet.bulk.unlock") }}'
            });

            form.append($('<input>', {
                'type': 'hidden',
                'name': '_token',
                'value': '{{ csrf_token() }}'
            }));

            selected.forEach(function(id) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'wallet_ids[]',
                    'value': id
                }));
            });

            $('body').append(form);
            form.submit();
        }
    }

    $('#bulkLockForm').submit(function() {
        var selected = getSelectedWallets();
        selected.forEach(function(id) {
            $(this).append($('<input>', {
                'type': 'hidden',
                'name': 'wallet_ids[]',
                'value': id
            }));
        }.bind(this));
    });
</script>
@endsection
