<!DOCTYPE html>
<html>
<head>
	<title>{{ config('app.name') }}</title>
	<link rel="stylesheet" type="text/css" href="{{asset('css/style_login.css')}}">
	<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">
	<link rel="shortcut icon" href="{{ config('app.favicon_path') }}" />
</head>
<body>
<div class="container">
	<div class="form sign-in-container" id="form_CD" >
		<form action="#" id="forgot-password">
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" style="width: 100px">
				<path d="M336 352c97.2 0 176-78.8 176-176S433.2 0 336 0S160 78.8 160 176c0 18.7 2.9 36.8 8.3 53.7L7 391c-4.5 4.5-7 10.6-7 17l0 80c0 13.3 10.7 24 24 24l80 0c13.3 0 24-10.7 24-24l0-40 40 0c13.3 0 24-10.7 24-24l0-40 40 0c6.4 0 12.5-2.5 17-7l33.3-33.3c16.9 5.4 35 8.3 53.7 8.3zM376 96a40 40 0 1 1 0 80 40 40 0 1 1 0-80z"/>
			</svg>
			<h3>Bạn quên mật khẩu?</h3>
			<span>Vui lòng nhập email của bạn tại đây.</span>
            <div class="form-input">
				<div class="form-control">
					<input type="email" placeholder="<EMAIL>">

				</div>
                <div class="social-container margin-top-10">
                    <button type="submit" disabled>Đặt lại mật khẩu</button>
                </div>
            </div>
		</form>
	</div>
	<div class="overlay-container switch-container" id="overlay-container">
		<div class="overlay">
			<div class="overlay-panel overlay-right">
				<span class="font-weight-bold font-size-15 margin-bottom-10">Chào mừng đến với hệ thống quản lý đào tạo</span>
				<h1 id="campus" class="font-size-40">{{ env('CAMPUS_NAME') }}</h1>
				<img src="{{asset('images/people-education-icon-isolated.png')}}" style="width: 300px; height: 300px;" alt="">
				<button class="signup_btn hidden" onclick="switchContainer()">Trở lại</button>
			</div>
		</div>
	</div>
	<div class="form sign-in-container overlay-container" id="form_PTCD">
		<form id="form-input" action="{{route('login.password')}}" method="post">
			@csrf
			<h1>Đăng nhập</h1>
            <div class="form-input">
                <input type="text" name="user_login" placeholder="Email hoặc tên đăng nhập">
                <input type="password" name="user_password" placeholder="Mật khẩu">
				@if (session()->has('message'))
					<div class="message-box">
						<small class="text-danger"> {{ session('message') }}</small>
					</div>
				@endif
				<div class="remember-forgot-container">
					<div class="remember-me">
						<input type="checkbox" id="remember" name="remember">
						<label for="remember"><small>Ghi nhớ đăng nhập</small></label>
					</div>
					<div class="forgot-password">
						<a onclick="switchContainer()">
							<i>Quên mật khẩu?</i>
						</a>
					</div>
				</div>
                <div class="remember-forgot-container">
					<button type="submit">Đăng nhập</button>
                </div>
				<div class="slad-space" id="slad_space">
					<hr class="slad" id="hr-left">
					<span class="middle-slad">OR</span>
					<hr class="slad" id="hr-right">
				</div>
                <div class="social-container">
                    <a href="{{route('login.google')}}"><i class="fab fa-google-plus-g google--icon"></i></a>
                </div>
            </div>
		</form>
	</div>
</div>
<script>
	let position = 0;
	function switchContainer(){
		if(position === 0){
			document.querySelector('.switch-container').style.transform ="translate3d(0%,0,0)";
			document.querySelector('.signup_btn').classList.remove('hidden');
			position = 1;
		}else{
			document.querySelector('.switch-container').style.transform ="translate3d(-100%,0,0)";
			document.querySelector('.signup_btn').classList.add('hidden');
			position = 0;
		}
	}
	function selected(campus_code, campus_name){
		let selected = document.querySelectorAll('.selected');
		[].forEach.call(selected, function(el) {
				el.classList.remove("selected");
			});
		document.getElementById(campus_code).classList.add('selected');
		document.querySelector('.title').innerHTML = "Cơ sở: "+campus_name;
	}
</script>

{{-- ChatMe Widget --}}
<x-chatme />

</body>
</html>
