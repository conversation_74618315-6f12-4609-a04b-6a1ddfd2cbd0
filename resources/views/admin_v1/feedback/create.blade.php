@extends('layouts.admin_v1.main')
@section('title', 'Tạo phiếu khảo sát')
@section('content')
    <div class="p-0" id="main-content">
        <div class="top-bar">
            <h3 class="mb-0"><b>TẠO PHIẾU KHẢO SÁT</b></h3>
        </div>

        <div class="content-area">
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endif
            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span>Đ<PERSON> xảy ra lỗi trong quá trình tạo phiếu khảo sát. Vui lòng kiểm tra lại các trường thông tin.</span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endif
            @if ($errors->any())

                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span>{{ $errors->first() }}</span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            @endif
            <form method="POST" action="{{ route('admin.feedback.store') }}" >
                @csrf
                <div class="evaluation-form-card">
                    <h3 class="mb-4"><i class="fas fa-plus-circle text-primary me-2"></i> Thông tin phiếu đánh giá</h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="evaluationName">Tên <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="evaluationName" name="name" placeholder="VD: Đánh giá giảng viên kỳ 1 năm 2024">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="term">Kỳ học <span class="text-danger">*</span></label>
                                <select class="form-control" id="term" name="term">
                                    <option value="">Chọn kỳ học</option>
                                    @foreach ($terms as $term)
                                        <option value="{{ $term->id }}">{{ $term->term_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="startDate">Ngày bắt đầu <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="startDate" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="endDate">Ngày kết thúc <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="endDate" name="end_date">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="description">Mô tả</label>
                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="Mô tả về mục đích và nội dung đánh giá..."></textarea>
                    </div>
                </div>
                <div class="evaluation-form-card">
                    <h3 class="mb-4"><i class="fas fa-chalkboard text-primary me-2"></i> Chọn lớp học áp dụng</h3>
                    <div class="class-selection">
                        <div class="form-group">
                            <div class="d-flex align-items-center mb-3">
                                <input type="checkbox" id="selectAllClasses" class="class-checkbox">
                                <label for="selectAllClasses" class="mb-0 font-weight-bold">Chọn tất cả lớp học</label>
                            </div>
                        </div>
                        <div class="row" id="classListContainer">
                        </div>
                    </div>
                </div>
                <div class="question-bank-card">
                    <div class="question-bank-header">
                        <h3 class="mb-0"><i class="fas fa-question-circle text-primary me-2"></i> Chọn tiêu chí đánh giá</h3>
                        <i class="text-muted">Chọn các câu hỏi từ ngân hàng câu hỏi để tạo bộ đánh giá</i>
                    </div>
                    <div class="p-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center question-item">
                                <input type="checkbox" id="selectAllQuestions" class="question-checkbox">
                                <label for="selectAllQuestions" class="mb-0 font-weight-bold">Chọn tất cả câu hỏi</label>
                            </div>
                            <div class="selected-questions">
                                <span class="selected-count">0 câu hỏi đã chọn</span>
                            </div>
                        </div>
                        @foreach ($criterias as $criteria)
                            <div class="question-item">
                                <input type="checkbox" class="question-checkbox" name="selectedQuestions[]" value="{{ $criteria->id }}">
                                <div class="question-text">
                                    <b>{{ $criteria->name }}</b> <br>
                                    <span class="text-mute">{{ $criteria->description }}</span>
                                </div>
                                <span class="question-type">Trọng số: {{ $criteria->weight }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="evaluation-preview">
                    <h4 class="mb-3"><i class="fas fa-eye text-warning me-2"></i> Xem trước đánh giá</span></h4>
                    <p class="mb-2"><strong>Tên đánh giá:</strong> <span id="previewName">Chưa nhập</span></p>
                    <p class="mb-2"><strong>Kỳ học:</strong> <span id="previewSemester">Chưa chọn</span></p>
                    <p class="mb-2"><strong>Số lớp áp dụng:</strong> <span id="previewClasses">0 lớp</span></p>
                    <p class="mb-3"><strong>Số câu hỏi:</strong> <span id="previewQuestions">0 câu hỏi</span></p>
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-outline-primary mr-3" id="openModalPreview">
                            <i class="fas fa-eye me-2"></i>Xem trước chi tiết
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Tạo đánh giá
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-labelledby="modalCriteriaLabel" aria-hidden="true" id="modalPreview">
            <div class="modal-dialog modal-lg modal-dialog-scrollable">
                <div class="modal-content">
                    <form>
                        <div class="modal-header">
                            <h5 class="modal-title font-weight-bold" id="modalCriteriaLabel">Xem trước phiếu khảo sát</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Đóng">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
        
                        <div class="modal-body" id="modalCriteriaContainer">
                            <form>
                                <div id="criteriaList">
                                    // Các tiêu chí sẽ được render ở đây
                                </div>
                                <!-- Comment Section -->
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-1">Nhận xét và đề xuất</h5>
                                        <small class="text-muted">Chia sẻ ý kiến của bạn về giảng viên (không bắt buộc)</small>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="comment">Nhận xét chung</label>
                                            <textarea name="comment" id="comment" class="form-control" rows="4" placeholder="Chia sẻ nhận xét của bạn về giảng viên..."></textarea>
                                        </div>
                            
                                        <div class="form-group">
                                            <label for="suggestions">Đề xuất cải thiện</label>
                                            <textarea name="suggestions" id="suggestions" class="form-control" rows="3" placeholder="Đề xuất những điểm cần cải thiện..."></textarea>
                                        </div>
                            
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input ml-0" id="is_anonymous" name="is_anonymous" value="1">
                                            <label class="form-check-label ml-4" for="is_anonymous">Đánh giá ẩn danh (khuyến nghị)</label>
                                        </div>
                                    </div>
                                </div>
                            
                                <!-- Submit Section -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <div class="alert alert-warning small" role="alert">
                                            <i class="fas fa-info-circle mr-2"></i>
                                            <strong>Lưu ý quan trọng:</strong><br>
                                            <ul class="mb-0 pl-3">
                                                <li>Sau khi gửi đánh giá, bạn không thể chỉnh sửa lại</li>
                                                <li>Đánh giá của bạn sẽ được bảo mật và chỉ dùng để cải thiện chất lượng giảng dạy</li>
                                                <li>Vui lòng đánh giá một cách khách quan và công bằng</li>
                                            </ul>
                                        </div>

                                        <div class="d-flex flex-column flex-sm-row justify-content-sm-end mb-3">
                                            <button type="button" name="draft" class="btn btn-secondary mr-sm-2 mb-2 mb-sm-0">
                                                <i class="fas fa-save mr-1"></i> Lưu nháp
                                            </button>
                                            <button type="button" name="submit" class="btn btn-primary">
                                                <i class="fas fa-paper-plane mr-1"></i> Gửi đánh giá
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('js')
    <script>
        const allCriterias = @json($criterias);
        document.getElementById('selectAllClasses').addEventListener('change', function() {
            const classCheckboxes = document.querySelectorAll('input[name="selectedClasses[]"]');
            classCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updatePreview();
        });
        document.getElementById('term').addEventListener('change', function() {
            getListGroupByTerm();
        });
        document.getElementById('selectAllQuestions').addEventListener('change', function() {
            const questionCheckboxes = document.querySelectorAll('input[name="selectedQuestions[]"]');
            questionCheckboxes.forEach(checkbox => { checkbox.checked = this.checked;});
            updateSelectedCount();
            updatePreview();
        });

        // Cập nhật số lượng câu hỏi đã chọn
        function updateSelectedCount() {
            const selectedQuestions = document.querySelectorAll('input[name="selectedQuestions[]"]:checked');
            document.querySelector('.selected-count').textContent = `${selectedQuestions.length} câu hỏi đã chọn`;
        }

        // Cập nhật preview
        function updatePreview() {
            const evaluationName = document.getElementById('evaluationName').value || 'Chưa nhập';
            const selectedClasses = document.querySelectorAll('input[name="selectedClasses[]"]:checked').length;
            const selectedQuestions = document.querySelectorAll('input[name="selectedQuestions[]"]:checked').length;

            document.getElementById('previewName').textContent = evaluationName;
            document.getElementById('previewSemester').textContent = document.getElementById('term').selectedOptions[0]?.text || 'Chưa chọn';
            document.getElementById('previewClasses').textContent = `${selectedClasses} lớp`;
            document.getElementById('previewQuestions').textContent = `${selectedQuestions} câu hỏi`;
        }

        // Hàm lấy danh sách lớp học theo kỳ
        function getListGroupByTerm() {
            const termId = document.getElementById('term').value;

            $.ajax({
                type: 'GET',
                url: '{{ route("api.v1.feedback.group_by_term") }}',
                data: {
                    term_id: termId,
                    key_word: '',
                    limit: 20,
                    not_student: true
                },
                success: function(response) {
                    const classList = response.data;
                    if (classList.length === 0) {
                        updateClassList([]);
                        return;
                    }
                    updateClassList(classList);
                },
                error: function(xhr, status, error) {
                    console.error('Lỗi khi gọi API:', error);
                }
            });
        }

        function updateClassList(classList) {
            const container = document.getElementById('classListContainer');
            container.innerHTML = ''; // Xoá danh sách cũ

            if (classList.length > 0) {
                classList.forEach((cls, index) => {
                    const classHtml = `
                        <div class="col-md-6">
                            <div class="class-item">
                                <input type="checkbox" id="class${index}" class="class-checkbox" name="selectedClasses[]" value="${cls.group_id}">
                                <label for="class${index}" class="mb-0">
                                    <strong>${cls.group_name}</strong> - ${cls.total_member} sinh viên<br>
                                    <small class="text-muted">${cls.psubject_name} (${cls.psubject_code})</small>
                                </label>
                            </div>
                        </div>
                    `;
                    container.insertAdjacentHTML('beforeend', classHtml);
                });

                // Gắn lại sự kiện update preview khi checkbox thay đổi
                document.querySelectorAll('input[name="selectedClasses[]"]').forEach(cb => {
                    cb.addEventListener('change', updatePreview);
                });

                updatePreview();
            } else {
                container.innerHTML = '<div class="col-12 text-muted">Không có lớp học nào cho kỳ này.</div>';
            }
        }

        // Lắng nghe thay đổi trên các form elements
        document.getElementById('evaluationName').addEventListener('input', updatePreview);

        // Lắng nghe thay đổi trên các checkbox
        document.querySelectorAll('input[name="selectedClasses[]"]').forEach(checkbox => {
            checkbox.addEventListener('change', updatePreview);
        });

        document.querySelectorAll('input[name="selectedQuestions"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateSelectedCount();
                updatePreview();
            });
        });

        function renderSelectedCriteriaInModal() {
            const selectedIds = Array.from(document.querySelectorAll('input[name="selectedQuestions"]:checked')).map(cb => parseInt(cb.value));

            if (selectedIds.length === 0) {
                alert("Vui lòng chọn ít nhất một tiêu chí đánh giá.");
                return;
            }
            // Lấy danh sách tiêu chí đã chọn
            let selectCriterias = allCriterias.filter(criteria => selectedIds.includes(criteria.id));
            console.log('selectCriterias', selectCriterias);
            
            
            const container = document.getElementById('criteriaList');
            container.innerHTML = ''; // clear

            selectCriterias.forEach(criteria => {
                let block = `
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <strong>${ criteria.name }</strong>
                            <span class="badge badge-primary ml-2">Trọng số: ${ criteria.weight }</span><br>
                            <div class="text-muted small mt-1">${ criteria.description }}</div>
                        </div>
                        <div class="card-body">
                            <!-- Rating scale -->
                            <div class="d-flex align-items-center mt-4">
                                <div class="mr-5">
                                    <span class="mr-2 small font-weight-bold">Điểm đánh giá:</span><br>
                                    <small class="text-muted">Đánh giá từ 1 (Rất kém) đến 5 (Rất tốt)</small>
                                </div>
                                <div class="score-options d-flex">
                                    <label class="score-label">
                                        <input type="radio" name="score" value="1">
                                        <span>1</span>
                                    </label>
                                    <label class="score-label">
                                        <input type="radio" name="score" value="2">
                                        <span>2</span>
                                    </label>
                                    <label class="score-label">
                                        <input type="radio" name="score" value="3">
                                        <span>3</span>
                                    </label>
                                    <label class="score-label">
                                        <input type="radio" name="score" value="4">
                                        <span>4</span>
                                    </label>
                                    <label class="score-label">
                                        <input type="radio" name="score" value="5">
                                        <span>5</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.insertAdjacentHTML('beforeend', block);
            });
        }

        // Gọi khi mở modal
        $('#openModalPreview').on('click', function () {
            renderSelectedCriteriaInModal();
            $('#modalPreview').modal('show');
        });

        // Hoặc nếu dùng nút có data-toggle
        $('[data-toggle="modal"]').on('click', function () {
            renderSelectedCriteriaInModal();
        });

        // Khởi tạo
        updateSelectedCount();
        updatePreview();
    </script>
@endsection

@section('css')
    <style>
        :root {
            --primary-color: #5865F2;
            --secondary-color: #4752C4;
            --light-bg: #F8F9FA;
            --border-color: #E9ECEF;
        }

        span {
            font-size: 14px;
        }

        label {
            font-size: 15px !important;
        }

        .top-bar {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            padding: 15px 30px;
            margin: 0px 30px;
            justify-content: between;
            align-items: center;
        }

        .content-area {
            padding: 30px;
        }

        .evaluation-form-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-group label {
            font-weight: 600;
            color: #2C3E50;
            margin-bottom: 8px;
        }

        .form-control {
            border: 2px solid #E9ECEF;
            border-radius: 8px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(88, 101, 242, 0.25);
        }

        .question-bank-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .question-bank-header {
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            padding: 20px;
            border-radius: 12px 12px 0 0;
            border-bottom: 1px solid var(--border-color);
        }

        .question-item {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .question-item:hover {
            background-color: #F8F9FA;
        }

        .question-item:last-child {
            border-bottom: none;
        }

        .class-checkbox, .question-checkbox {
            margin-right: 15px;
            transform: scale(1.2);
        }

        .question-text {
            flex: 1;
            color: #2C3E50;
            font-weight: 500;
            font-size: 14px;
        }

        .question-type {
            background: var(--primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px !important;
            font-weight: 600;
            margin-left: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(88, 101, 242, 0.3);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 8px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
        }

        .selected-questions {
            background: #E8F5E8;
            border: 2px solid #28A745;
            border-radius: 8px;
            padding: 10px;
            margin-top: 15px;
        }

        .selected-count {
            color: #28A745;
            font-weight: 600;
        }

        #classListContainer {
            max-height: 300px; 
            overflow-y: auto; 
            padding-right: 8px;
        }

        .class-selection {
            background: #F8F9FA;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .class-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .class-checkbox {
            margin-right: 12px;
            transform: scale(1.1);
        }

        .evaluation-preview {
            background: #FFF9E6;
            border: 2px solid #FFC107;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .score-options .score-label {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            margin-right: 8px;
            border: 2px solid #ccc;
            border-radius: 50%;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .score-options .score-label input[type="radio"] {
            opacity: 0;
            position: absolute;
            pointer-events: none;
        }

        .score-options .score-label input[type="radio"]:checked + span {
            background-color: #007bff;
            color: #fff;
            border-color: #007bff;
        }

        .score-options .score-label span {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }
        #modalCriteriaContainer {
            max-height: 500px; 
            overflow-y: auto; 
            padding-right: 8px;
        }
        #modalCriteriaContainer .alert {
            display: block;
        }
    </style>    
@endsection