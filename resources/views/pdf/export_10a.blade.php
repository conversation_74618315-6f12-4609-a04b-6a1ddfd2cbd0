<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Bảng điểm ({{$groups->implode('group_name', ', ')}})</title>
    <style>
        /**{padding:0;margin:0;text-decoration: none;}*/
        body {
            font-family: "Times New Roman", sans-serif;
            font-size: 8px;
        }
        @page {
            header: page-header;
            footer: page-footer;
            margin: 30px 40px 40px 40px;

        }
        .italic {
            font-style: italic;
        }
        .bold {
            font-weight: bold;
        }
        table {
            border-collapse: collapse;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            position: fixed;
            left: 0;
            bottom: 0;
            width: 100%;
            color: white;
        }
    </style>
</head>
<body>
@foreach($groups as $group)

<table style="font-weight: bold;margin-left:20px;">
    <tr>
        <td rowspan="5"><img style="" src="{{ public_path('images/logo.png') }}" alt="" width="170"></td>
        <td>
            <h2 style="">Sổ Điểm</h2>
        </td>
    </tr>
    <tr>
        <td>Học phần</td>
        <td>{{$group->psubject_name}} ({{$group->psubject_code}})</td>
    </tr>
    <tr>
        <td>Lớp</td>
        <td>{{$group->group_name}}</td>
    </tr>
    <tr>
        <td>Thời gian</td>
        <td>{{$group->start_date}} - {{$group->end_date}}</td>
    </tr>
    <tr>
        <td>Học kỳ</td>
        <td>{{$group->pterm_name}} - {{$group->block_name}}</td>
    </tr>
</table>
<table border="1" style="margin: auto">
    <tr>
        <th>#</th>
        <th>Mã sinh viên</th>
        <th>Họ và tên</th>
        @foreach($grades as $grade)
            <!-- text-rotate="45" -->
            <th style="padding: 2px 2px;" class="text-center">{{$grade['grade_name']}}<br>({{$grade['grade_weight']}}%)</th>
        @endforeach
        @if(!$display)
        <th>Điểm tổng kết</th>
        <th>Trạng thái</th>
        @endif
    </tr>
    @foreach ($group->groupMembers->sortBy('user_code') as $member)
        <tr>
            <td class="text-center">{{$loop->iteration}}</td>
            <td>{{$member->user_code}}</td>
            <td>{{$member->full_name}}</td>
            @foreach($grades as $key => $grade)
                <td class="text-center">{{$member->grades[$key]['point']}}</td>
            @endforeach
            @if(!$display)
            <td class="text-center">{{$member->result ? $member->result->grade : '-'}}</td>
            <td class="text-center">
                @if($member->result)
                    @if($member->result->val == 0)
                        Trượt
                    @elseif($member->result->val == -1)
                        Trượt điểm danh
                    @elseif($member->result->val == 1)
                        Đạt
                    @else
                        -
                    @endif
                @else
                    -
                @endif
            </td>
            @endif
        </tr>
    @endforeach
</table>
@if($group->groupMembers->count() > 40 && $group->groupMembers->count() < 41)
    <pagebreak></pagebreak>
@endif
<table width="100%" style="margin-top: 10px">
    <tr>
        <th>Chủ nhiệm bộ môn</th>
        <th>
            <span>Hà Nội, ngày {{ now()->format("d") }} tháng {{ now()->format("m") }} năm {{ now()->format("Y") }}</span><br>
            <span>Giảng viên phụ trách </span>
        </th>
    </tr>
    <tr>
        <td colspan="2"></td>
    </tr>
    <tr>
        <td colspan="2"></td>
    </tr>
    <tr>
        <td colspan="2"></td>
    </tr>
</table>

<footer class="footer" name="page-footer">
    <hr>
    <table width="100%">
        <tr>
            {{-- <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;04.7-BM/ĐT/HDCV/FE</td> --}}
            <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;07.03.01-BM/FPL/HDCV/FE</td>
            <td>Token: <?php echo $code[$group->id]->token ?? null; ?></td>
        </tr>
    </table>
</footer>
@if(!$loop->last)
    <pagebreak></pagebreak>
@endif
@endforeach
</body>


