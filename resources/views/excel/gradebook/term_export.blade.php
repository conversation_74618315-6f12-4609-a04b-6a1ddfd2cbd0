<head>
    <style>
        body {
            font-family: "Times New Roman", sans-serif;
            font-size: 10px;
        }

        .italic {
            font-style: italic;
        }

        .bold {
            font-weight: bold;
        }

        table {
            border-collapse: collapse;
        }

        .text-center {
            text-align: center;
        }

    </style>
</head>

<body>

    <table border="1" style="margin: auto">
        <tr>
            <th>#</th>
            <th>Mã sinh viên</th>
            <th>Họ và tên</th>
            <th>Lớp</th>
            @foreach ($groups[0]->grades as $grade)
                <th style="padding: 2px 2px;" class="text-center">
                    {{ $grade['grade_name'] }}<br>({{ $grade['weight'] }}%)
                </th>
            @endforeach
            <th>Điểm tổng kết</th>
            <th>Trạng thái</th>
        </tr>
        @foreach ($groups as $group)
            @foreach ($group->group_members as $member)
                <tr>
                    <td class="text-center">{{ $loop->iteration }}</td>
                    <td>{{ $member->user_code }}</td>
                    <td>{{ $member->full_name }}</td>
                    <td> {{ $group->group_name }} </td>
                    @foreach ($member->grades as $key => $grade)
                        <td class="text-center">
                            @if (isset($member->grades[$key]['point']))
                                {{ $member->grades[$key]['point'] }}
                            @else
                                0
                            @endif
                        </td>
                    @endforeach
                    <td class="text-center">
                        @if (isset($member->result->grade))
                            {{ $member->result->grade }}
                        @else
                            0
                        @endif
                    </td>
                    <td class="text-center">
                        {{ $member->result ? $member->result->status_subject : '-' }}
                    </td>
                </tr>
            @endforeach
        @endforeach
    </table>


</body>
