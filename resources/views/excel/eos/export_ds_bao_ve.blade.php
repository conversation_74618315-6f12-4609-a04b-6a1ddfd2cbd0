<table class="table table-striped">
    <tbody class="border: 1px solid #000000">
        {{-- <tr>
            <th rowspan="2" colspan="8" style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center">
                H<PERSON> thống điền sẵn
            </th>
            <th colspan="3" style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center">
                Kh<PERSON>o thí xếp
            </th>
        </tr>
        <tr>
            <th>
                Giảng viên xếp
            </th>
            <th colspan="2">
                CN Bộ môn xếp
            </th>
        </tr> --}}
        <tr>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center">
                STT
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center; color: red;">
                Tên đăng nhập
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center">
                MSSV
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center">
                Họ tên
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center; color: red;">
                Mã môn
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center; color: red;">
                Lớp
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center; color: red;">
                Buổi thi
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center">
                Phòng thi
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center">
                Giờ thi
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center;">
                Ngày thi
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center">
                Giám thị 1
            </th>
            <th style="font-family: 'Times New Roman'; font-weight: bold; font-size: 12px; text-align: center">
                Giám thị 2
            </th>
        </tr>
        @foreach($data as $member)
        <tr>
            <td style="font-family: 'Times New Roman'; font-size: 11px; text-align: center">
                {{ $loop->iteration }}
            </td>
            <td style="font-family: 'Times New Roman'; font-size: 11px;">
                {{ $member->member_login ?? "" }}
            </td>
            <td style="font-family: 'Times New Roman'; font-size: 11px;">
                {{ $member->user_code ?? "" }}
            </td>
            <td style="font-family: 'Times New Roman'; font-size: 11px;">
                {{ $member->user->fullname() ?? "" }}
            </td>
            <td style="font-family: 'Times New Roman'; font-size: 11px;">
                {{-- Mã môn --}}
                {{ $excel_info['bo_mon_info']->psubject_code ?? "" }}
            </td>
            <td style="font-family: 'Times New Roman'; font-size: 11px;">
                {{-- Lớp --}}
                {{ $member->group_name ?? "" }}
            </td>
            <td style="font-family: 'Times New Roman'; font-size: 11px;">
                {{-- Buổi thi : 1-2-3 --}}
                {{ $member->course_session ?? "" }}
            </td>
            @if($member->course_session > 0 && $member->slot_graduate != null)
                <td style="font-family: 'Times New Roman'; font-size: 11px; text-align: center">
                    {{-- Phòng thi --}}
                    {{ $member->slot_graduate->room_name ?? "" }} {{ $member->slot_graduate->url_room_online ?? "" }}
                </td>
                <td style="font-family: 'Times New Roman'; font-size: 11px; text-align: center">
                    @if($member->slot_graduate->slotDetail != null)
                        {{-- Giờ thi --}}
                        {{ $member->slot_graduate->slotDetail->slot_start ?? "" }} đến {{ $member->slot_graduate->slotDetail->slot_end ?? "" }}
                    @endif
                </td>
                <td style="font-family: 'Times New Roman'; font-size: 11px; text-align: center">
                    @php
                        $slot_date = str_replace('/', '-', $member->slot_graduate->day);
                        $new_date = date("d-m-Y", strtotime($slot_date)) ?? "";
                    @endphp
                    {{-- Ngày thi --}}
                    {{ $new_date }}
                </td>
            @else
                <td style="font-family: 'Times New Roman'; font-size: 11px; text-align: center">
                    {{-- Phòng thi --}}
                </td>
                <td style="font-family: 'Times New Roman'; font-size: 11px; text-align: center">
                    {{-- Giờ thi --}}
                </td>
                <td style="font-family: 'Times New Roman'; font-size: 11px; text-align: center">
                    {{-- Ngày thi --}}
                </td>
            @endif

            <td style="font-family: 'Times New Roman'; font-size: 11px; text-align: center">
                {{-- Giám thị 1 --}}
                {{-- CN Bộ môn || Khảo thí --}}
                {{-- {{ $excel_info['bo_mon_info']->leader_login }} --}}
                @if($member->course_session > 0)
                    {{ $member->slot_graduate->leader_login ?? "" }}
                @else
                    {{ $excel_info['bo_mon_info']->leader_login ?? "" }}
                @endif
            </td>
            <td style="font-family: 'Times New Roman'; font-size: 11px; text-align: center">
                {{-- Giám thị 2 --}}
                @if($member->course_session > 0)
                    {{ $member->slot_graduate->leader_login2 ?? "" }}
                @endif
            </td>
        </tr>
        @endforeach
    </tbody>
</table>
