<template>
    <form class="p-3">
        <div class="form-outline mb-4" v-if="formData.save_file">
            <label for="category_id"><PERSON><PERSON><PERSON> tin nhắn</label>
            <select class="form-control" name="category_id" id="category_id" v-model="formData.sms_category" 
            v-if="formData.student_type == 1">
                <option :value="null" disabled selected>Chọn loại tin nhắn</option>
                <option v-for="category in sms_category" :key="category.id"
                    :value="category.id">
                    {{ category.category_name }}
                </option>
            </select>
            <select class="form-control" name="category_id" id="category_id" v-model="formData.sms_category" disabled v-else>
                <option :value="4" selected>Tin nhắn vãng lai</option>
            </select>
        </div>

        <div class="form-outline mb-4" >
            <label for="student_type"><PERSON><PERSON><PERSON><PERSON><PERSON></label>
            <select class="form-control" name="student_type" id="student_type" 
            v-model="formData.student_type">
                <option :value="1" selected>Tin nhắn cho sinh viên đã có tài khoản</option>
                <option :value="0" >Tin nhắn cho sinh viên chưa có tài khoản</option>
            </select>
        </div>

        <div class="form-outline mb-4" v-if="formData.student_type == 1">
            <label for="owner_type">Người nhận</label>
            <select name="owner_type" id="owner_type" class="form-control" data-live-search="true"
                v-model="formData.owner_type">
                <option :value="null" selected>Chọn người nhận</option>
                <option :value="1">Phụ huynh</option>
                <option :value="0">Học sinh</option>
            </select>
        </div>

        <div class="form-outline mb-4" v-if="formData.student_type == 1">
            <label for="sms_prototype">Định dạng tin nhắn</label>
            <textarea name="sms_prototype" class="form-control" id="sms_prototype" cols="55" rows="5"
            placeholder="Xin chào {student_fullname}!
Hiện tại bạn đang có mã sinh viên là {student_code}, bạn có chắc là bạn không muốn thay đổi số điện thoại {student_telephone} của mình." 
            v-model="formData.sms_prototype"></textarea>
        </div>
        <div class="form-outline mb-4" v-else>
            <label for="sms_prototype">Định dạng tin nhắn</label>
            <textarea name="sms_prototype" class="form-control" id="sms_prototype" cols="55" rows="5"
            placeholder="Xin chào {student_fullname}!
Hiện tại bạn chưa có tài khoản sinh viên. Hãy đến phòng cộng tác sinh viên để tạo ngay nhé!" 
            v-model="formData.sms_prototype"></textarea>
        </div>

        <div class="file_prototype">
            <b-icon stacked icon="file-earmark-arrow-down-fill" variant="info" style="width:13px"></b-icon>
            <a :href="href_template" style="font-size: 12px; color: #007bff;" >Template mẫu</a>
        </div>

        <div class="form-outline mb-4">
            <label for="excel_file">Chọn file tải lên 
                <span style="color:red">(*) Lưu ý file tải lên bắt buộc phải bao gồm các trường trong file Template mẫu</span>
            </label>
            <input type="file" class="form-control" id="excel_file" name="excel_file" @change="changeFile"  accept=".xlsx"/>
        </div>
      
        <div class="form-outline mb-4" >
            <label class="form-check-label"  for="save_format">Bạn có muốn lưu lại mẫu tin nhắn?</label>
            <input type="checkbox" class="form-check-input" name="save_format" id="save_format" 
            v-model="formData.save_file">
        </div>
        <div class="d-flex flex-row-reverse">
            <button type="button" class="btn btn-success" v-if="formData.save_file" 
            @click="saveSmsFormat()">Lưu mẫu tin nhắn</button> 
            <button type="button" class="btn btn-primary" @click="exportSmsData()">Kiểm tra mẫu tin nhắn</button> 
        </div>
    </form>
</template>
<script>
import * as XLSX from 'xlsx/xlsx.mjs';
import stringInject from 'stringinject';
export default{
    props : ['list_role','sms_category','sms_format'],
    data(){
        return{
            formData:{
                sms_category:null,
                sms_prototype: "",
                file: File,
                save_file: false,
                student_type: 1,
                array_student_code:[],
                dataInput: [],
                owner_type: null,
            },
            listTelco: [],
            telco_code: [],
            href_template: null,
        }
    },
    created(){
        this.getListTelco();
        this.getHref();
    },
    watch:{
        "formData.student_type": function(){
            this.getHref();
            if(this.formData.student_type == 0){
                this.formData.sms_category = 4;
            }else{
                this.formData.sms_category = null;
            }
        }
    },
    methods:{        
        getListTelco(){
            axios
                .get("/api/v1/sms/getListTelco")
                .then((res) => {
                    this.listTelco = res;
                    for(let i=0; i<this.listTelco.length;i++){
                        this.telco_code[this.listTelco[i]['number']] = this.listTelco[i]['code'];
                    }
                });
        },
        getHref(){
            if(this.formData.sms_category == 4){
                this.href_template = window.location.origin + "/example/sms/mau_tin_nhan_vang_lai.xlsx";
            }else{
                this.href_template = window.location.origin + "/example/sms/mau_tin_nhan_co_ban.xlsx";
            }
        },
        changeFile(event){
            this.formData.array_student_code = [];
            this.file = null;
            this.file = event.target.files ? event.target.files[0] : null;
            if (this.file) {
                let reader = new FileReader();

                reader.onload = (e) => {
                    let bstr = e.target.result;
                    let wb = XLSX.read(bstr, { type: 'binary' });
                    let wsname = wb.SheetNames[0];
                    let ws = wb.Sheets[wsname];
                    let data = XLSX.utils.sheet_to_json(ws, { header: true });
                    this.formData.dataInput = data;
                    for(let i=0; i < data.length; i++){
                        this.formData.array_student_code.push(data[i].student_code);
                    }
                }
                reader.readAsBinaryString(this.file);
            }
            // console.log(this.formData.array_student_code);
        },

        exportSmsData(){
            if(this.formData.dataInput != []){
                if(this.formData.student_type == 1){
                    if(this.formData.owner_type != null ){
                        axios
                        .post("/api/v1/sms/fillDataStudent", this.formData)
                        .then((res)=>{
                            for(let i=0; i< this.formData.dataInput.length; i++){
                                let student_code = this.formData.dataInput[i].student_code;
                                if(res.hasOwnProperty('student_code') && res['student_code']['user'] != null){
                                    this.formData.dataInput[i].student_fullname = res[student_code]['student_name'];
                                    this.formData.dataInput[i].student_telephone = res[student_code]['phone'];
                                    this.formData.dataInput[i].telco = res[student_code]['telco'];
                                    this.formData.dataInput[i].sms_content =  stringInject(this.formData.sms_prototype,this.formData.dataInput[i]);
                                }else{
                                    this.formData.dataInput[i].student_fullname = "Chưa có dữ liệu";
                                    this.formData.dataInput[i].student_telephone = "Chưa có dữ liệu";
                                    this.formData.dataInput[i].telco = "Chưa có dữ liệu";
                                    this.formData.dataInput[i].sms_content =  stringInject(this.formData.sms_prototype,this.formData.dataInput[i]);
                                }
                            }
                            var worksheet = XLSX.utils.json_to_sheet(this.formData.dataInput);
                            var workbook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(workbook, worksheet, 'Test');
                            XLSX.writeFile(workbook, 'TestFormatSms.xlsx');

                        });
                    }else{
                        alert('Vui lòng chọn đối tượng để gửi tin nhắn!')
                    }
                }else{
                    for(let i= 0; i < this.formData.dataInput.length; i++){
                        // Lấy 3 số đầu trong số điện thoại của sinh viên
                        if(this.formData.dataInput[i].student_telephone != undefined){
                            let number = this.formData.dataInput[i].student_telephone.slice(0,3);
                            this.formData.dataInput[i].telco = this.telco_code[number];
                            this.formData.dataInput[i].sms_content =  stringInject(this.formData.sms_prototype,this.formData.dataInput[i]);
                            this.formData.dataInput[i].warning_id =null;
                        }else{
                            this.formData.dataInput[i].telco = "";
                            this.formData.dataInput[i].sms_content =  stringInject(this.formData.sms_prototype,this.formData.dataInput[i]);
                            this.formData.dataInput[i].warning_id = null;
                        }
                    }
                    var worksheet = XLSX.utils.json_to_sheet(this.formData.dataInput);
                    var workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Test');
                    XLSX.writeFile(workbook, 'TestFormatSms.xlsx');
                }
            }else{
                alert('Chưa có dữ liệu danh sách sinh viên!')
            }
        },
        saveSmsFormat(){
            if(this.formData.sms_prototype != "" && this.formData.sms_category != null){
                var descripton = prompt("Thêm mô tả cho mẫu tin nhắn:", '');
                this.formData.descripton = descripton;
                axios
                    .post("/api/v1/sms/createFormat", this.formData)
                    .then((res) => {
                        if(res['status']){
                            alert(res['notification']);
                            // console.log(res);
                            this.$emit('updateFormatSms', res['newFormatSms']);
                        }else{
                            alert('Tạo mẫu tin nhắn thất bại!')
                        };
                    });
            }else{
                alert("Hãy điền đầy đủ các trường thông tin")
            }
        },
    }
}
</script>
<style>
.form-check-input{
    margin-left: 10px;
}
#excel_file{
    height: 37px;
}
.file_prototype:hover span{
    cursor: pointer;
    color: rgb(1, 1, 175);
    text-decoration: underline;
}
</style>