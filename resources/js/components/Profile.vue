<template>
  <div class="kt-portlet kt-portlet--tabs">
    <div class="kt-portlet__head">

      <div class="kt-portlet__head-toolbar w-100">
        <ul class="
            nav
            nav-tabs
            nav-tabs-line
            nav-tabs-line-danger
            nav-tabs-line-2x
            nav-tabs-line-right
            w-100
          " 
          ref="tabList"
          role="tablist">
          <li
            v-for="(tab, index) in allTabs"
            :key="index"
            class="nav-item fixed-tab mr-0"
          >
            <a
              class="nav-link kt-font-boldest kt-font-transform-u"
              :class="{ active: index === 0 }"
              :href="'#' + tab.id"
              data-toggle="tab"
            >
              {{ tab.label }}
            </a>
          </li>
        </ul>
      </div>
    </div>
    <div class="kt-portlet__body">
      <div class="tab-content">
        <div class="tab-pane active" id="lich_hoc" role="tabpanel">
          <div class="form-container">
            <div class="form-group select-wrapper">
              <label class="title"> Thờ<PERSON>ian</label>
              <select v-model="day" class="form-control">
                <option v-for="day in days" :value="day.value">
                  {{ day.name }}
                </option>
              </select>
              <span class="custom-arrow">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </span>
            </div>
          </div>
          <div
            class="alert alert-warning"
            role="alert"
            v-if="schedules.length === 0"
          >
            <div class="alert-text">Không có lịch học!</div>
          </div>
          <div class="table-responsive">
            <table class="table" v-if="schedules.length !== 0">
              <thead class="thead-light">
                <tr>
                  <th>STT</th>
                  <th class="no-sort">Ngày</th>
                  <th>Phòng</th>
                  <th>Giảng đường</th>
                  <th>Mã môn</th>
                  <th>Môn</th>
                  <th>Lớp</th>
                  <th>Giảng viên</th>
                  <th>Ca</th>
                  <th>Thời gian</th>
                </tr>
              </thead>
            <!-- </table>
            <div class="table-body-wrapper">
              <table> -->
              <tbody v-if="checkLoading.schedules == true" >
                <tr v-for="(schedule, key) in schedules">
                  <td>{{ key + 1 }}</td>
                  <td class="text-capitalize">
                    {{ schedule.day_name }}<br />{{ schedule.day_transform
                    }}{{ schedule.msg }}
                  </td>
                  <td>{{ schedule.room_name }}</td>
                  <td>{{ schedule.area_name }}</td>
                  <td>{{ schedule.psubject_code }}</td>
                  <td>{{ schedule.subject_name }}</td>
                  <td>{{ schedule.group_name }}</td>
                  <td>{{ schedule.activity_leader_login }}</td>
                  <td>Ca {{ schedule.slot }}</td>
                  <td>
                    {{ schedule.slotInfo.slot_start }}<br />{{
                      schedule.slotInfo.slot_end
                    }}
                  </td>
                </tr>
              </tbody>
              <tbody v-else>
                <tr>
                    <td colspan="10" class="text-center">Đang tải dữ liệu....</td>
                </tr>
              </tbody>
            </table>
            <!-- </div> -->
          </div>
        </div>

        <div class="tab-pane" id="lich_thi" role="tabpanel">
          <div class="form-container">
            <div class="form-group select-wrapper">
              <label class="title" >Thời gian</label>
              <select v-model="day_exam" class="form-control">
                <option v-for="day in days" :value="day.value">
                  {{ day.name }}
                </option>
              </select>
              <span class="custom-arrow">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </span>
            </div>
          </div>
          
          <div
            class="alert alert-warning"
            role="alert"
            v-if="schedules_exam.length === 0"
          >
            <div class="alert-text">Không có lịch học!</div>
          </div>
          <div class="table-responsive">
            <table class="table" v-if="schedules_exam.length !== 0">
              <thead class="thead-light">
                <tr>
                  <th>Ngày</th>
                  <th>Phòng</th>
                  <th>Giảng đường</th>
                  <th>Môn</th>
                  <th>Lớp</th>
                  <th>Giảng viên</th>
                  <th>Ca, Thời gian</th>
                  <th>Loại buổi học</th>
                  <th>Nội dung</th>
                  <th>Nhiệm vụ sinh viên</th>
                  <th>Học liệu môn</th>
                  <th>Tài liệu tham khảo</th>
                  <th></th>
                </tr>
              </thead>
              <tbody v-if="checkLoading.schedules_exam == true">
                <tr v-for="(schedule, key) in schedules_exam" :key="key">
                  <td>
                    <a href="">
                      {{ schedule.date }}
                    </a>
                  </td>
                  <td>{{ schedule.room_name }}</td>
                  <td>{{ schedule.area_name }}</td>
                  <td>{{ schedule.subject_name }}</td>
                  <td>
                    <a href="">
                      {{ schedule.class_name }}
                    </a>
                  </td>
                  <td>{{ schedule.leader_name }}</td>
                  <td>{{ schedule.slot_and_time }}</td>
                  <td>{{ schedule.type }}</td>
                  <td>{{ schedule.content }}</td>
                  <td>{{ schedule.duty_of_student }}</td>
                  <td>{{ schedule.document_of_subject }}</td>
                  <td>{{ schedule.refer_documents }}</td>
                  <td>
                    <a href=""> Chi tiết </a>
                  </td>
                </tr>
              </tbody>
              <tbody v-else>
                <tr>
                    <td colspan="11" class="text-center">Đang tải dữ liệu....</td>
                </tr>
              </tbody>
            </table>
          </div>

          <b-row
            v-if="paginationExam.total > paginationExam.per_page"
            class="pagination"
          >
            <b-col cols="12" class="text-center mt-3">
              <pagination
                :total-pages="paginationExam.total_page"
                :total="paginationExam.total"
                :per-page="paginationExam.per_page"
                :current-page="paginationExam.current_page"
                @pagechanged="getCurrentPageExam"
              />
            </b-col>
          </b-row>
        </div>

        <div class="tab-pane" id="diem_danh" role="tabpanel">
          <div class="form-container">
            <div class="form-group select-wrapper">
              <label class="title">Học kì</label>
              <select v-model="term_id" class="form-control">
                <option
                  v-for="(term, index) in terms"
                  :value="term.id"
                  :key="index"
                >
                  {{ term.term_name }}
                </option>
              </select>
              <span class="custom-arrow">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </span>
            </div>
            <div class="form-group select-wrapper">
              <label class="title">Môn học</label>
              <select v-model="subjectInTerm" class="form-control">
                <option value="">Tất cả các môn</option>
                <option
                  v-for="(subject, index) in subjectsInTerm"
                  :value="subject.course_id"
                  :key="index"
                >
                  {{ subject.psubject_name }} ({{ subject.psubject_code }}) -
                  {{ subject.pgroup_name }}
                </option>
              </select>
              <span class="custom-arrow">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </span>
            </div>
          </div>
          
          <div v-if="!loadingAttendanceTable">
            <div
              class="kt-portlet"
              v-for="(attendance, index) in listBaseOnFilter"
              :key="index"
            >
              <div class="kt-portlet__head kt-portlet__head--lg">
                <div class="kt-portlet__head-label">
                  <span class="kt-portlet__head-icon">
                    <i class="kt-font-brand flaticon2-list-2"></i>
                  </span>
                  <h3 class="kt-portlet__head-title">
                    {{ attendance.psubject_name }} ({{
                      attendance.psubject_code
                    }}) - {{ attendance.pgroup_name }}
                  </h3>
                </div>
              </div>
              <div class="kt-portlet__body">
                <div class="table-responsive">
                  <table class="table table-bordered table-content">
                    <thead>
                      <tr>
                        <th class="kt-font-boldest">Bài học</th>
                        <th>Ngày</th>
                        <th>Ca</th>
                        <th>Người điểm danh</th>
                        <th>Mô tả</th>
                        <th>Trạng thái đi học</th>
                        <th>Ghi chú</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(plan, index) in attendance.plans"
                        :key="index"
                      >
                        <td>{{ plan.course_session }}</td>
                        <td>{{ plan.plan_day }}</td>
                        <td>{{ plan.plan_slot }}</td>
                        <td>{{ plan.plan_teacher }}</td>
                        <td>{{ plan.session_type }}</td>
                        <td
                          style="display: flex; justify-content: space-between"
                        >
                          <span :class="plan.plan_status.style"
                            >{{ plan.plan_status.value }}
                          </span>
                          <b-button
                            v-if="
                              plan.plan_status.value !== 'Asume present' &&
                              plan.plan_status.value !== 'Future' &&
                              currentTermId === attendance.course.term_id
                            "
                            variant="outline-primary"
                            @click="
                              showModalUpdateAttendancePopup(
                                attendance,
                                plan,
                                index
                              )
                            "
                            >Edit</b-button
                          >
                          <!-- <b-button
                        variant="outline-primary"
                        @click="showModalUpdateAttendancePopup(attendance, plan, index)"
                        >Edit</b-button> -->
                        </td>
                        <td>
                          {{
                            attendance.ds_attend_comment[
                              "session_" + plan.course_session
                            ]
                          }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <b-modal
                    v-model="showModalUpdateAttendance"
                    title="Chỉnh sửa trạng thái điểm danh"
                    @ok="handleUpdateAttendance"
                  >
                    <b-container fluid>
                      <b-row class="mb-1">
                        <b-col cols="4">Môn học</b-col>
                        <b-col
                          >{{ planChoiced.subject_code }}-
                          {{ planChoiced.subject_name }}</b-col
                        >
                      </b-row>
                      <b-row class="mb-1">
                        <b-col cols="4">Ngày học</b-col>
                        <b-col>{{ planChoiced.plan_day }}</b-col>
                      </b-row>
                      <b-row class="mb-1">
                        <b-col cols="4">Trạng thái điểm danh</b-col>
                        <b-col>
                          <b-form-group v-slot="{ ariaDescribedby }">
                            <b-form-radio-group
                              v-model="attendance_status"
                              :options="optionsAttendanceStatus"
                              :aria-describedby="ariaDescribedby"
                              name="plain-inline"
                              plain
                            ></b-form-radio-group>
                          </b-form-group>
                        </b-col>
                      </b-row>
                      <b-row class="mb-1">
                        <b-col cols="4">Lí do</b-col>
                        <b-col>
                          <b-form-group
                            id="fieldset-1"
                            valid-feedback="OK!"
                            :invalid-feedback="invalidFeedback"
                            :state="stateReason"
                          >
                            <b-form-input
                              id="input-1"
                              v-model="reasonUpdateAttendance"
                              :state="stateReason"
                              trim
                            ></b-form-input>
                          </b-form-group>
                        </b-col>
                      </b-row>
                    </b-container>
                  </b-modal>
                </div>
              </div>
            </div>
          </div>
          <div class="text-center" v-if="loadingAttendanceTable">
            <b-spinner variant="primary" label="Spinning"></b-spinner>
          </div>
        </div>
        <div class="tab-pane" id="dang_hoc" role="tabpanel">
            <div class="table-responsive">
                <table class="table table-bordered table-content">
                    <thead>
                    <tr>
                        <th class="kt-font-boldest">Tên môn</th>
                        <th class="kt-font-boldest">Mã môn</th>
                        <th class="kt-font-boldest">Mã chuyển đổi</th>
                        <th class="kt-font-boldest">Lớp</th>
                        <th class="kt-font-boldest">Điểm trung bình</th>
                        <th class="kt-font-boldest">Vắng/Tổng số</th>
                        <th class="kt-font-boldest">Số buổi lớp đã hoàn thành</th>
                        <th class="kt-font-boldest">Vắng</th>
                        <th class="kt-font-boldest">Vắng tối đa cho phép</th>
                        <th class="kt-font-boldest">Ghi chú</th>
                    </tr>
                    </thead>
                    <tbody v-if="checkLoading.studying == true">
                      <tr v-for="study in studying" v-if="study.term_id === term_id">
                          <td>{{study.psubject_name}}</td>
                          <td>{{study.psubject_code}}</td>
                          <td>{{study.skill_code}}</td>
                          <td>{{study.pgroup_name}}</td>
                          <td>{{study.grade}}</td>
                          <td>{{study.attendance_absent}}/{{study.total_session}}</td>
                          <td>{{study.done_activity}}/{{study.total_session}}</td>
                          <td>{{study.attendance_absent_percent}}%</td>
                          <td>{{study.attendance_cutoff}}%</td>
                          <td>{{study.description}}</td>
                      </tr>
                    </tbody>
                    <tbody v-else>
                      <tr>
                          <td colspan="10" class="text-center">Đang tải dữ liệu....</td>
                      </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="tab-pane" id="lich_su_hoc" role="tabpanel">
          <h3>Lịch sử học</h3>
          <div class="table-responsive height-1350">
            <table class="table table-bordered table-content">
              <thead>
                <tr>
                  <th class="kt-font-boldest">Học kỳ</th>
                  <th class="kt-font-boldest">Tên môn</th>
                  <th class="kt-font-boldest">Mã môn</th>
                  <th class="kt-font-boldest">Mã chuyển đổi</th>
                  <th class="kt-font-boldest">Lớp</th>
                  <th class="kt-font-boldest text-center">Điểm quá trình</th>
                  <th class="kt-font-boldest text-center">Điểm trung bình</th>
                  <th class="kt-font-boldest">Vắng/Tổng số</th>
                  <th class="kt-font-boldest">Tổng số buổi</th>
                  <th class="kt-font-boldest">Ngày bắt đầu</th>
                  <th class="kt-font-boldest">Ngày kết thúc</th>
                  <th class="kt-font-boldest">Trạng thái</th>
                </tr>
              </thead>
              <tbody v-if="checkLoading.studying == true">
                <tr v-for="(study, index) in studying" :key="index">
                  <td>{{ study.pterm_name }}</td>
                  <td
                    class="pointer-td"
                    @click="showModal(user_login, study.groupid, study)"
                  >
                    {{ study.psubject_name }}
                  </td>
                  <td
                    class="pointer-td"
                    @click="showModal(user_login, study.groupid, study)"
                  >
                    {{ study.psubject_code }}
                  </td>
                  <td>{{ study.skill_code }}</td>
                  <td
                    class="pointer-td"
                    @click="redirectToGroup(study.groupid)"
                  >
                    {{ study.pgroup_name }}
                  </td>
                  <td class="text-center">{{ study.grade_qua_trinh }}</td>
                  <td class="text-center">{{ study.grade }}</td>
                  <td>
                    {{ study.attendance_absent }}/{{ study.total_session }}({{
                      study.attendance_absent_percent
                    }}%)
                  </td>
                  <td>{{ study.done_activity }}/{{ study.total_session }}</td>
                  <td>{{ study.start_date }}</td>
                  <td>{{ study.end_date }}</td>
                  <td :class="'kt-font-' + study.status_info.style">
                    {{ study.status_info.value }}
                  </td>
                </tr>
              </tbody>
              <tbody v-else>
                <tr>
                    <td colspan="12" class="text-center">Đang tải dữ liệu....</td>
                </tr>
              </tbody>
            </table>
            <b-modal ref="my-modal" hide-footer title="Bảng điểm chi tiết">
              <div class="d-block text-center">
                <p>
                  {{ markDetail.psubject_name }}({{
                    markDetail.psubject_code
                  }})-{{ markDetail.pgroup_name }}
                </p>
                <table
                  v-if="!loading"
                  class="table table-bordered table-content"
                >
                  <thead>
                    <tr>
                      <th class="kt-font-boldest">#</th>
                      <th class="kt-font-boldest">Tên đầu điểm</th>
                      <th class="kt-font-boldest">Trọng số</th>
                      <th class="kt-font-boldest">Điểm</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-if="markDetail.grade_detail.length > 0 && !loading"
                      v-for="(study, index) in markDetail.grade_detail"
                      :key="index"
                    >
                      <td>{{ index }}</td>
                      <td>{{ study.grade_name }}</td>
                      <td>{{ study.grade_weight }}</td>
                      <td>{{ study.grade_value }}</td>
                    </tr>
                    <tr>
                      <td colspan="4">
                        <b>Trung bình: </b>
                        <span
                          :class="'kt-font-' + markDetail.status_info.style"
                          >{{ markDetail.grade }}</span
                        >
                        <b>Trạng thái:</b>
                        <span
                          :class="'kt-font-' + markDetail.status_info.style"
                          >{{ markDetail.status_info.value }}</span
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div v-if="loading">
                  <b-spinner variant="primary" label="Spinning"></b-spinner>
                </div>
              </div>
            </b-modal>
          </div>
        </div>
        <div style="" class="tab-pane" id="bang_diem_theo_ky" role="tabpanel">
          
          <div class="form-group">
            <div class="form-container">
              <div class="form-group select-wrapper">
                <select v-model="term_id_bdtk" class="form-control">
                  <option v-for="term in terms" :value="term.id">
                    {{ term.term_name }}
                  </option>
                </select>
                <span class="custom-arrow">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </span>
              </div>
            </div>
            
            <br />
            <template
              v-for="study in studying"
              v-if="study.term_id === term_id_bdtk"
            >
              <h5><b>{{ study.psubject_name }} ({{ study.psubject_code }}) - {{ study.pgroup_name }}</b></h5>
              <div class="table-responsive mb-4">
                <table class="table table-striped table-content">
                  <thead>
                    <tr>
                      <th class="kt-font-boldest">#</th>
                      <th class="kt-font-boldest">Tên đầu điểm</th>
                      <th class="kt-font-boldest">Trọng số</th>
                      <th class="kt-font-boldest text-center">Điểm</th>
                      <th class="kt-font-boldest text-center">Chú thích</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(grade, key) in study.grade_info">
                      <td>{{ key + 1 }}</td>
                      <td>{{ grade.name }}</td>
                      <td>{{ grade.percent }}%</td>
                      <td class="text-center">{{ grade.value }}</td>
                      <td>{{ grade.comment }}</td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr>
                      <th></th>
                      <th colspan="2">
                        Trung bình: <span class="red">{{ study.grade }}</span>
                      </th>
                      <td colspan="2">
                        Trạng thái:
                        <span
                          class="kt-font-boldest"
                          :class="'kt-font-' + study.status_info.style"
                          >{{ study.status_info.value }}</span
                        >
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </template>
          </div>
        </div>
        <div style="" class="tab-pane" id="bang_diem" role="tabpanel">
          <div class="table-responsive height-1350">
            <a
              target="_blank"
              style="color: blue; font-size: 17px !important"
              :href="`/exportPdfBangDiemTichLuy/${this.user_login}`"
              >Export bảng điểm tích luỹ (PDF)</a
            >
            <table class="table table-striped table-content">
              <thead>
                <tr>
                  <th class="kt-font-boldest">#</th>
                  <th class="kt-font-boldest">Kì</th>
                  <th class="kt-font-boldest">Học kỳ</th>
                  <th class="kt-font-boldest">Tên môn</th>
                  <th class="kt-font-boldest">Mã môn</th>
                  <th class="kt-font-boldest">Mã chuyển đổi</th>
                  <th class="kt-font-boldest">Loại</th>
                  <th class="kt-font-boldest">Số tín chỉ {{ user.grade_create }}</th>
                  <template v-if="isNewScoringSystem">
                    <th class="kt-font-boldest text-center">Thang điểm 10</th>
                    <th class="kt-font-boldest text-center">Thang điểm 4</th>
                    <th class="kt-font-boldest text-center">Điểm chữ</th>
                  </template>
                  <template v-else>
                    <th class="kt-font-boldest text-center">Điểm</th>
                  </template>
                  
                  <th class="kt-font-boldest text-center">Trạng thái</th>
                  <th class="kt-font-boldest text-center">Ghi chú</th>
                  <th class="kt-font-boldest text-center">Lần học</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(grade, key) in grade_point.details">
                  <td>{{ key + 1 }}</td>
                  <td>{{ grade.ki_thu }}</td>
                  <td>{{ grade.term_name }}</td>
                  <template v-if="parseInt(grade.type) === 1">
                    <td>
                      {{ grade.subject_name_replace }} (Môn
                      {{ grade.subject_code }} được thay thế bởi môn
                      {{ grade.subject_code_replace }})
                    </td>
                    <td>{{ grade.subject_code_replace }}</td>
                  </template>
                  <template v-else>
                    <td>
                      {{
                        grade.subject_name_pass
                          ? grade.subject_name_pass
                          : grade.subject_name
                      }}
                    </td>
                    <td>
                      {{
                        grade.subject_code_pass
                          ? grade.subject_code_pass
                          : grade.subject_code
                      }}
                    </td>
                  </template>
                  <td>
                    {{
                      grade.skill_code_replace
                        ? grade.skill_code_replace
                        : grade.skill_code
                    }}
                  </td>
                  <td>
                    <template v-if="parseInt(grade.type) === 0">Môn gốc</template>
                    <template v-else-if="parseInt(grade.type) === 1">Thay thế</template>
                    <template v-else-if="parseInt(grade.type) === 2">Miễn giảm</template>
                    <template v-else>Thực tập</template>
                  </td>
                  <td>{{ grade.num_of_credit }}</td>
                  
                  <template v-if="isNewScoringSystem">
                    <td class="text-center">
                      <template v-if="parseInt(grade.type) === 2">*</template>
                      <template v-else>{{ grade.point }}</template>
                    </td>
                    
                    <td class="text-center">
                      <template v-if="parseInt(grade.type) === 2">*</template>
                      <template v-else>{{ grade.pointGPA }}</template>
                    </td>
                    
                    <td class="text-center">
                      <template v-if="parseInt(grade.type) === 2">*</template>
                      <template v-else><span v-html="grade.pointGPAText"></span></template>
                    </td>
                  </template>
                  <template v-else>
                    <td class="text-center">
                      <template v-if="parseInt(grade.type) === 2">*</template>
                      <template v-else>{{ grade.point }}</template>
                    </td>
                  </template>
                  
                  <td
                    class="text-center kt-font-boldest"
                    :class="'kt-font-' + grade.status_info.style"
                  >
                    {{ grade.status_info.value }}
                  </td>
                  <td
                    class="text-center kt-font-boldest"
                    :class="'kt-font-' + grade.status_info.style"
                  >
                    {{ grade.status_info.status_id }}
                  </td>
                  <td class="text-center">{{ grade.luot_hoc }}</td>
                </tr>
                <tr>
                  <td colspan="14">
                    <template v-if="isNewScoringSystem">
                      Điểm trung bình học tập (thang 10):&nbsp <b>{{ GPA }}</b> 
                      &nbsp Điểm trung bình (thang 4):&nbsp <b>{{ convertScaleGPAPoint4(GPA) }}</b> 
                      <!-- &nbsp Điểm trung bình tốt nghiệp (thang 10):&nbsp <b>{{ GPATN }}</b>  -->
                      &nbsp Điểm chữ:&nbsp <b><span v-html="convertNumToTextGPAPoint(GPA)"></span></b> 
                    </template>
                    <template v-else>
                      Điểm trung bình học tập:&nbsp <b>{{ GPA }}</b> 
                    </template>
                    &nbsp Tổng tín chỉ đạt: &nbsp <b>{{ totalOfPassedCredits }}</b> 
                    &nbsp Tổng tín miễn giảm: &nbsp
                    
                    <b>{{ totalNumberOfCreditsWaived }}</b> &nbsp Tổng tín
                    chỉ:&nbsp <b>{{ totalOfCredits }}</b>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="tab-pane" id="ky_luat_khen_thuong" role="tabpanel">
          <div class="table-responsive">
            <table class="table table-striped table-content">
              <thead>
                <tr>
                  <th class="kt-font-boldest">#</th>
                  <th class="kt-font-boldest">Loại quyết định</th>
                  <th class="kt-font-boldest">Học kỳ</th>
                  <th class="kt-font-boldest">Ngày tháng</th>
                  <th class="kt-font-boldest">Nội dung</th>
                  <th class="kt-font-boldest">Số quyết định</th>
                  <th class="kt-font-boldest">Người ký</th>
                  <th class="kt-font-boldest">Ghi chú</th>
                </tr>
              </thead>
              <tbody v-if="checkLoading.discipline == true">
                <tr v-for="(item, key) in discipline">
                  <td>{{ (parseInt(key) + 1) }}</td>
                  <td>
                    <template v-if="item.type == 1">Khen Thưởng</template>
                    <template v-else-if="item.type == 2">Kỷ luật</template>
                    <template v-else>Không xác định</template>
                  </td>
                  <td>{{ item.term_name }}</td>
                  <td>{{ item.date_affected }}</td>
                  <td>{{ item.reason }}</td>
                  <td>{{ item.decision_no }}</td>
                  <td>{{ item.signee }}</td>
                  <td>{{ item.note }}</td>
                </tr>
              </tbody>
              <tbody v-else>
                <tr>
                    <td colspan="7" class="text-center">Đang tải dữ liệu....</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <!-- <div class="tab-pane" id="sms" role="tabpanel">
          <div class="table-responsive">
            <table class="table table-striped table-content">
              <thead>
                <tr>
                  <th class="kt-font-boldest">#</th>
                  <th class="kt-font-boldest">Số điện thoại</th>
                  <th class="kt-font-boldest">Nhà mạng</th>
                  <th class="kt-font-boldest">Loại tài khoản</th>
                  <th class="kt-font-boldest">Ngày tạo</th>
                  <th class="kt-font-boldest">Trạng thái</th>
                </tr>
              </thead>
              <tbody v-if="checkLoading.sms == true">
                <tr v-for="(item, key) in phoneList">
                  <td>{{ key + 1 }}</td>
                  <td>{{ item.phone }}</td>
                  <td>{{ item.network_name }}</td>
                  <td>{{ item.owner_type }}</td>
                  <td>{{ item.created_on }}</td>
                  <td :class="item.is_active.style">
                    {{ item.is_active.value }}
                  </td>
                </tr>
              </tbody>
              <tbody v-else>
                <tr>
                    <td colspan="11" class="text-center">Đang tải dữ liệu....</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div> -->
        <!-- Add Tab Phone Number Management -->
        <div class="tab-pane" id="phone-number" role="tabpanel">
          <b-overlay
            :show="overlay.show"
            :variant="overlay.variant"
            :opacity="overlay.opacity"
            :blur="overlay.blur"
            :rounded="overlay.sm"
          >
            <template #overlay>
              <div class="text-center">
                <b-icon
                  style="color: #7366ff"
                  icon="arrow-clockwise"
                  font-scale="2"
                  animation="spin"
                />
              </div>
            </template>

            <div class="table-responsive" style="overflow-y: hidden">
              <table class="table table-striped table-content">
                <thead>
                  <tr>
                    <th class="kt-font-boldest text-center">#</th>
                    <th class="kt-font-boldest text-center">Số điện thoại</th>
                    <!-- <th class="kt-font-boldest text-center">Tên chủ tài khoản</th> -->
                    <th class="kt-font-boldest text-center">Nhà mạng</th>
                    <th class="kt-font-boldest text-center">Loại tài khoản</th>
                    <th class="kt-font-boldest text-center">Ngày tạo tài khoản</th>
                    <th class="kt-font-boldest text-center">Trạng thái</th>
                    <th class="kt-font-boldest text-center">Hành động</th>
                  </tr>
                </thead>
              <!-- </table>
              <div class="table-body-wrapper">
                <table> -->
                  <tbody>
                    <tr v-for="(item, index) in phoneList" :key="index">
                      <td class="text-center">{{ index + 1 }}</td>
                      <td class="text-center">{{ item.phone }}</td>
                      <!-- <td class="text-center">{{ item.owner_name }}</td> -->
                      <td class="text-center">{{ item.network_name }}</td>
                      <td class="text-center">{{ item.owner_type_name }}</td>
                      <td class="text-center">{{ item.created_on }}</td>
                      <td class="text-center">
                        <b-form-checkbox
                          switch
                          size="lg"
                          variant="success"
                          :state="item.is_active"
                          v-model="item.is_active"
                          @change="onClickChangeStatus(item)"
                        ></b-form-checkbox>
                      </td>

                      <td class="text-center">
                        <b-button
                          size="sm"
                          variant="outline-warning"
                          @click="btnEditPhone(item)"
                        >
                          Chỉnh sửa
                        </b-button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              <!-- </div> -->
              <div
                class="modal fade"
                id="phoneEditor"
                data-backdrop="static"
                tabindex="-1"
                role="dialog"
                aria-labelledby="phoneEditorLabel"
                aria-hidden="true"
              >
                <div
                  class="modal-dialog modal-dialog-centered"
                  role="document"
                >
                  <div class="modal-content">
                    <b-overlay
                      :show="overlayModalActive.show"
                      :variant="overlayModalActive.variant"
                      :opacity="overlayModalActive.opacity"
                      :blur="overlayModalActive.blur"
                      :rounded="overlayModalActive.sm"
                    >
                      <template #overlay>
                        <div class="text-center">
                          <b-icon
                            style="color: #7366ff"
                            icon="arrow-clockwise"
                            font-scale="2"
                            animation="spin"
                          />
                          <span>Xin vui lòng chờ...</span>
                        </div>
                      </template>

                      <div class="modal-header">
                        <h5 class="modal-title" id="phoneEditorLabel">
                          Cập nhật số điện thoại của sinh viên
                        </h5>
                        <button
                          type="button"
                          class="close"
                          data-dismiss="modal"
                          aria-label="Close"
                          @click="onClosePhoneNumberEditor()"
                        >
                          <i aria-hidden="true" class="ki ki-close"></i>
                        </button>
                      </div>
                      <div class="modal-body">
                        <b-form-group
                          v-slot="{ ariaDescribedby }"
                        >
                          <b-form-radio
                            v-model="changePhoneNumber"
                            :aria-describedby="ariaDescribedby"
                            name="some-radios"
                            :value="true"
                            >Thay đổi số điện thoại</b-form-radio
                          >
                        </b-form-group>
                        <b-form-group
                          id="input-group-edit-owner-type"
                          label="Loại tài khoản:"
                          label-for="input-add-phoneNumber"
                        >
                          <b-form-select
                            v-model="phoneNumberSelected.owner_type"
                            :options="options_owner_type"
                            :disabled="!changePhoneNumber"
                          ></b-form-select>
                        </b-form-group>

                        <b-form-group
                          id="input-group-edit-telco"
                          label="Nhà mạng:"
                          label-for="input-add-phoneNumber"
                        >
                          <b-form-select
                            v-model="phoneNumberSelected.telco"
                            :options="options_telco"
                            :disabled="!changePhoneNumber"
                          ></b-form-select>
                        </b-form-group>

                        <b-form-group
                          id="input-group-edit-phone-number"
                          label="Số điện thoại mới:"
                          label-for="input-add-phoneNumber"
                        >
                          <b-form-input
                            v-if="!changePhoneNumber"
                            type="tel"
                            id="inline-form-show-phone-number"
                            class="mt-2"
                            v-model="phoneNumberSelected.phone"
                            :disabled="!changePhoneNumber"
                          >
                          </b-form-input>
                          <b-form-input
                            v-else
                            type="tel"
                            class="mt-2"
                            id="inline-form-change-phone-number"
                            v-model="phoneNumberSelected.phone"
                            :state="validateSDT(phoneNumberSelected.phone)"
                            placeholder="Nhập số điện thoại"
                            maxlength="10"
                            pattern="[0-9]{10}"
                          >
                          </b-form-input>
                        </b-form-group>
                      </div>
                      <div class="modal-footer">
                        <button
                          type="button"
                          class="btn btn-light-primary font-weight-bold"
                          data-dismiss="modal"
                          @click="onClosePhoneNumberEditor()"
                        >
                          Huỷ
                        </button>
                        <button
                          type="button"
                          style="background-color: #7366ff"
                          class="btn btn-primary font-weight-bold"
                          :disabled="!changePhoneNumber && !validateSDT(phoneNumberSelected.phone)"
                          @click="saveChangePhoneNumber(phoneNumberSelected)"
                        >
                          Lưu thay đổi
                        </button>
                      </div>
                    </b-overlay>
                  </div>
                </div>
              </div>
            </div>
            <b-button-group>
              <b-button
                id="btn-add-phone"
                variant="info"
                @click="openModalAddPhone()"
                data-toggle="modal"
                data-target="#modalAddPhoneNumber"
              >
                <b-icon icon="plus-circle-fill" aria-hidden="true" class="mr-1" ></b-icon>
                Thêm số mới
              </b-button>
            </b-button-group>
            <div
              class="modal fade"
              id="modalAddPhoneNumber"
              tabindex="-1"
              role="dialog"
              aria-labelledby="modalAddPhoneNumberLabel"
              aria-hidden="true"
            >
              <div
                class="modal-dialog modal-dialog-centered"
                role="document"
              >
                <div class="modal-content">
                  <b-overlay
                    :show="overlayModalActive.show"
                    :variant="overlayModalActive.variant"
                    :opacity="overlayModalActive.opacity"
                    :blur="overlayModalActive.blur"
                    :rounded="overlayModalActive.sm"
                  >
                    <template #overlay>
                      <div class="text-center">
                        <b-icon
                          style="color: #7366ff"
                          icon="arrow-clockwise"
                          font-scale="2"
                          animation="spin"
                        />
                        <span>Xin vui lòng chờ...</span>
                      </div>
                    </template>

                    <div class="modal-header">
                      <h5 class="modal-title" id="addPhoneNumberLabel">
                        Thêm mới số điện thoại sinh viên
                      </h5>
                      <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="Close"
                        @click="onClosePhoneNumberEditor()"
                      >
                        <i aria-hidden="true" class="ki ki-close"></i>
                      </button>
                    </div>
                    <div class="modal-body">
                      <form>
                        <b-form-group
                          id="input-group-add-owner-type"
                          label="Loại tài khoản:"
                          label-for="input-add-owner-type"
                          require
                        >
                          <b-form-select
                            id="input-add-owner-type"
                            v-model="newPhoneNumber.owner_type"
                            :options="options_owner_type"
                          ></b-form-select>
                        </b-form-group>

                        <b-form-group
                          id="input-group-add-owner-name"
                          label="Họ và tên chủ tài khoản:"
                          label-for="input-add-owner-name"
                          require
                        >
                          <b-form-input
                            type="text"
                            class="mt-2"
                            id="input-add-owner-name"
                            v-model="newPhoneNumber.owner_name"
                            placeholder="Nguyễn Văn A"
                            require
                          >
                          </b-form-input>
                        </b-form-group>

                        <b-form-group
                          id="input-add-owner-type"
                          label="Nhà mạng:"
                          require
                        >
                          <b-form-select
                            v-model="newPhoneNumber.telco"
                            :options="options_telco"
                          ></b-form-select>
                        </b-form-group>

                        <b-form-group
                          id="input-group-add-phoneNumber"
                          label="Số điện thoại:"
                          label-for="input-add-phoneNumber"
                        >
                          <b-form-input
                            type="tel"
                            class="mt-2"
                            id="input-add-phoneNumber"
                            v-model="newPhoneNumber.phone"
                            :state="validateSDT(newPhoneNumber.phone)"
                            placeholder="Nhập số điện thoại"
                            maxlength="10"
                            pattern="[0-9]{10}"
                            require
                          >
                          </b-form-input>
                        </b-form-group>
                      </form>
                    </div>
                    <div class="modal-footer">
                      <button
                        type="button"
                        class="btn btn-light-primary font-weight-bold"
                        data-dismiss="modal"
                        @click="onCloseAddPhoneNumber()"
                      >
                        Huỷ
                      </button>
                      <button
                        type="button"
                        style="background-color: #7366ff"
                        class="btn btn-primary font-weight-bold"
                        @click="saveNewPhoneNumber(phoneNumberSelected)"
                      >
                        Thêm
                      </button>
                    </div>
                  </b-overlay>
                </div>
              </div>
            </div>
          </b-overlay>
        </div>

        <b-modal
          v-model="changePassword.show"
          hide-footer
          hide-header
          modal-class="custom-modal"
        >
          <template #default>
            <div class="model-body">
              <h3 class="[changePassword.style]">Cập nhập mật khẩu</h3>
              <p style="display: block;" :class="[changePassword.style]" v-html="changePassword.text"></p>
              <div class="sa-button-container">
                <div class="form-group">
                  <label for="">Mật khẩu mới: (Mật khẩu gợi ý: <span @click="makeToast('success')" class="hover-copy">Abcd@123</span> )</label>
                  <input style="margin-bottom: 10px" v-model="changePassword.new_password" type="text" placeholder="Mật khẩu cần đổi" class="form-control" autocomplete="off">
                </div>
                <div class="form-group">
                  <label for="">Nhập lại mật khẩu mới:</label>
                  <input style="margin-bottom: 10px" v-model="changePassword.new_password2" type="text" placeholder="Mật khẩu cần đổi" class="form-control" autocomplete="off">
                </div>
                <div class="sa-confirm-button-container">
                  <button class="btn btn-info btn-primary" tabindex="1" @click="onClickChangePassWord()">Đổi mật khẩu</button>
                  <button class="btn btn-info btn-secondary" tabindex="1" @click="onClickChangePassWord(0)">Đóng</button>
                </div>
              </div>
            </div>
          </template>
        </b-modal>

        <!-- End Tab Phone Number Management -->
        <div class="tab-pane" id="lich_su" role="tabpanel">
          <div class="table-responsive height-1350">
            <table class="table table-striped table-content">
              <thead>
                <tr>
                  <th class="kt-font-boldest">Người cập nhật</th>
                  <th class="kt-font-boldest">Thời gian</th>
                  <th class="kt-font-boldest">Hành động</th>
                  <th class="kt-font-boldest">Đối tượng</th>
                  <th class="kt-font-boldest">Tên lớp</th>
                  <th class="kt-font-boldest">Mã môn</th>
                  <th class="kt-font-boldest">Nội dung</th>
                </tr>
              </thead>
              <tbody v-if="checkLoading.histories == true">
                <tr v-for="(item, key) in histories">
                  <td>{{ item.actor }}</td>
                  <td>{{ item.log_time }}</td>
                  <td>{{ item.action }}</td>
                  <td>{{ item.object_name }}</td>
                  <td>{{ item.group_name }}</td>
                  <td>{{ item.subject_code }}</td>
                  <td>{{ item.description }}</td>
                </tr>
              </tbody>
              <tbody v-else>
                <tr>
                    <td colspan="7" class="text-center">Đang tải dữ liệu....</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import pagination from "./paginate";

const urlAPI = {
  getSchedulesExam: "/api/v1/test-schedule",
};

export default {
  name: "Profile",
  components: {
    pagination,
  },
  props: ["user_code", "user_login", "student"],
  data() {
    return {
      allTabs: [
      { label: "Lịch học", id: "lich_hoc" },
      { label: "Lịch thi", id: "lich_thi" },
      { label: "Điểm danh", id: "diem_danh" },
      { label: "Môn đang học", id: "dang_hoc" },
      { label: "Lịch sử học", id: "lich_su_hoc" },
      { label: "Bảng điểm kỳ", id: "bang_diem_theo_ky" },
      { label: "Điểm tích luỹ", id: "bang_diem" },
      { label: "Kỷ luật", id: "ky_luat_khen_thuong" },
      { label: "Liên lạc", id: "phone-number" },
      { label: "Lịch sử", id: "lich_su" },
    ],
    visibleTabs: [],
    overflowTabs: [],
    dropdownVisible: false,
      days: [
        { name: "7 ngày tới", value: 7 },
        { name: "14 ngày tới", value: 14 },
        { name: "30 ngày tới", value: 30 },
        { name: "60 ngày tới", value: 60 },
        { name: "90 ngày tới", value: 90 },
        { name: "7 ngày trước", value: -7 },
        { name: "14 ngày trước", value: -14 },
        { name: "30 ngày trước", value: -30 },
        { name: "60 ngày trước", value: -60 },
        { name: "90 ngày trước", value: -90 },
      ],
      changePassword : {
        new_password: "",
        new_password2: "",
        check_fail: false,
        show: false,
        data: null,
        style: "",
        text: ""
      },
      checkLoading: {
        schedules: false,
        schedules_exam: false,
        terms: false,
        attendances: false,
        studying: false,
        transfer_result: false,
        grade_point: false,
        histories: false,
        discipline: false,
        sms: false
      },
      day: 7,
      day_exam: 7,
      schedules: [],
      schedules_exam: [],
      user: window.user,
      terms: [],
      term_id: 0,
      term_id_bdtk: 0,
      attendances: [],
      studying: [],
      transfer_result: [],
      grade_point: [],
      discipline: [],
      phoneList: [],
      options_telco: [],
      options_owner_type: [
        {
          value : 0,
          text : "Sinh viên"
        },
        {
          value : 1,
          text : "Phụ huynh"
        }
      ],
      newPhoneNumber: {
        owner_type: 0,
        owner_name: "",
        telco: "viettel",
        phone: null,
      },
      paginationExam: {
        path: "",
        total: 0,
        current_page: 1,
        per_page: 15,
        total_page: 0,
      },
      phoneNumberSelected : {
        id : null,
        telco : null,
        phone : null
      },
      changePhoneNumber: false,
      histories: [],
      overlay: {
        show: false,
        variant: "light",
        opacity: 1,
        blur: "1rem",
        rounded: "sm",
      },

      overlayModalDeactive: {
        show: false,
        variant: "light",
        opacity: 1,
        blur: "1rem",
        rounded: "sm",
      },

      overlayModalActive: {
        show: false,
        variant: "light",
        opacity: 1,
        blur: "1rem",
        rounded: "sm",
      },

      markDetail: {
        user_login: "",
        groupid: "",
        grade_detail: [],
        psubject_code: "",
        psubject_name: "",
        skill_code: "",
        pgroup_name: "",
        grade: "",
        status_info: "",
      },
      loading: false,
      loadingAttendanceTable: false,
      totalOfCredits: 0,
      totalOfPassedCredits: 0,
      totalNumberOfCreditsWaived: 0,
      totalGrades: 0,
      GPA: 0,
      GPATN: 0,
      //attendance
      subjectInTerm: "",
      subjectsInTerm: [],
      listBaseOnFilter: [],
      showModalUpdateAttendance: false,
      attendance_status: "",
      optionsAttendanceStatus: [
        { text: "Absent", value: "Absent" },
        { text: "Present", value: "Present" },
      ],
      planChoiced: {
        subject_name: "",
        subject_code: "",
        plan_day: "",
        id: "",
        status: "",
        activity_id: "",
        group_id: "",
      },
      reasonUpdateAttendance: "",
      currentTermId: "",
    };
  },
  computed: {
    stateReason() {
      return this.reasonUpdateAttendance.length >= 10;
    },
    invalidFeedback() {
      if (this.reasonUpdateAttendance.length > 0) {
        return "Lí do phải dài ít nhất 10 kí tự.";
      }
      return "Hãy viết lí do thay đổi điểm danh.";
    },
    isNewScoringSystem() {
      return this.student?.curriculum?.khoa && parseFloat(this.student.curriculum.khoa) >= 18.3;
    }
  },
  watch: {
    day(day) {
      this.getSchedules();
    },
    day_exam() {
      this.getSchedulesExam();
    },
    term_id() {
      this.getAttendances();
    },
    subjectInTerm(val) {
      this.listBaseOnFilter =
        val === ""
          ? this.attendances
          : this.attendances.filter((e) => e.course_id === val);
    },
  },
  methods: {
    async showModal(userLogin, groupId, study) {
      this.loading = true;
      this.markDetail.user_login = userLogin;
      this.markDetail.groupid = groupId;
      this.markDetail.psubject_code = study.psubject_code;
      this.markDetail.psubject_name = study.psubject_name;
      this.markDetail.pgroup_name = study.pgroup_name;
      this.markDetail.skill_code = study.skill_code;
      this.markDetail.grade = study.grade;
      this.markDetail.status_info = study.status_info;
      this.$refs["my-modal"].show();
      try {
        const markDetailData = await axios.get(
          `/api/v1/grade_point_detail?user_code=${this.user_code}&user_login=${this.user_login}&group_id=${groupId}`
        );

        this.markDetail.grade_detail = Object.values(markDetailData.data);
        this.loading = false;
      } catch (e) {
        console.log(e);
      }
    },
    hideModal() {
      this.$refs["my-modal"].hide();
    },
    showModalUpdateAttendancePopup(attendance, plan, index) {
      this.showModalUpdateAttendance = true;
      this.planChoiced.subject_name = plan.subject_name;
      this.planChoiced.subject_code = plan.subject_code;
      this.planChoiced.plan_day = plan.plan_day;
      this.planChoiced.id = plan.id;
      this.attendance_status = plan.plan_status.value;
      this.planChoiced.status = plan.plan_status.value;
      this.planChoiced.activity_id = attendance.activities[index].id;
      this.planChoiced.group_id = attendance.groupid;
    },
    async handleUpdateAttendance() {
      try {
        if (this.stateReason === false) {
          window.libs.notify("", "Lí do của bạn chưa đủ 10 kí tự");
          this.showModalUpdateAttendance = true;
          return;
        }
        this.loadingAttendanceTable = true;
        const URL = "/api/v1/change_attendance";
        const DATA = {
          group_id: this.planChoiced.group_id,
          user_login: this.user_login,
          comment: this.reasonUpdateAttendance,
          status: this.attendance_status,
          activity_id: this.planChoiced.activity_id,
        };
        await axios.post(URL, DATA);
        this.reasonUpdateAttendance = "";
        this.showModalUpdateAttendance = false;
        await this.getAttendances();
        this.subjectInTerm = '';
        this.loadingAttendanceTable = false;
      } catch (error) {
        console.error("Profile: handleUpdateAttendance");
        this.loadingAttendanceTable = false;
      }
    },
    getSchedules() {
      window.libs.notify("", "Đang tải dữ liệu lịch học");
      axios
        .get(
          `/api/v1/schedules?user_code=${this.user_code}&user_login=${this.user_login}&day=${this.day}`
        )
        .then((response) => {
          this.schedules = response.data;
          this.checkLoading.schedules = true;
        });
    },
    async getSchedulesExam() {
      window.libs.notify("", "Đang tải dữ liệu lịch thi");

      try {
        const response = await axios.get(
          `${urlAPI.getSchedulesExam}?date=${this.day_exam}&student_login=${this.user_login}&page=${this.paginationExam.current_page}`
        );

        this.schedules_exam = response.data;
        this.checkLoading.schedules_exam = true;

        this.paginationExam.current_page = response.meta.current_page;
        this.paginationExam.total = response.meta.total;
        this.paginationExam.total_page = response.meta.last_page;
      } catch (error) {
        console.error("Profile: getSchedulesExam");
      }
    },
    async getTerms() {
      await axios.get(`/api/v1/terms`)
        .then((response) => {
          const currentTerm = response.data.find((e) => {
              const startDay = new Date(e.startday);
              const endDay = new Date(e.endday);
              const currentDate = new Date();
              return currentDate >= startDay && currentDate <= endDay;
          });
          this.term_id = currentTerm.id;
          this.currentTermId = currentTerm.id;
          this.term_id_bdtk = currentTerm.id;
          this.terms = response.data;
          this.getAttendances();
          this.getStudying();
          this.getTransferResult();
        });
    },
    async getAttendances() {
      
      const response = await axios.get(
        `/api/v1/attendances?user_code=${this.user_code}&user_login=${this.user_login}&term_id=${this.term_id}`
      );
      this.subjectsInTerm = [];
      response.data.length > 0 &&
        response.data.map((e) => {
          const item = {
            psubject_name: e.psubject_name,
            psubject_code: e.psubject_code,
            pgroup_name: e.pgroup_name,
            course_id: e.course_id,
            groupid: e.groupid,
          };
          this.subjectsInTerm.push(item);
        });
      this.listBaseOnFilter = response.data;
      this.attendances = response.data;
      this.checkLoading.attendances = true;
    },
    getStudying() {
      axios
        .get(
          `/api/v1/studying?user_code=${this.user_code}&user_login=${this.user_login}`
        )
        .then((response) => {
          this.studying = response.data;
          this.checkLoading.studying = true;
        });
    },
    getTransferResult() {
      axios
        .get(
          `/api/v1/transfer_result?user_code=${this.user_code}&user_login=${this.user_login}`
        )
        .then((response) => {
          this.transfer_result = response.data;
          this.checkLoading.transfer_result = true;
        });
    },
    getGradePoint() {
      this.grade_point.details = [];
      axios
        .get(
          `/api/v1/grade_point?user_code=${this.user_code}&user_login=${this.user_login}`
        )
        .then((response) => {
          this.grade_point = response.data;
          this.checkLoading.grade_point = true;
          let totalOfCredits = 0;
          let totalOfCreditsTN = 0;
          let totalOfPassedCredits = 0;
          let totalOfPassedCreditsTN = 0;
          let totalNumberOfCreditsWaived = 0;
          let subjectDontCalNumCredit = ['VIE103','VIE1033','VIE1031','VIE1032','VIE104', 'VIE111'];
          let totalGrades = 0;
          let totalGradesTN = 0;
          let GPA = 0;
          let GPATN = 0;
          const tttn = [
            "PRO106",
            "PRO109",
            "PRO110",
            "PRO115",
            "PRO116",
            "PRO117",
            "PRO118",
            "PRO119",
            "PRO120",
            "PRO121",
            "PRO122",
          ];
          

          for (let i = 0; i < response.data.details.length; i++) {
            const element = response.data.details[i];
            let num_of_credit_subject = parseInt(element.num_of_credit);
            totalOfCredits += num_of_credit_subject;

            if (parseInt(element.status) !== 0 && parseInt(element.status) !== -2) {
                if(parseInt(element.type) !== 2 && parseInt(element.type) !== 3) {
                    if (!tttn.includes(element.subject_code) && !subjectDontCalNumCredit.includes(element.subject_code)) {
                        if(!subjectDontCalNumCredit.includes(element.subject_code)) {
                          totalOfPassedCreditsTN += num_of_credit_subject;
                          totalGradesTN += parseFloat((num_of_credit_subject * element.point).toPrecision(8));
                        }
                        
                        totalOfPassedCredits += num_of_credit_subject;
                        totalGrades += parseFloat((num_of_credit_subject * element.point).toPrecision(8));
                    } 
                }  else if (parseInt(element.type) === 2) {
                    totalNumberOfCreditsWaived += num_of_credit_subject;
                }
            }
          }

          if (totalGrades != 0) {
            GPA = this.round(totalGrades / totalOfPassedCredits, 1);
          } else {
            GPA = 0;
          }
          
          if (totalGradesTN != 0) {
            GPATN = this.round(totalGradesTN / totalOfPassedCreditsTN, 1);
          } else {
            GPATN = 0;
          }
          
          this.totalOfCredits = totalOfCredits;
          this.totalOfPassedCredits = totalOfPassedCredits;
          this.totalNumberOfCreditsWaived = totalNumberOfCreditsWaived;
          this.GPA = GPA;
          this.GPATN = GPATN;
        });
    },
    getDiscipline() {
      axios
        .get(
          `/api/v1/discipline?user_code=${this.user_code}&user_login=${this.user_login}`
        )
        .then((response) => {
          this.discipline = response.data;
          this.checkLoading.discipline = true;
        });
    },

    async getListPhoneNumber() {
      this.overlay.show = true;

      await axios
        .get(
          `/api/v1/profile/sms/getListPhoneNumber?user_code=${this.user_code}&user_login=${this.user_login}`
        )
        .then((res) => {
          this.phoneList = res.lists;
          this.options_telco = res.options_telco;
          this.overlay.show = false;
        })
        .catch((err) => {
          this.overlay.show = false;
        });
    },
    btnEditPhone(item) {
      this.phoneNumberSelected = item;
      $("#phoneEditor").modal('show');
    },
    
    convertScaleGPAPoint4(point) {
      if (point >= 9.0) {
            return 4.0;
      } else if (point >=  8.5 && point < 9) {
          return 3.75;
      } else if (point >=  8.0 && point < 8.5) {
          return 3.5;
      } else if (point >=  7.5 && point < 8.0) {
          return 3.25;
      } else if (point >=  7.0 && point < 7.5) {
          return 3.0;
      } else if (point >=  6.5 && point < 7.0) {
          return 2.75;
      } else if (point >=  6.0 && point < 6.5) {
          return 2.5;
      } else if (point >=  5.5 && point < 6.0) {
          return 2;
      } else if (point >=  5.0 && point < 5.5) {
          return 1.75;
      } else if (point >=  4.5 && point < 5.0) {
          return 1.25;
      } else if (point >=  4.0 && point < 4.5) {
          return 1;
      } else if (point < 4.0) {
          return 0;
      }
    },
    
    convertNumToTextGPAPoint(point) {
      let res = null;
      // &nbsp; là dấu cách để căn nhìn cho thẳng hàng
      if (point >= 9.0) {
          res = 'A+';
      } else if (point >=  8.5 && point < 9) {
          res = 'A';
      } else if (point >=  8.0 && point < 8.5) {
          res = 'A&#8722;';
      } else if (point >=  7.5 && point < 8.0) {
          res = 'B+';
      } else if (point >=  7.0 && point < 7.5) {
          res = 'B';
      } else if (point >=  6.5 && point < 7.0) {
          res = 'B&#8722;';
      } else if (point >=  6.0 && point < 6.5) {
          res = 'C+';
      } else if (point >=  5.5 && point < 6.0) {
          res = 'C';
      } else if (point >=  5.0 && point < 5.5) {
          res = 'C&#8722;';
      } else if (point >=  4.5 && point < 5.0) {
          res = 'D+';
      } else if (point >=  4.0 && point < 4.5) {
          res = 'D';
      } else if ( point < 4.0) {
          res = 'F';
      }

      return res;
    },
    
    openModalAddPhone() {

    },
    onClickChangeStatus(item){
      if(item.is_active == true) {
        if (confirm("Bạn muốn kích hoạt số điện thoại này?") == true) {
          this.changeStatusPhoneNumber(item);
        } else {
          item.is_active = false;
        }
      } else {
        if (confirm("Bạn muốn vô hiệu hoá số điện thoại này?") == true) {
          this.changeStatusPhoneNumber(item);
        } else {
          item.is_active = true;
        }
      }
    },
    async changeStatusPhoneNumber(changeData){
      this.overlay.show = true;

      const URL = "/api/v1/profile/sms/changeStatusPhoneNumber";
      let DATA = { id: changeData.id };

      await axios.post(URL, DATA)
        .then((res) => {
          alert(res.message)
          this.overlay.show = false;
          this.getListPhoneNumber();
          this.getHistory();
        })
        .catch((err) => {
          alert(err.message)
          this.getListPhoneNumber();
          this.overlay.show = false;
        });
    },
    async saveChangePhoneNumber(changeData) {
      this.overlayModalActive.show = true;

      const URL = "/api/v1/profile/sms/changePhoneNumber";
      let DATA = {
        id: changeData.id,
        telco: changeData.telco,
        phone_number: changeData.phone
      };

      await axios.post(URL, DATA)
        .then((res) => {
          alert(res.message)
          this.onClosePhoneNumberEditor();
          this.getListPhoneNumber();
          this.getHistory();
          this.overlayModalActive.show = false;
        })
        .catch((err) => {
          alert(err.message)
          this.overlayModalActive.show = false;
        });
    },

    async saveNewPhoneNumber() {
      this.overlayModalActive.show = true;

      const URL = "/api/v1/profile/sms/saveNewPhoneNumber";
      let DATA = {
        student_code: this.user_code,
        owner_type: this.newPhoneNumber.owner_type,
        owner_name: this.newPhoneNumber.owner_name,
        telco: this.newPhoneNumber.telco,
        phone: this.newPhoneNumber.phone
      };
      let check  = this.validDataNewPhoneNumber(DATA);
      if(!check) {
        this.overlayModalActive.show = false;
        return;
      }
      await axios.post(URL, DATA)
        .then((res) => {
          alert(res.message)
          $("#modalAddPhoneNumber").modal("hide");
          this.overlayModalActive.show = false;
          this.getListPhoneNumber();
          this.getHistory();
        })
        .catch((err) => {
          alert(err.message)
          this.overlayModalActive.show = false;
        });
    },
    validDataNewPhoneNumber(data) {
      if(!data.student_code || data.owner_name.student_code == 0){
        alert('Lỗi! Vui lòng load lại trang.')
        return false;
      }
      if(!data.owner_name || data.owner_name.length == 0 ){
        alert('Tên chủ tài khoản không hợp lệ!')
        return false;
      }
      if(!data.phone || data.phone.length == 0){
        alert('Số điện thoại mới không hợp lệ!')
        return false;
      }
      if(data.owner_type == null){
        alert('Loại tài khoản không hợp lệ!')
        return false;
      }
      return true;
    },
    onCloseAddPhoneNumber() {
      $('#modalAddPhoneNumber').modal('hide');
    },
    clearModalAddPhoneNumber() {
      this.newPhoneNumber.owner_name = "";
      this.newPhoneNumber.owner_type = null;
      this.newPhoneNumber.phone = null;
      this.newPhoneNumber.telco = "";
    },
    onClosePhoneNumberEditor() {
      this.phoneNumberSelected.id = null;
      this.phoneNumberSelected.telco = null;
      this.phoneNumberSelected.phone = null;
      this.changePhoneNumber = false;
      $("#phoneEditor").modal('hide');
    },
    getHistory() {
      axios
        .get(
          `/api/v1/history?user_code=${this.user_code}&user_login=${this.user_login}`
        )
        .then((response) => {
          this.histories = response.data;
          this.checkLoading.histories = true;
        });
    },

    onClickSearch() {
      this.getHistory();
    },

    getCurrentPageExam(value) {
      if (value !== this.paginationExam.currentPage) {
        this.paginationExam.currentPage = value;

        console.log("Call api pagination exam change");
      }
    },
    redirectToGroup(groupID) {
      window.open(`/admin/group/edit/${groupID}`);
    },
    round(value, precision) {
        var multiplier = Math.pow(10, precision || 0);
        return Math.round(value * multiplier) / multiplier;
    },
    validateSDT(telephone) {
      const isValid = /^[0-9]{10}$/.test(telephone);
      return isValid ? true : false;
    },
  },
  created() {
    this.getSchedulesExam();
    this.getTerms();
    this.getSchedules();
    this.getGradePoint();
    this.getDiscipline();
    // this.getSms();
    this.getListPhoneNumber();
    this.getHistory();
  },
};
</script>

<style lang="scss" scoped>
.pointer-td {
  cursor: pointer;
}
.pointer-td:hover {
  color: #28a745;
}
.active-status-btn {
  min-width: 120px;
  color: white;
}

.active-status-text {
  color: white;
  font-weight: bold;
}

.edit-active-status-btn {
  min-width: 95px;
  color: white;
  font-weight: bold;
  background-color: #7366ff;
}

.text-active {
  color: #28a745;
}

.text-deactive {
  color: #dc3545;
}

.text-underline {
  text-decoration: underline;
}

.text-bold {
  font-weight: bold;
}
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid #e5e7eb; /* Màu viền bảng */
  border-radius: 10px;
  overflow: hidden;
  font-size: 14px;
}

.table thead {
  background-color: #f5f9ff; /* Màu header */
  font-weight: 600;
  color: #111827;
}

.table thead th {
  padding: 12px;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
}

.table tbody td {
  padding: 12px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

/* Nền xen kẽ các dòng */
.table tbody tr:nth-child(even) {
  background-color: #f5faff; /* xanh nhạt */
}

/* Làm bo góc phần header */
.table thead th:first-child {
  display: flex;
  border-top-left-radius: 10px;
}

.table thead th:last-child {
  border-top-right-radius: 10px;
}
.nav-tabs {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  overflow: hidden;
}
// .nav-tabs.nav-tabs-line .nav-item{
//   margin: 0 !important;
// }

// .tabs-wrapper {
//   width: 100%;
//   overflow: hidden;
//   border-bottom: 1px solid #ccc;
// }
// .tab-list {
//   display: flex;
//   flex-wrap: nowrap;
//   padding: 0;
//   margin: 0;
//   list-style: none;
// }
// .tab-item {
//   position: relative;
// }
// .tab-btn {
//   padding: 6px 12px;
//   white-space: nowrap;
//   background: none;
//   border: none;
//   border-bottom: 2px solid transparent;
//   cursor: pointer;
// }
// .tab-btn:hover {
//   border-bottom: 2px solid #007bff;
// }


// /* Đảm bảo dropdown hiển thị đúng */
// .nav-item.dropdown {
//   position: relative;
// }

// .dropdown-menu1 {
//   display: none;
//   // position: absolute;
//   // top: 100%;
//   // right: 0;
//   // z-index: 1000;
//   // float: left;
//   // min-width: 10rem;
//   // padding: 0.5rem 0;
//   // margin: 0.125rem 0 0;
//   // font-size: 1rem;
//   // text-align: left;
//   // list-style: none;
//   // background-color: #fff;
//   // background-clip: padding-box;
//   // border: 1px solid rgba(0, 0, 0, 0.15);
//   // border-radius: 0.25rem;
//   // box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
// }

// /* Khi dropdownVisible = true, thêm class "show" để hiển thị */
// .dropdown-menu1.show {
//   display: block;
// }


.nav-tabs .nav-item {
  flex: 1; 
  text-align: center;
}

.nav-tabs .nav-link {
  white-space: normal;
  word-break: break-word; 
  padding: 0.75rem 0.5rem;
  font-size: 12px;
  line-height: 1.2;
}
.nav-item .nav-link {
  color: #6b7280 !important;
}
.nav-item .nav-link.active {
  color: #2671fa !important;
}
.nav-item .nav-link:hover {
  color: #5d78ff !important;
}

// .form-group .form-control{
//     background-color: white;
//     border: 1px solid #DDDDDD;
//     color: #333;
//     margin: 0.5rem auto;
//     border-radius: 6px;
//     max-width: 200px;
//     font-size: 0.9rem;
//     gap: 0.5rem;
// }
.form-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px; 
}

.form-container .form-group {
  width: 50% ;
  max-width: 300px;
  display: flex;
  align-items: center;

}

.form-container .form-group .title {
  font-weight: 500;
  margin-bottom: 4px;
  margin-right: 4px;
  width: 100px;
  font-size: 14px;      
  color: #6b7280;        

}
.form-container .form-group .form-control {
  background-color: white;
  border: 1px solid #DDDDDD;
  color: #333;
  margin: 0.5rem auto;
  height: 40px;
  border-radius: 6px;
  font-size: 0.9rem;
  gap: 0.5rem;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  padding-right: 35px; 
  padding-left: 10px;
  width: 100%;
  height: 36px;
  font-size: 14px;
  }
  .select-wrapper {
    position: relative;
    display: inline-block;
    width: 200px; 
  }
  .custom-arrow {
    position: absolute;
    top: 50%;
    right: 10px; 
    transform: translateY(-50%);
    pointer-events: none; 
    color: #BBBBBB;
  }
  .table-responsive {
    width: 100%;     
    overflow: auto; 
  }
  .height-1350 {
    height: 1350px;
  }
  .table-body-wrapper {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: left;
    white-space: nowrap;
  }
</style>
