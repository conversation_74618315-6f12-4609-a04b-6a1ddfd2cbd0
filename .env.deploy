APP_NAME="CRM"
APP_ENV=local
APP_KEY=base64:Vwj859nrsU+XTCrRMwnAJOhRyEnamU+rKfg2iy1cdUs=
APP_DEBUG=false
APP_URL=http://localhost:8000/
MIX_APP_URL=http://localhost:8000/
EMAIL_ADMIN=<EMAIL>

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=*************
DB_PORT=3306
DB_DATABASE=qldt
DB_USERNAME=root
DB_PASSWORD=admin123

BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=linhhngch18077
MAIL_PASSWORD=hdudgawmdtechfwh--test
MAIL_ENCRYPTION=TLS

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=6bTWefBZs0s+OyJ2VJoM3lQqLELa4AnuCppPzuKb
AWS_DEFAULT_REGION=ap-southeast-1
AWS_BUCKET=crm-uni
AWS_URL=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
MIX_AP_URL=
THEME_ADMIN=admin_v1

GOOGLE_CLIENT_ID=84465911873-p2b1q325ac15t7j2fa7grq62tdh2sj5l.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT=http://localhost:8000/login/google/callback

LOG_SLACK_WEBHOOK_URL=*****************************************************************************
LOG_DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/951371638673408040/9GsdlVo_WYY8DZQpwzDF9rxdAYH6M4X8bYFvdl8bfaAYN7BInOXKexbHGoPlLPIDlU6y
EMAIL_SUPPORT=

LARAVEL_ECHO_SERVER_REDIS_HOST=127.0.0.1
LARAVEL_ECHO_SERVER_REDIS_PORT=6379

COST_PER_CREDIT=427000J
TELESCOPE_ENABLED=false

DNG_URL=
VUE_APP_DNG_URL=
DNG_API_CODE=CRMApi
DNG_ACCESS_CODE=NleEWfq3YhZi7RmVhw1o5s4AIXKDorbbgHbF5e6pwkZ6ZU9sXvmzKOsvrN79EWOGgXbzPR2c
DNG_HASH_KEY=AL3UC8LXYsQ3eLACpQnqi7UKFsZbTJMgEk99WH3tciklfVCEwJk9DK5D2rojiJepPKy5NBQS
FEE_URL_API=
MIX_AP_URL=http://localhost:8080
