<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Route;

use Carbon\Carbon;
use Barryvdh\DomPDF\Facade as PDF;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\Graduation\InBangDiem;
use App\Http\Controllers\Admin\EOSController;
use App\Http\Controllers\Admin\FeeController;
use App\Http\Controllers\Admin\AreaController;
use App\Http\Controllers\Admin\RoomController;
use App\Http\Controllers\Admin\SlotController;
use App\Http\Controllers\Admin\TermController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\PopupController;
use App\Http\Controllers\Admin\GroupController;
use App\Http\Controllers\Admin\ExportController;
use App\Http\Controllers\Admin\EducateController;
use App\Http\Controllers\Admin\SubjectController;
use App\Http\Controllers\Admin\ActivityController;
use App\Http\Controllers\Admin\AdministrativeClassController;
use App\Http\Controllers\Admin\CalendarController;
use App\Http\Controllers\Admin\CourseController;
use App\Http\Controllers\Admin\CurriculumController;
use App\Http\Controllers\Admin\DecisionController;
use App\Http\Controllers\Admin\DecisionUserController;
use App\Http\Controllers\Admin\DiplomaController;
use App\Http\Controllers\Admin\FeedbackController;
use App\Http\Controllers\Admin\GDQPController;
use App\Http\Controllers\Admin\GraduateController;
use App\Http\Controllers\Admin\ScheduleController;
use App\Http\Controllers\Admin\NewFeedbackController;
use App\Http\Controllers\Admin\GraduationController;
use App\Http\Controllers\Admin\TranscriptController;
use App\Http\Controllers\Admin\SmsByTemplateController;
use App\Http\Controllers\Admin\SmsRoleController;
use App\Http\Controllers\Admin\LectureLessonController;
use App\Http\Controllers\Admin\LectureTeacherController;
use App\Http\Controllers\Admin\MajorController;
use App\Http\Controllers\Admin\NewsletterStudentController;
use App\Http\Controllers\Admin\StudentWarningController;
use App\Http\Controllers\Admin\SubjectUpdateAbleController;
use App\Http\Controllers\Admin\SubstituteSubjectsController;
use App\Http\Controllers\Admin\SurveyController;
use App\Http\Controllers\Admin\SyllabusController;
use App\Http\Controllers\Admin\SystemController;
use App\Http\Controllers\Api\OtherManageController;
use App\Http\Controllers\Teacher\AttendanceController;
use App\Http\Controllers\Admin\WalletController;
use App\Http\Controllers\Admin\DebtController;
use App\Http\Controllers\Admin\FeeTuitionPeriodController;

Route::get('/', 'Admin\HomeController@home')->name('admin.home');
Route::get('/cropper', 'Admin\HomeController@cropper')->name('admin.cropper');
Route::get('/user', 'Admin\UserController@index')->name('admin.user');
Route::get('/user/edit/{id}', 'Admin\UserController@edit')->name('admin.user.edit');
Route::post('/user/store', 'Admin\UserController@store')->name('admin.user.store');
Route::get('/user/lock/{id}', 'Admin\UserController@lockUser')->name('admin.user.lock');
Route::post('/user/reset-password', 'Admin\UserController@resetPassword')->name('admin.user.reset_password');
Route::get('/user/lign-as/{id}', 'Admin\UserController@loginToUser')->name('admin.user.login_as');
Route::get('/user/export', 'Admin\UserController@export')->name('admin.user.export');
Route::get('/user/new-export', 'Admin\UserController@newExport')->name('admin.user.new_export');
Route::get('/user/count_status', 'Api\UserApiController@countStatus')->name('api.count_status');
Route::post('/user/change-password', 'Admin\UserController@changePassword')->name('admin.user.change_password');
Route::get('/user/print_form', 'Admin\UserController@print_form')->name('admin.user.print_form');
Route::post('/user/print_result', 'Admin\UserController@print_result')->name('admin.user.print_result');

Route::get('/user/roles', 'Admin\RoleController@index')->name('admin.role');
Route::get('/user/role/add', 'Admin\RoleController@add')->name('admin.role.add');
Route::get('/user/role/edit/{id}', 'Admin\RoleController@edit')->name('admin.role.edit');
Route::post('/user/role/store', 'Admin\RoleController@store')->name('admin.role.store');
Route::get('/user/role/user/delete', 'Admin\RoleController@deleteUser')->name('admin.role.user.delete');

Route::get('/permissions', 'Admin\PermissionController@index')->name('admin.permission');
Route::post('/permission/store', 'Admin\PermissionController@store')->name('admin.permission.store');
Route::get('/permission/edit/{id}', 'Admin\PermissionController@edit')->name('admin.permission.edit');
Route::get('/permission/delete', 'Admin\PermissionController@delete')->name('admin.permission.delete');


// ------------------------------ Tinh hinh di hoc ------------------------------------------
Route::view('/tinh_hinh_di_hoc', 'admin_v1.tinh_hinh_di_hoc.index')->name('tinh_hinh_di_hoc');
// ------------------------------ Quản lý tin nhắn ------------------------------------------
Route::get('/sms/dashboard', [SmsByTemplateController::class, 'index'])->name('admin.sms.index');
Route::get('/sms/role', [SmsRoleController::class, 'index'])->name('admin.sms.role');
Route::get('/sms/format-sms', [SmsByTemplateController::class, 'viewFormatSms'])->name('admin.sms.format_sms');
Route::get('/sms/student-warning', [StudentWarningController::class, 'index'])->name('admin.sms.student_warning');

Route::view('/retest/plan', 'admin_v1.online_service_v2.retest.plan_view')->name('admin.retest.plan_view');
Route::view('/retest/plan/create', 'admin_v1.online_service_v2.retest.plan_create')->name('admin.retest.plan_create');
Route::view('/retest/student', 'admin_v1.online_service_v2.retest.student')->name('admin.retest.student');
Route::view('/retest/subjects', 'admin_v1.online_service_v2.retest.subjects')->name('admin.retest.subjects');
Route::view('/retest/plan', 'admin_v1.online_service_v2.retest.plan_view')->name('admin.retest.plan_view');
Route::view('/retest/plan/create', 'admin_v1.online_service_v2.retest.plan_create')->name('admin.retest.plan_create');
Route::view('/retest/student', 'admin_v1.online_service_v2.retest.student')->name('admin.retest.student');
Route::view('/retest/subjects', 'admin_v1.online_service_v2.retest.subjects')->name('admin.retest.subjects');

// start-------------------------------- Quản lý học lại  ------------------------------------------
Route::view('/relearn/student', 'admin_v1.online_service_v2.relearn.student')->name('admin.relearn.student');

// end---------------------------------- Quản lý học lại  ------------------------------------------

Route::view('/online_service_v2/fake-attendance', 'admin_v1.test.fake_attendance')->name('admin.test.fake_attendance');
// });

Route::get('/online_service', 'Admin\OnlineServiceController@index')->name('admin.online_service');
Route::post('/online_service/store', 'Admin\OnlineServiceController@store')->name('admin.online_service.store');
Route::post('/online_service/process', 'Admin\OnlineServiceController@processOrder')->name('admin.online_service.process');
Route::get('/online_service/getAllOrder', 'Admin\OnlineServiceController@getAllOrder')->name('admin.online_service.orders');
Route::get('/online_service/edit/{id}', 'Admin\OnlineServiceController@edit')->name('admin.online_service.edit');
Route::get('/online_service/orders', 'Admin\OnlineServiceController@orders')->name('admin.online_service.order');
Route::get('/online_service/order/edit/{id}', 'Admin\OnlineServiceController@orderEdit')->name('admin.online_service.order.edit');

Route::get('/online_service/relearn/export-list-student', 'Admin\OnlineServiceController@newExport')->name('admin.relearn.export');

Route::get('/feedback', 'Admin\FeedbackController@index')->name('admin.feedback');
Route::get('/feedback/create', 'Admin\NewFeedbackController@createFeedbackGroup')->name('admin.feedback.create');
Route::post('/feedback/store', 'Admin\NewFeedbackController@storeFeedbackTemplate')->name('admin.feedback.store');
Route::get('/feedback/get_group_by_term', 'Admin\FeedbackController@getGroupByTerm')->name('admin.feedback.get_group_by_term');
Route::get('/feedback/get_block_name_by_term', 'Admin\FeedbackController@getBlockNameByTerm')->name('admin.feedback.get_block_name_by_term');
Route::get('/feedback/get_feedback', 'Admin\FeedbackController@getFeedback')->name('admin.feedback.get_feedback');
Route::get('/feedback/view/{id}', 'Admin\FeedbackController@viewFeedback')->name('admin.feedback.view');
Route::post('/feedback/update_status', 'Admin\FeedbackController@updateStatusById')->name('admin.feedback.update_status');
Route::post('/feedback/update_hit', 'Admin\FeedbackController@updateHitById')->name('admin.feedback.update_hit');
Route::post('/feedback/sync_all_current_class', 'Admin\FeedbackController@synAllCurrentClass')->name('admin.feedback.sync_all_current_class');

Route::get('/dropout', 'Admin\DropOutController@index')->name('admin.dropout');
Route::post('/dropout/store', 'Admin\DropOutController@store')->name('admin.dropout.store');
Route::get('/dropout/list', 'Admin\DropOutController@listDropOut')->name('admin.dropout.list');
Route::get('/dropout/list/{id}', 'Admin\DropOutController@viewDropOut')->name('admin.dropout.view');

Route::get('/discipline', 'Admin\DisciplineController@index')->name('admin.discipline');
Route::get('/discipline/{id}', 'Admin\DisciplineController@viewDiscipline')->name('admin.discipline.view');
Route::get('/discipline/qd/create', 'Admin\DisciplineController@create')->name('admin.discipline.create');
Route::post('/discipline/store', 'Admin\DisciplineController@store')->name('admin.discipline.store');

Route::get('/discipline_exam_regulation', 'Admin\DisciplineExamRegulationController@index')->name('admin.discipline_exam_regulation');
Route::get('/discipline_exam_regulation/qd/create', 'Admin\DisciplineExamRegulationController@create')->name('admin.discipline_exam_regulation.create');
Route::post('/discipline_exam_regulation/store', 'Admin\DisciplineExamRegulationController@store')->name('admin.discipline_exam_regulation.store');
Route::post('/discipline_exam_regulation/update/{id}', 'Admin\DisciplineExamRegulationController@update')->name('admin.discipline_exam_regulation.update');
Route::get('/discipline_exam_regulation/export/{id}', 'Admin\DisciplineExamRegulationController@export')->name('admin.discipline_exam_regulation.export');
Route::get('/discipline_exam_regulation/{id}', 'Admin\DisciplineExamRegulationController@show')->name('admin.discipline_exam_regulation.view');

Route::get('/fee/export', 'Admin\FeeController@export')->name('admin.fee.export');
Route::post('/fee/export/post', 'Admin\FeeController@exportPost')->name('admin.fee.export.post');

Route::post('/queue/get_by_type', 'Admin\QueueController@getQueueByType')->name('admin.queue.get_by_type');

Route::get('/export', function () {
    return view('admin_v1.feedback.test');
});
Route::post('/export', 'Admin\FeedbackController@test')->name('admin.feedback.test');
Route::get('/system/course_result/check', 'Admin\SystemController@checkEmptySkillCode')->name('system.course_result.check');
Route::get('/system/course_result/double', 'Admin\SystemController@checkDoubleCourseResult')->name('system.course_result.double');

Route::get('edu/export/xml', [EducateController::class, 'exportCSVInfoPoint'])->name('export.grade.xml');
Route::get('edu/export/xml2', [EducateController::class, 'exportCSVInfoPoint2'])->name('export.grade.xml2');
Route::get('edu/FUGE/index', [EducateController::class, 'indexGradeFUGE'])->name('edu.fuge.index');
Route::post('edu/FUGE/import', [EducateController::class, 'importGradeFUGE'])->name('edu.fuge.import');

Route::prefix('administrative')->name('admin.administrative.')->group(function () {
    Route::get('export-fee-detail', 'Admin\AdministrativeController@exportDateFee')->name('export_fee_detail');
    Route::get('export-fee-detail-count', 'Admin\AdministrativeController@exportDateFeeCount')->name('export_fee_detail_count');
    Route::get('export-fee-detail-ajax', 'Admin\AdministrativeController@exportDateFeeAjax')->name('export_fee_detail_ajax');


    Route::get('manager-fee-mail', 'Admin\AdministrativeController@exportDateFee')->name('manager_fee_mail');
});

Route::post('/graduation/export/bang_diem_doi_soat', 'Admin\GraduationController@exportBangDiemDoiSoat')->name('admin.graduation.export.bang_diem_doi_soat');


Route::name('admin.')->group(function () {
    Route::get('profile', [UserController::class, 'profile'])->name('profile');
    Route::prefix('data')->name('data.')->group(function () {
        Route::get('area/all', [AreaController::class, 'all'])->name('area.all');
        Route::get('slot/all', [SlotController::class, 'all'])->name('slot.all');
        Route::get('room/getRoomByArea/{id}', [RoomController::class, 'getRoomByArea'])->name('room.getRoomByArea');
        Route::get('subject/getSubjectOnline', [SubjectController::class, 'getSubjectOnline'])->name('subject.getSubjectOnline');
        Route::get('subject/getCourseAjax', [SubjectController::class, 'getCourseAjax'])->name('subject.get_course_ajax');
        Route::get('subject/getSubjectsAjax', [SubjectController::class, 'getSubjectsAjax'])->name('subject.get_subject_ajax');
        Route::get('term/all', [TermController::class, 'all'])->name('term.all');
        Route::get('activity/checkActivityAvailable', [ActivityController::class, 'checkActivityAvailable'])->name('activity.checkActivityAvailable');
    });

    Route::prefix('user')->name('user.')->group(function () {
        Route::get('create', [UserController::class, 'create'])->name('create');
        Route::post('import', [UserController::class, 'importUsers'])->name('import');
        Route::post('createUser', [UserController::class, 'createUser'])->name('create_user');

        Route::get('get-token-login', [UserController::class, 'getTokenLogin'])->name('get_token_login');

        Route::get('manager-pull-user', [UserController::class, 'managerPullUser'])->name('managerPullUser');
        Route::get('rcm-pull-datatable', [UserController::class, 'rcmPullDatatable'])->name('rcmPullDatatable');
        Route::get('rcm-export-user', [UserController::class, 'rcmExport'])->name('rcmExport');

        Route::post('store-pull-user', [UserController::class, 'storePullUser'])->name('storePullUser');
        Route::get('detail-pull-user', [UserController::class, 'detailPullUser'])->name('detailPullUser');
        Route::post('update-detail-user', [UserController::class, 'updateDetailsUser'])->name('updateDetailsUser');
        Route::post('update-pull-user', [UserController::class, 'updatePullUser'])->name('updatePullUser');
        Route::post('update-pull-user-no-id', [UserController::class, 'updatePullUserNoId'])->name('updatePullUserNoId');
    });
    Route::prefix('system_log')->name('system_log.')->group(function () {
        Route::get('/datatable', [SystemController::class, 'historyDataTable'])->name('datatable');
    });

    Route::prefix('survey')->name('survey.')->group(function () {
        Route::get('manager-survey', [SurveyController::class, 'managerSurvey'])->name('managerSurvey');
        Route::get('detail-survey', [SurveyController::class, 'detailSurvey'])->name('detailSurvey');
        Route::get('survey-datatable', [SurveyController::class, 'surveyDatatable'])->name('surveyDatatable');
        Route::get('survey-detail-datatable', [SurveyController::class, 'surveyDetailDatatable'])->name('surveyDetailDatatable');
        Route::post('store-survey', [SurveyController::class, 'storeSurvey'])->name('storeSurvey');
        Route::post('update-survey', [SurveyController::class, 'updateSurvey'])->name('updateSurvey');
        Route::post('change-survey-status', [SurveyController::class, 'changeStatusSurvey'])->name('changeStatusSurvey');
        Route::post('sync-student', [UserController::class, 'syncStudent'])->name('syncStudent');
    });

    Route::prefix('transcript')->name('transcript.')->group(function () {
        Route::get('getByUserLogin/{user_login}', [TranscriptController::class, 'getByUserLogin'])->name('getByUserLogin');
        Route::get('export-not-learn-old-semester', [TranscriptController::class, 'exportNotLearnOldSemester'])->name('exportNotLearnOldSemester');
        Route::get('syncFromCourseResult/{user_login}', [TranscriptController::class, 'syncFromCourseResult'])->name('syncFromCourseResult');
        Route::get('exportPdfBangDiemTichLuy/{user_login}', [TranscriptController::class, 'exportPdfBangDiemTichLuy'])->name('exportPdfBangDiemTichLuy');
    });


    Route::prefix('graduation')->name('graduation.')->group(function () {
        Route::get('bang_diem_doi_soat', [GraduationController::class, 'bangDiemDoiSoat'])->name('bang_diem_doi_soat');
        Route::get('in_bang_diem', [GraduationController::class, 'inBangDiem'])->name('in_bang_diem');
        Route::get('export/so_goc/{campaign_id}', [GraduationController::class, 'exportSoGoc'])->name('exportSoGoc');
        Route::get('campaign', [GraduationController::class, 'campaign'])->name('campaign');
        Route::get('campaign/detail/{campaign_id}', [GraduationController::class, 'campaignDetail'])->name('campaign.detail');
        Route::get('campaign/create', [GraduationController::class, 'campaignCreate'])->name('campaign.create');
        Route::post('campaign/store', [GraduationController::class, 'campaignStore'])->name('campaign.store');
        Route::post('campaign/createTranscipt', [GraduationController::class, 'createTranscipt'])->name('campaign.createTranscipt');
        Route::post('campaign/addDecision', [GraduationController::class, 'addDecision'])->name('campaign.add_decision');
        Route::post('campaign/downRankGraduateStudent', [GraduationController::class, 'downRankGraduateStudent'])->name('campaign.down_rank_graduate');
        Route::get('campaign/find_student/{campaign_id}', [GraduationController::class, 'campaignFindStudent'])->name('campaign.find_student');
        Route::get('export/tot_nghiep', [GraduationController::class, 'exportTotNghiep'])->name('export');
        Route::get('export/tot_nghiep/all', [GraduationController::class, 'exportTotNghiepAll'])->name('export.all');
        Route::get('export/tot_nghiep/bang_diem_doi_soat/all', [GraduationController::class, 'exportBangDiemDoiSoatAll'])->name('export.bang_diem_doi_soat.all');

        Route::post('campaign/create-new', [GraduationController::class, 'createCampaign'])->name('campaign.create_new');
        Route::get('export/tot_nghiep/bang_diem_excel', [GraduationController::class, 'exportBangDiemExcel'])->name('export.bang_diem_excel');
        // Route::post('update-decision', [GraduationController::class,'exportBangDiemDoiSoatAll'])->name('update_decision');

    });
    Route::view('fee/subject_manage', 'admin_v1.fees.subject_managerment')->name('fee.subject_manage');
    Route::view('fee/tuition_period', 'admin_v1.fees.tuition_period')->name('fee.tuition_period');

    Route::prefix('fee')->name('fee.')->group(function () {
        Route::get('/', [FeeController::class, 'feeManager'])->name('manager');
        Route::get('/fee_types', 'Admin\FeeController@feeTypes')->name('fee_types.index');
        Route::get('transaction', [FeeController::class, 'transaction'])->name('transaction');
        Route::get('transaction/{id}', [FeeController::class, 'transactionForm'])->name('transaction.form');
        Route::post('transaction/confirm', [FeeController::class, 'transactionConfirm'])->name('transaction.confirm');
        Route::get('import', [FeeController::class, 'importForm'])->name('import');
        Route::post('import/store', [FeeController::class, 'importStore'])->name('import.store');
        Route::post('create-new-wallet', [FeeController::class, 'createFeeForNewUser'])->name('createFeeForNewUser');
        Route::get('transaction/import/form', [FeeController::class, 'importConfirmForm'])->name('transaction.import.form');
        Route::post('transaction/import/store', [FeeController::class, 'importConfirmStore'])->name('transaction.import.store');

        // mail fee
        Route::prefix('feemail')->name('feemail.')->group(function () {
            Route::get('/', [FeeController::class, 'indexFeeMail'])->name('index');
            Route::get('/import-from', [FeeController::class, 'indexFeeMail'])->name('import_from');
            Route::get('/detail/{id}', [FeeController::class, 'detailFeeMail'])->name('detail');
            Route::get('/feeMail-datatable', [FeeController::class, 'datatableFeeMail'])->name('datatable');
            Route::post('create', [FeeController::class, 'createTermFeeMail'])->name('create');
            Route::post('update', [FeeController::class, 'updateTermFeeMail'])->name('update');
            // Route::get('update', [FeeController::class,'updateTermFeeMail'])->name('update');
            Route::get('export', [FeeController::class, 'exportFeeMail'])->name('export');
            Route::get('export-english', [FeeController::class, 'exportEnglishFee'])->name('exportEnglishFee');
            Route::post('send-mail', [FeeController::class, 'sendMailCurrentTerm'])->name('send_mail');
            Route::post('send-mail-by-id', [FeeController::class, 'sendMailCurrentId'])->name('send_mail_id');
            Route::post('send-mail-test', [FeeController::class, 'sendMailTest'])->name('send_mail_test');
            Route::post('sync-fee-mail', [FeeController::class, 'syncFeeMail'])->name('sync_fee_mail');
            Route::post('check_english_fee', [FeeController::class, 'checkEnglishAgain'])->name('check_english_fee');
            // Route::post('send-mail-test', [FeeController::class,'sendMailCurrentTerm'])->name('send_mail');

            Route::post('upload-feeMail', [FeeController::class, 'uploadFeeMailStudent'])->name('upload_feemail');
        });
    });


    Route::prefix('edu')->name('edu.')->group(function () {
        Route::get('thieu_no_mon', [EducateController::class, 'thieuNoMon'])->name('thieu_no_mon');
        Route::get('getExportThieuNoMon', [EducateController::class, 'getExportThieuNoMon'])->name('getExportThieuNoMon');

        Route::middleware('permission')->group(function () {
            Route::get('so_diem', [EducateController::class, 'soDiem'])->name('so_diem');
            Route::get('import', [EducateController::class, 'importForm'])->name('import.form');
            Route::get('export', [EducateController::class, 'exportForm'])->name('export.form');
            Route::post('export/download', [EducateController::class, 'exportDownload'])->name('export.download');
            Route::get('import/download/example/{course_id?}', [EducateController::class, 'downloadFileImportExample'])->name('import.download.example');
            Route::post('import/store', [EducateController::class, 'importStore'])->name('import.store');
            Route::post('so_diem/store', [EducateController::class, 'soDiemSave'])->name('so_diem.store');
            Route::post('grade_lock/store', [EducateController::class, 'gradeLockStore'])->name('so_diem.grade_lock.store');
            Route::view('gradebook-export-v2', 'admin_v1.edu.gradebook_export_v2')->name('gradebook_export_v2');
        });
    });


    Route::prefix('activity')->name('activity.')->group(function () {
        Route::get('/', [ActivityController::class, 'index'])->name('index');
        Route::get('/export-attendance', [ActivityController::class, 'exportAttendance'])->name('export_attendance');
        Route::get('/config-schedule', [ActivityController::class, 'configSchedule'])->name('config_schedule');
        Route::get('/config-schedule-test', [ActivityController::class, 'configScheduleTest'])->name('config_schedule_test');
        Route::get('/config-group', [ActivityController::class, 'configGroup'])->name('config_group');
        Route::get('/config-room', [ActivityController::class, 'exportConfigRoom'])->name('config_room');
        Route::get('/export-schedule', [ActivityController::class, 'exportSchedule'])->name('export_schedule');
        Route::post('/import', [ActivityController::class, 'import'])->name('import');
        Route::get('/import', [ActivityController::class, 'importForm'])->name('importForm');
        Route::post('/importExam', [ActivityController::class, 'importExam'])->name('importExam');
        Route::get('/importExam', [ActivityController::class, 'importExamForm'])->name('importExamForm');
        Route::post('/import-new', [ActivityController::class, 'importnewSchedule'])->name('import_new');
        Route::get('/import-new', [ActivityController::class, 'importNewForm'])->name('group.import');
        Route::get('/lich_thi_giua_ky_cuoi_ky', [ActivityController::class, 'lich_thi_giua_ky_cuoi_ky'])->name('lich_thi_giua_ky_cuoi_ky');
        Route::get('/danh_sach_thi', [ActivityController::class, 'danh_sach_thi'])->name('danh_sach_thi');
        Route::get('/chi_tiet_hoat_dong', [ActivityController::class, 'chi_tiet_hoat_dong'])->name('chi_tiet_hoat_dong');

        Route::get('/check-passed-subject-by-term', [ActivityController::class, 'checkPassedSubjectByTerm'])->name('check_passed_subject_by_term');


        // template
        Route::get('/download-template', [ActivityController::class, 'downloadTemplate'])->name('download_template');
        Route::get('/download-course-exam', [ActivityController::class, 'downloadCourseExam'])->name('download_course_exam');
        Route::get('/download-exam-class-list', [ActivityController::class, 'downloadExamClassList'])->name('download_exam_class_list');
    });

    Route::prefix('change-scheduled')->name('changeScheduled.')->group(function () {
        Route::view('index', 'admin_v1.schedule.teacher-change-scheduled.index')->name('index');
    });

    // Điểm danh
    Route::prefix('/attendance')->name('attendance.')->group(function () {
        Route::get('{id}', [AttendanceController::class, 'getGroup'])->name('get_group');
        Route::post('add', [AttendanceController::class, 'createAttendance'])->name('add');
        Route::post('update', [AttendanceController::class, 'updateAttendance'])->name('update');
    });

    Route::prefix('check_feedback_block')->name('check_feedback_block.')->group(function () {
        Route::get('/', [FeedbackController::class, 'searchGroup'])->name('getGroup');
        Route::post('/', [FeedbackController::class, 'checkFeedbackBlock'])->name('getGroup');
        Route::get('/datatable', [FeedbackController::class, 'datatableFeedbackBlock'])->name('datatable');
    });

    Route::prefix('syllabus')->name('syllabus.')->group(function () {
        Route::get('/export-hau-kiem-lich-trinh', [SyllabusController::class, 'export1'])->name('export_hau_kiem_lich_trinh');
        Route::get('/export-hau-kiem-dau-diem', [SyllabusController::class, 'export2'])->name('export_hau_kiem_dau_diem');
    });

    Route::prefix('subject')->name('subject.')->group(function () {
        Route::post('/export-hau-kiem-lich-trinh', [SubjectController::class, 'updateNumberStudent'])->name('update_mumber_student');
    });

    Route::prefix('group')->name('group.')->group(function () {
        Route::get('/', [GroupController::class, 'index'])->name('index');
        Route::get('/create', [GroupController::class, 'create'])->name('index.create');
        Route::get('/edit/{id}', [GroupController::class, 'edit'])->name('index.edit');
        Route::post('/store', [GroupController::class, 'store'])->name('index.store');
        Route::post('/delete', [GroupController::class, 'delete'])->name('index.delete');
        Route::get('/member/delete/{id}', [GroupController::class, 'deleteMember'])->name('member.delete');
        Route::post('/activity/store', [GroupController::class, 'storeActivity'])->name('activity.store');
        Route::post('/activity/store-new', [GroupController::class, 'storeActivityNew'])->name('activity.storeNew');
        Route::get('/get_course', [GroupController::class, 'getCourse'])->name('get_course');
        Route::get('/get_block', [GroupController::class, 'getBlocksByTerm'])->name('get_block');
        Route::get('/manager_students', [GroupController::class, 'managerStudents'])->name('manager_students');
        Route::get('/suitable_schedule', [GroupController::class, 'suitableSchedule'])->name('suitable_schedule');
        Route::get('/manager_schedule', [GroupController::class, 'managerSchedule'])->name('manager_schedule');
        Route::get('/export', [GroupController::class, 'export'])->name('export_list');
        Route::post('/sync-info', [GroupController::class, 'syncInfo'])->name('sync_info');

        // Dev
        Route::get('/datatable', [GroupController::class, 'datatable'])->name('datatable');
        Route::post('/remove-student-file', [GroupController::class, 'removeStudentFile'])->name('remove_student_file');
        Route::post('/add-student-file', [GroupController::class, 'addStudentFile'])->name('add_student_file');
        Route::post('/add-student-no-check', [GroupController::class, 'updateScheduleNoCheck'])->name('add_student_no_check');
        Route::get('/change-schedule', [GroupController::class, 'changeSchedule'])->name('change_schedule');

        Route::get('/change-schedule/check-swap-group', [GroupController::class, 'checkSwapGroup'])->name('check_swap_group');
        Route::post('/change-schedule/swap-group', [GroupController::class, 'swapGroup'])->name('swap_group');
        Route::get('/change-schedule/list-group-can-join', [GroupController::class, 'getListClassCanJoin'])->name('list_group_can_join');
        Route::post('/change-schedule/move-to-group', [GroupController::class, 'moveToGroup'])->name('move_to_group');
        Route::get('/get-group-members', [GroupController::class, 'getGroupMembers'])->name('get_group_members');
        Route::get('/get-staff-process-log', [GroupController::class, 'getStaffProcessLog'])->name('get_staft_process_log');
    });

    Route::prefix('schedule')->name('schedule.')->group(function () {
        Route::get('schedule-mangement', [ScheduleController::class, 'scheduleMangement'])->name('schedule_mangement');
        Route::get('schedule-mangement-datatable', [ScheduleController::class, 'scheduleMangementDatatable'])->name('schedule_mangement.datatable');
        Route::post('update-time-change-group', [ScheduleController::class, 'updateTimeChangeGroup'])->name('updateTimeChangeGroup');
    });

    Route::prefix('export')->name('export.')->group(function () {
        Route::get('/luot-hoc-chi-tiet', [ExportController::class, 'DetailedLessons'])->name('detailed_lessons');
        Route::get('/get/luot-hoc-chi-tiet', [ExportController::class, 'exportDetailedLessons'])->name('export.detailed_lessons');
        Route::get('/get/danh-sach-lop-mon', [ExportController::class, 'exportDetailGroup'])->name('export.detail_group');
        Route::get('/get/form-english-detail', [ExportController::class, 'GetFormDataEnglish'])->name('form_english_detail');
        Route::get('/get/english-detail', [ExportController::class, 'getDataEnglish'])->name('english_detail');
        Route::prefix('schedule')->name('schedule.')->group(function () {
            Route::get('/', [ScheduleController::class, 'indexDataMakeSchedule'])->name('show');
            Route::get('/download-template', [ScheduleController::class, 'downloadTemplate'])->name('download_template');
            Route::get('/getData/{type}', [ScheduleController::class, 'exportDataMakeSchedule'])->name('export');
        });
    });


    Route::prefix('decision')->name('decision.')->group(function () {
        Route::get('/', [DecisionController::class, 'index'])->name('index');
        Route::get('/datatable', [DecisionController::class, 'datatable'])->name('datatable');
        Route::get('/export', [DecisionController::class, 'exportData'])->name('export');
        Route::get('/loadSelect2Decision', [DecisionController::class, 'loadSelect2Decision'])->name('loadSelect2Decision');
        Route::get('/edit/{id}', [DecisionController::class, 'edit'])->name('edit');
        Route::post('/update/{id}', [DecisionController::class, 'update'])->name('update');
        Route::post('/destroy', [DecisionController::class, 'destroy'])->name('destroy');
        Route::post('/remove-member', [DecisionController::class, 'removeUser'])->name('remove_user');
        Route::get('/create', [DecisionController::class, 'create'])->name('create');
        Route::post('/store', [DecisionController::class, 'store'])->name('store');
        Route::get('/export-decision-user', [DecisionController::class, 'exportDecision'])->name('exportDecision');
    });

    Route::prefix('decision_user')->name('decision_user.')->group(function () {
        Route::get('/datatable', [DecisionUserController::class, 'datatable'])->name('datatable');
    });

    // Route::prefix('gpa')->name('gpa.')->group(function() {
    //     Route::get('/', [DecisionController::class,'index'])->name('index');
    //     Route::get('/detail', [DecisionController::class,'datatable'])->name('datatable');
    //     Route::get('/export', [DecisionController::class,'exportData'])->name('export');
    // });

    // Route::prefix('calendar')->name('calendar.')->group(function () {
    //     Route::get('/lich_bao_ve', [CalendarController::class, 'lich_bao_ve'])->name('lich_bao_ve');
    //     Route::post('/get_activity', [CalendarController::class, 'getActivity'])->name('get_activity');
    //     Route::post('/update_lich_bao_ve', [CalendarController::class, 'updateActivity'])->name('update_lich_bao_ve');
    //     Route::get('/danh_sach_bao_ve', [CalendarController::class, 'danh_sach_bao_ve'])->name('danh_sach_bao_ve');
    //     Route::get('/export_danh_sach_bao_ve', [CalendarController::class, 'export_danh_sach_bao_ve'])->name('export_danh_sach_bao_ve');
    // });


    Route::prefix('calendar')->name('calendar.')->group(function () {
        Route::get('/check_php', [CalendarController::class, 'check_php'])->name('check_php');
        Route::get('/test_export', [CalendarController::class, 'test_export'])->name('test_export');
        Route::get('/danh_sach_thi', [CalendarController::class, 'danh_sach_thi'])->name('danh_sach_thi');
        Route::get('/export_danh_sach_thi', [CalendarController::class, 'export_danh_sach_thi'])->name('export_danh_sach_thi');
        Route::get('/export_danh_sach_thi_new', [CalendarController::class, 'export_danh_sach_thi_new'])->name('export_danh_sach_thi_new');
        Route::get('/export_groups_exam_detail', [CalendarController::class, 'exportGroupsExamDetail'])->name('export_groups_exam_detail');
        Route::get('/datatable', [CalendarController::class, 'datatable'])->name('datatable');
        Route::get('/sync-graduate-calendar', [CalendarController::class, 'syncGraduateCalendar'])->name('sync_graduate_calendar');
    });


    Route::prefix('graduate')->name('graduate.')->group(function () {
        Route::get('/export_danh_sach_sinh_vien', [GraduateController::class, 'export_danh_sach_sinh_vien'])->name('export_danh_sach_sinh_vien');
        Route::get('/process_export_danh_sach_sinh_vien', [GraduateController::class, 'process_export_danh_sach_sinh_vien'])->name('process_export_danh_sach_sinh_vien');

        Route::get('/xep_lich_bao_ve_sinh_vien', [GraduateController::class, 'xep_lich_bao_ve_sinh_vien'])->name('xep_lich_bao_ve_sinh_vien');
        Route::post('/process_import_lich_bao_ve_sinh_vien', [GraduateController::class, 'process_import_lich_bao_ve_sinh_vien'])->name('process_import_lich_bao_ve_sinh_vien');

        Route::get('/export_danh_sach_giam_thi', [GraduateController::class, 'export_danh_sach_giam_thi'])->name('export_danh_sach_giam_thi');
        Route::get('/process_export_danh_sach_giam_thi', [GraduateController::class, 'process_export_danh_sach_giam_thi'])->name('process_export_danh_sach_giam_thi');

        Route::get('/xep_giam_thi', [GraduateController::class, 'xep_giam_thi'])->name('xep_giam_thi');
        Route::post('/process_import_xep_giam_thi', [GraduateController::class, 'process_import_xep_giam_thi'])->name('process_import_xep_giam_thi');
    });

    Route::prefix('eos')->name('eos.')->group(function () {
        Route::get('/export_danh_sach_sinh_vien_eos', [EOSController::class, 'export_danh_sach_sinh_vien_eos'])->name('export_danh_sach_sinh_vien_eos');
        Route::get('/process_export_danh_sach_sinh_vien_eos', [EOSController::class, 'process_export_danh_sach_sinh_vien_eos'])->name('process_export_danh_sach_sinh_vien_eos');
        Route::get('/xep_lich_thi_eos', [EOSController::class, 'xep_lich_thi_eos'])->name('xep_lich_thi_eos');
        Route::post('/process_import_xep_lich_thi_eos', [EOSController::class, 'process_import_xep_lich_thi_eos'])->name('process_import_xep_lich_thi_eos');

        Route::get('/export_danh_sach_giam_thi', [EOSController::class, 'export_danh_sach_giam_thi'])->name('export_danh_sach_giam_thi');
        Route::get('/process_export_danh_sach_giam_thi', [EOSController::class, 'process_export_danh_sach_giam_thi'])->name('process_export_danh_sach_giam_thi');

        Route::get('/xep_giam_thi', [EOSController::class, 'xep_giam_thi'])->name('xep_giam_thi');
        Route::post('/process_import_xep_giam_thi', [EOSController::class, 'process_import_xep_giam_thi'])->name('process_import_xep_giam_thi');

        Route::get('/export_lich_thi_eos', [EOSController::class, 'export_lich_thi_eos'])->name('export_lich_thi_eos');
        Route::get('/process_export_lich_thi_eos', [EOSController::class, 'process_export_lich_thi_eos'])->name('process_export_lich_thi_eos');

        Route::get('/export_danh_sach_thi_new', [EOSController::class, 'export_danh_sach_thi_new'])->name('export_danh_sach_thi_new');

        Route::get('/datatable', [EOSController::class, 'datatable'])->name('datatable');
        Route::get('/syncGraduateEos', [EOSController::class, 'syncGraduateEos'])->name('syncGraduateEos');
    });



    Route::prefix('feedback')->name('feedback.')->group(function () {
        Route::get('/roles', [FeedbackController::class, 'feedbackRole'])->name('roles');
        Route::get('/role/{id}', [FeedbackController::class, 'feedbackRoleEdit'])->name('role.edit');
        Route::post('/role/store', [FeedbackController::class, 'feedbackRoleStore'])->name('role.semesterstore');
        Route::post('/update-Feedback', [FeedbackController::class, 'updateFeedback'])->name('update_feedback');
        // Route::get('/', [DecisionController::class,'index'])->name('index');
        Route::get('/datatable', [FeedbackController::class, 'datatable'])->name('datatable');
        Route::get('/export', [NewFeedbackController::class, 'exportFeedback'])->name('export');
        Route::get('/export_term', [NewFeedbackController::class, 'exportFeedbackTerm'])->name('export_term');
    });

    Route::prefix('popup')->name('popup.')->group(function () {
        Route::get('/', [PopupController::class, 'index'])->name('index');
        Route::post('/created', [PopupController::class, 'store'])->name('created');
        Route::get('/edit/{id}', [PopupController::class, 'edit'])->name('edit');
        Route::delete('/delete/{id}', [PopupController::class, 'delete'])->name('delete');
        Route::get('/getlistPopup', [PopupController::class, 'getlistPopup'])->name('getlistPopup');
    });

    Route::prefix('lecture')->name('lecture.')->group(function () {
        Route::get('/lesson', [LectureLessonController::class, 'indexlectureLesson'])->name('indexlectureLesson');
        Route::get('/teacher', [LectureTeacherController::class, 'indexlectureTeacher'])->name('indexlectureTeacher');
    });

    Route::prefix('other-manage')->name('other_manage.')->group(function () {
        Route::view('/area', 'admin_v1.other_manage.area')->name('area');
        Route::view('/room', 'admin_v1.other_manage.room')->name('room');
        Route::view('/check-room', 'admin_v1.other_manage.check_room')->name('check_room');
        Route::view('/term', 'admin_v1.other_manage.term')->name('term');
        Route::view('/slot', 'admin_v1.other_manage.slot')->name('slot');
        Route::view('/khoa-nhap-hoc', 'admin_v1.other_manage.khoa_nhap_hoc')->name('khoa_nhap_hoc');
        Route::post('/term/create', [OtherManageController::class, 'createTerm']);
        Route::post('/save-term-change', [OtherManageController::class, 'saveTermChange']);
    });

    Route::prefix('student-helper')->name('student_helper.')->group(function () {
        Route::view('/category', 'admin_v1.student_helper.category')->name('category');
        Route::view('/qna', 'admin_v1.student_helper.qna')->name('qna');
    });

    Route::prefix('newsletter_student')->name('newsletter_student.')->group(function () {
        Route::get('/', [NewsletterStudentController::class, 'indexNewsletterStudent'])->name('indexNewsletterStudent');
        Route::post('/createEdit', [NewsletterStudentController::class, 'createEditNewsletterStudent'])->name('createEditNewsletterStudent');
        Route::delete('/delete/{id}', [NewsletterStudentController::class, 'deleteNewsletterStudent'])->name('deleteNewsletterStudent');
        Route::put('/updatestate/{id}', [NewsletterStudentController::class, 'updateStateNewsletterStudent'])->name('updateStateNewsletterStudent');
    });

    Route::prefix('student-status-logs')->name('student-status-logs.')->group(function () {
        Route::view('/', 'admin_v1.student_status_logs.index')->name('index');
    });

    Route::prefix('scan_student')->name('scan_student.')->group(function () {
        Route::view('/', 'admin_v1.scan_student.index')->name('index');
    });

    Route::prefix('training_frame')->name('training_frame.')->group(function () {
        Route::view('/brands', 'admin_v1.training_frame.brands')->name('brands');
        Route::view('/departments', 'admin_v1.training_frame.departments')->name('departments');
        Route::view('/list_frame', 'admin_v1.training_frame.list_frame')->name('list_frame');
        Route::view('/create_frame', 'admin_v1.training_frame.create_frame')->name('create_frame');
        Route::view('/detail_frame', 'admin_v1.training_frame.detail_frame')->name('detail_frame');
        Route::view('/list_student_frame', 'admin_v1.training_frame.list_student_frame')->name('list_student_frame');
    });
    Route::prefix('remove_student')->name('remove_student.')->group(function () {
        Route::view('/remove_student_30', 'admin_v1.remove_student.remove_student_30')->name('remove_student_30');
        Route::view('/remove_student_ap', 'admin_v1.remove_student.remove_student_ap')->name('remove_student_ap');
    });

    Route::prefix('scanning_student')->name('scanning_student.')->group(function () {
        Route::view('/', 'admin_v1.scanning_student.index')->name('index');
        Route::view('import_fee', 'admin_v1.scanning_student.import_fee')->name('import_fee');
    });

    Route::prefix('prerequisite_subjects')->name('prerequisite_subjects.')->group(function () {
        Route::view('/', 'admin_v1.prerequisite_subjects.index')->name('index');
    });
    Route::prefix('syllabus')->name('syllabus.')->group(function () {
        Route::view('/list', 'admin_v1.syllabus.list')->name('list');
        Route::view('/detail', 'admin_v1.syllabus.detail')->name('detail');
    });
    Route::prefix('substitute_subject_matrix')->name('substitute_subject_matrix.')->group(function () {
        Route::view('/list', 'admin_v1.substitute_subject_matrix.list')->name('list');
    });
    Route::prefix('subject_exemptions')->name('subject_exemptions.')->group(function () {
        Route::view('/list', 'admin_v1.subject_exemptions.list')->name('list');
    });
    Route::prefix('course_management')->name('course_management.')->group(function () {
        Route::view('/list', 'admin_v1.course_management.index')->name('index');
        Route::view('/create', 'admin_v1.course_management.create')->name('create');
    });
    Route::prefix('alternative_subject_student')->name('alternative_subject_student.')->group(function () {
        Route::view('/list', 'admin_v1.alternative_subject_student.index')->name('index');
    });
    Route::prefix('/grade_view')->name('grade_view.')->group(function () {
        Route::get('/', [EducateController::class, 'gradeView'])->name('index');
        Route::get('/bang_diem_ky', [EducateController::class, 'bangDiemKyView'])->name('bangdiemky_view');
        Route::get('bangdiemky', [EducateController::class, 'bangdiemky'])->name('bangdiemky');
        Route::put('/chot-ky', [EducateController::class, 'chotKy'])->name('chotKy');
        Route::get('/grade-by-term', [EducateController::class, 'gradeByTerm'])->name('grade-by-term');
        Route::get('/grade-by-term-export', [EducateController::class, 'gradeByTermExport'])->name('grade-by-term-export');
        Route::get('/index', [EducateController::class, 'showGradeReport'])->name('admin.grade_view.index');
    });

    // management_course
    Route::prefix('/course')->name('course.')->group(function () {
        Route::get('/', [CourseController::class, 'index'])->name('index');
        Route::get('/datatable', [CourseController::class, 'datatable'])->name('datatable');
        Route::post('/store', [CourseController::class, 'store'])->name('store');
        Route::post('/import', [CourseController::class, 'import'])->name('import');
        Route::get('/edit/{id}', [CourseController::class, 'edit'])->name('edit');
        Route::put('/update/{id}', [CourseController::class, 'update'])->name('update');
    });

    // Quản lý GDQP
    Route::prefix('/gdqp')->name('gdqp.')->group(function () {
        Route::get('/', [GDQPController::class, 'index'])->name('index');
        Route::get('/datatable', [GDQPController::class, 'datatable'])->name('datatable');
        Route::get('/export', [GDQPController::class, 'export'])->name('export');
        Route::post('/import', [GDQPController::class, 'import'])->name('import');
    });

    Route::prefix('subject_update_able')->name('subject_update_able.')->group(function () {
        Route::get('/', [SubjectUpdateAbleController::class, 'index'])->name('index');
        Route::get('/datatable', [SubjectUpdateAbleController::class, 'datatable'])->name('datatable');
        Route::get('/loadSelectSubject', [SubjectUpdateAbleController::class, 'loadSelectSubject'])->name('loadSelectSubject');

        // Route::get('/edit/{id}', [SubjectUpdateAbleController::class,'edit'])->name('edit');
        Route::get('/edit/{old_subject_code}', [SubjectUpdateAbleController::class, 'edit'])->name('edit');
        Route::post('/update/{old_subject_code}', [SubjectUpdateAbleController::class, 'update'])->name('update');
        Route::post('/remove-subject', [SubjectUpdateAbleController::class, 'removeSubjectAble'])->name('removeSubjectAble');
        Route::get('/create', [SubjectUpdateAbleController::class, 'create'])->name('create');
        Route::post('/store', [SubjectUpdateAbleController::class, 'store'])->name('store');

        Route::post("/import", [SubjectUpdateAbleController::class, 'import'])->name('import');
    });

    // Route thay thế môn cho sinh viên
    Route::prefix('thay-the-mon-sv')->name('substitute_subjects.')->group(function () {

        Route::get('/', [SubstituteSubjectsController::class, 'index'])->name('index');
        Route::get('/datatable', [SubstituteSubjectsController::class, 'datatable'])->name('datatable');
        Route::get('/import/file-mau', [SubstituteSubjectsController::class, 'downloadFileImport'])->name('file_mau');
        Route::post('/import', [SubstituteSubjectsController::class, 'import'])->name('import');
        Route::delete('/delete/{id}', [SubstituteSubjectsController::class, 'destroy'])->name('delete');
        Route::get('/get-subject-by-student', [SubstituteSubjectsController::class, 'getSubjectCodeByStudent'])->name('getSubjectCodeByStudent');

        Route::post('/import-mien-giam', [SubstituteSubjectsController::class, 'importMienGiam'])->name('importMienGiam');
    });

    // Quan ly van bang
    Route::prefix('quan-ly-van-bang')->name('diploma.')->group(function () {
        Route::get('/', [DiplomaController::class, 'index'])->name('index');
        Route::post('/updateSoBang', [DiplomaController::class, 'update'])->name('updateSoBang');
        Route::get('/datatable', [DiplomaController::class, 'datatable'])->name('datatable');
        Route::post('/import', [DiplomaController::class, 'import'])->name('import');
        Route::get('/export', [DiplomaController::class, 'export'])->name('export');
    });

    // Quản lý tài chính sinh viên
    Route::group(['prefix' => 'wallet'], function () {
        Route::get('/', [WalletController::class, 'listWallets'])->name('admin.wallet.list');
        Route::get('/create', [WalletController::class, 'createWalletForm'])->name('admin.wallet.create.form');
        Route::post('/create', [WalletController::class, 'createWallet'])->name('admin.wallet.create');
        Route::get('/{id}/lock', [WalletController::class, 'lockWalletForm'])->name('admin.wallet.lock.form');
        Route::post('/{id}/lock', [WalletController::class, 'lockWallet'])->name('admin.wallet.lock');
        Route::post('/{id}/unlock', [WalletController::class, 'unlockWallet'])->name('admin.wallet.unlock');
        Route::get('/{id}/deposit', [WalletController::class, 'depositForm'])->name('admin.wallet.deposit.form');
        Route::post('/{id}/deposit', [WalletController::class, 'deposit'])->name('admin.wallet.deposit');
        Route::get('/{id}/transactions', [WalletController::class, 'transactions'])->name('admin.wallet.transactions');
        Route::get('/export', [WalletController::class, 'exportWallets'])->name('admin.wallet.export');

        // Bulk actions
        Route::post('/bulk/lock', [WalletController::class, 'bulkLockWallets'])->name('admin.wallet.bulk.lock');
        Route::post('/bulk/unlock', [WalletController::class, 'bulkUnlockWallets'])->name('admin.wallet.bulk.unlock');

        // Auto create wallet
        Route::post('/auto-create', [WalletController::class, 'autoCreateWallet'])->name('admin.wallet.auto.create');

        // Import functionality
        Route::get('/import/wallets', [WalletController::class, 'importWalletsForm'])->name('admin.wallet.import.wallets.form');
        Route::get('/import/deposits', [WalletController::class, 'importDepositsForm'])->name('admin.wallet.import.deposits.form');
        Route::get('/import/template', [WalletController::class, 'downloadImportTemplate'])->name('admin.wallet.import.template');
        Route::post('/import/process', [WalletController::class, 'processBulkImport'])->name('admin.wallet.import.process');
    });

    // Quản lý công nợ sinh viên
    Route::prefix('debt')->name('debt.')->group(function () {
        Route::get('/', [DebtController::class, 'index'])->name('list');
        Route::get('/list', [DebtController::class, 'index']); // Alias for /admin/debt/list
        Route::get('/export', [DebtController::class, 'export'])->name('export');
        Route::get('/{id}/detail', [DebtController::class, 'detail'])->name('detail');
        Route::post('/{id}/cancel', [DebtController::class, 'cancel'])->name('cancel');
        Route::get('/{id}/payment', [DebtController::class, 'paymentForm'])->name('payment.form');
        Route::post('/{id}/payment', [DebtController::class, 'payment'])->name('payment');
    });
    Route::prefix('/administrative_class')->name('administrative_class.')->group(function () {
        Route::get('/', [AdministrativeClassController::class, 'index'])->name('index');
        Route::get('/export', [AdministrativeClassController::class, 'export'])->name('export');
        Route::get('/create', [AdministrativeClassController::class, 'create'])->name('create');
        Route::post('/storage', [AdministrativeClassController::class, 'storage'])->name('storage');
        Route::post('/update', [AdministrativeClassController::class, 'update'])->name('update');
        Route::post('/delete', [AdministrativeClassController::class, 'delete'])->name('delete');
    });
});
Route::get('/train/subject', 'Admin\TrainController@subject')->name('admin.train.subject');
Route::get('/train/curriculum', 'Admin\TrainController@curriculum')->name('admin.train.curriculum');
Route::get('/train/report', 'Admin\TrainController@report')->name('admin.train.report');

Route::get('/training_management/group', 'Admin\TrainingManagementController@group')->name('admin.training_management.group');
Route::get('/training_management/schedule', 'Admin\TrainingManagementController@schedule')->name('admin.training_management.schedule');
Route::get('/training_management/attendance', 'Admin\TrainingManagementController@attendance')->name('admin.training_management.attendance');
Route::get('/training_management/teaching', 'Admin\TrainingManagementController@teaching')->name('admin.training_management.teaching');
Route::get('/training_management/learn_again', 'Admin\TrainingManagementController@learn_again')->name('admin.training_management.learn_again');
Route::get('/training_management/academic', 'Admin\TrainingManagementController@academic')->name('admin.training_management.academic');
Route::get('/training_management/graduate', 'Admin\TrainingManagementController@graduate')->name('admin.training_management.graduate');
Route::get('/training_management/other', 'Admin\TrainingManagementController@other')->name('admin.training_management.other');

Route::get('/exams/test', 'Admin\ExamsController@test')->name('admin.exams.test');
Route::get('/exams/re_test', 'Admin\ExamsController@re_test')->name('admin.exams.re_test');
Route::get('/exams/grades', 'Admin\ExamsController@grades')->name('admin.exams.grades');

Route::get('/online_service/permission_services', 'Admin\OnlineServiceController@permission_services')->name('admin.online_service_v2.permission_services');

Route::prefix('promoted-semester')->group(function () {
    Route::get('/', 'Admin\PromotedSemesterController@index')->name('admin.promted-semester.index');
});

Route::prefix('bhyt')->group(function () {
    Route::view('/', 'admin_v1.bhyt.index')->name('admin.bhyt.index');
    Route::view('/available-periods', 'admin_v1.bhyt.available_periods')->name('admin.bhyt.available_periods');
    Route::view('/available-periods-logs', 'admin_v1.bhyt.available_student_logs')->name('admin.bhyt.available_student_logs');
});

Route::prefix('ebook')->group(function () {
    Route::view('/', 'admin_v1.ebook.index')->name('admin.ebook.index');
});

// Quản lý Curriculum
Route::prefix('/curriculum')->name('curriculum.')->group(function () {
    Route::get('/datatable', [CurriculumController::class, 'datatable'])->name('datatable');
    Route::get('/export', [CurriculumController::class, 'export'])->name('export');
    Route::get('/create', [CurriculumController::class, 'create'])->name('create');
    Route::get('/auto-gen-code', [CurriculumController::class, 'autoGenerateCurriculumCode'])->name('auto_gen_code');
    Route::post('/storage', [CurriculumController::class, 'storage'])->name('storage');
    Route::post('/update', [CurriculumController::class, 'update'])->name('update');
    Route::post('/delete', [CurriculumController::class, 'delete'])->name('delete');
    Route::prefix('/preiod')->name('preiod.')->group(function () {
        Route::get('/export-template', [CurriculumController::class, 'getPeridoSubjectTemplate'])->name('export-template');
        Route::post('/update-period-subject', [CurriculumController::class, 'updatePeriodSubject'])->name('update-period-subject');
    });
});

// Quản lý Tuyển sinh
Route::get('/admission', function () {
    return view('admin_v1.admission.index');
})->name('admin.admission.index');

// Download admission template (web route for direct download)
Route::get('/download-admission-template', 'Admin\AdmissionController@downloadTemplate')->name('admin.admission.download_template');

// Export admission data
Route::get('/export-admission-data', 'Admin\AdmissionController@exportData')->name('admin.admission.export_data');

