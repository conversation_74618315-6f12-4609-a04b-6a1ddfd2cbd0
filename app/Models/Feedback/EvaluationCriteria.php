<?php

namespace App\Models\Feedback;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationCriteria extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'category',
        'weight',
        'max_score',
        'sort_order',
        'is_active',
        'scale_labels'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'scale_labels' => 'array'
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('category')->orderBy('sort_order');
    }

    // Accessors
    public function getCategoryLabelAttribute()
    {
        switch($this->category) {
            case 'teaching':
                return 'Phương pháp giảng dạy';
            case 'knowledge':
                return '<PERSON><PERSON><PERSON> thức chuyên môn';
            case 'attitude':
                return 'Thái độ và tác phong';
            case 'interaction':
                return 'Tương tác với sinh viên';
            case 'organization':
                return 'Tổ chức lớp học';
            case 'technology':
                return 'Sử dụng công nghệ';
            default:
                return 'Khác';
        }
    }

    public function getScaleLabel($score)
    {
        if ($this->scale_labels && isset($this->scale_labels[$score])) {
            return $this->scale_labels[$score];
        }

        // Default scale labels
        switch($score) {
            case 1:
                return 'Rất kém';
            case 2:
                return 'Kém';
            case 3:
                return 'Trung bình';
            case 4:
                return 'Tốt';
            case 5:
                return 'Rất tốt';
            default:
                return 'Không xác định';
        }
    }
}
