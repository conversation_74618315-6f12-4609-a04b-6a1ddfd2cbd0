<?php

namespace App\Models\Feedback;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationPeriod extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'term_id',
        'start_date',
        'end_date',
        'is_active',
        'criteria',
        'created_by'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'settings' => 'array'
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCurrent($query)
    {
        return $query->where('start_date', '<=', now())->where('end_date', '>=', now());
    }

    // Accessors
    public function getStatusAttribute()
    {
        $now = now();
        if ($now < $this->start_date) {
            return 'upcoming';
        } elseif ($now > $this->end_date) {
            return 'ended';
        } else {
            return 'active';
        }
    }

    public function getStatusLabelAttribute()
    {
        switch($this->status) {
            case 'upcoming':
                return 'Sắp mở';
            case 'active':
                return 'Đang mở';
            case 'ended':
                return 'Đã kết thúc';
            default:
                return 'Không xác định';
        }
    }

    public function getDaysRemainingAttribute()
    {
        if ($this->status === 'active') {
            return now()->diffInDays($this->end_date, false);
        }
        return null;
    }
}
