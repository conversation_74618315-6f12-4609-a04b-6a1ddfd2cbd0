<?php

namespace App\Models\Feedback;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationPeriodGroup extends Model
{
    use HasFactory;

    protected $table = 'evaluation_period_group';
    protected $fillable = [
        'period_id',
        'group_id',
    ];
    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }
}
