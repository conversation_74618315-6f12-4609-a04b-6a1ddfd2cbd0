<?php


namespace App\Repositories\Admin;


use App\Exports\Edu\ImportDiem;
use App\Exports\Edu\ThieuNoMon;
use App\Exports\GradeBook\GradeBookExport;
use App\Exports\GradeBook\GradeBookTermExportMain;
use App\Http\Lib;
use App\Imports\DisciplineImport;
use App\Jobs\ExportGradebook;
use App\Jobs\ExportThieuNoMon;
use App\Models\Dra\CurriCulum;
use App\Models\Fu\Course;
use App\Models\Fu\Department;

use App\Models\Fu\Group;
use App\Models\Fu\GroupMember;
use App\Models\Fu\Block;
use App\Models\Fu\Subject;
use App\Models\Fu\Term;
use App\Models\Fu\Activity;
use App\Models\KhoaNhapHoc;
use App\Models\Kithu;
use App\Models\Brand;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\T7\Grade;
use App\Models\T7\GradeGroup;
use App\Models\T7\GradeSyllabus;
use App\Models\T7\SyllabusPlan;
use App\Models\T7\Discipline;
use App\Models\Transcript;
use App\Models\T7\AcademicDecisionManagement;
use App\Models\Dra\StudentSubject;
use App\Models\Fu\ActionLog;
use App\Models\Fu\Decision;
use App\Repositories\BaseRepository;
use App\Models\Fu\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Redirect;
use niklasravnsborg\LaravelPdf\Facades\Pdf as PDF2;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\PhpWord;
use Carbon\Carbon;
use Illuminate\Support\Facades\Response;
use stdClass;

class EducateRepository extends BaseRepository
{
    const DAT = 1;
    const KHONG_DAT = 0;
    const TRUOT_DIEM_DANH = -1;
    const CO_DI_THI = 1;
    const CO_DI_THI_2 = 2;
    const KHONG_DI_THI = 0;
    const ROLE_STUDENT = 3;

    protected $listSubject2Blocks = ['PRO104', 'SOF102', 'SOF301', 'SOF302', 'PRO131', 'SOF102', 'NET104', 'NET105', 'PRO104', 'PRO131', 'PRO101', 'WEB301', 'WEB206', 'PRO101', 'WEB206', 'WEB503', 'PRO101', 'PRO112', 'MOB201', 'MOB306', 'PRO112', 'MOB201', 'MOB306', 'PRO112', 'MOB201', 'PRO111', 'MUL313', 'MUL317', 'PRO111', 'MUL221', 'MUL218', 'MUL317', 'PRO111', 'MUL321', 'MUL219', 'MUL318', 'PRO111', 'TOU204', 'TOU302', 'PRO105', 'TOU103', 'TOU202', 'TOU103', 'TOU202', 'HOS102', 'HOS202', 'HOS104', 'PRO108', 'HOS102', 'HOS202', 'HOS104', 'PRO114', 'PRE206', 'PRE205', 'PRE207', 'MAR207', 'PRO114', 'PRE206', 'PRO102', 'MAR202', 'MAR206', 'BUS204', 'MAR205', 'MAR202', 'BUS204', 'MAR205', 'PRO113', 'DOM202', 'MAR202', 'DOM107', 'PRO113', 'DOM107', 'ENT1125', 'ENT1225', 'ENT2125', 'ENT2225'];

    public function getModel()
    {
        return User::class;
    }

    public function thieuNoMon($request)
    {
        try {
            $export_excel = $request->export_excel;
            $export_excel_new = $request->export_excel_new ?? null;
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', '600');
            ini_set("pcre.backtrack_limit", "20000000");
            DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
            $subject_status = $request->subject_status;
            $keyword = $request->keyword;
            $curriculum_id = $request->curriculum_id ?? null;
            $khoa_hoc = $request->khoa_hoc ?? [] ;
            $major = $request->major ?? null;
            $study_status = config('status')->trang_thai_hoc;
            $transcript_status = config('status')->transcript_status;
            $curriculum = CurriCulum::orderBy('id', 'desc')->get();
            $majors = Brand::select('major')->distinct()->get()->pluck('major')->toArray();
            $ki_thu = Kithu::get();
            $khoa_nhap_hoc = KhoaNhapHoc::get();
            $listSubject = Subject::orderBy('id', 'DESC')->get()->pluck('subject_code')->toArray();
            $curriculum_ids = [];
            $listSubjectReq = $request->list_subject ?? [];

            if ($subject_status == 'all') {
                $subject_status = 0;
            }

            if ($curriculum_id == 'all') {
                $curriculum_id = 0;
            }

            if ($major !== 'all' && $major !== null) {
                $curriculum_ids = CurriCulum::where('nganh', $major)->get()->pluck('id');
            }

            if ($export_excel || $export_excel_new) {
                $transcripts = Transcript::select([
                    'transcripts.id',
                    'transcripts.user_code',
                    'transcripts.user_login',
                    'transcript_details.status',
                    'transcript_details.subject_code_replace',
                    'transcript_details.subject_code',
                    'user.kithu',
                    DB::raw('(CASE
                    WHEN status = "1" THEN "Đã học"
                    WHEN status = "0" THEN "Chưa học"
                    WHEN status = "-1" THEN "Chưa Đạt"
                    WHEN status = "-2" THEN "Đang học"
                    WHEN status = "-3" THEN "Chưa Đạt"
                    WHEN status = "-4" THEN "Thi lại"
                    ELSE "Không xác định"
                    END) AS "status_subject"'),
                ]);
            } else {
                $transcripts = Transcript::select([
                    'transcripts.id',
                    'transcripts.user_code',
                    'transcripts.user_login',
                    'user.kithu'
                ]);
            }

            $transcripts->with(['details' => function ($query) use ($subject_status) {
                $query->when($subject_status, function ($query, $subject_status) {
                    $query->where('status', $subject_status);
                });
            }])
                ->leftJoin('user', 'user.user_login', '=', 'transcripts.user_login')
                ->leftJoin('transcript_details', 'transcript_details.transcript_id', '=', 'transcripts.id');

            if (count($curriculum_ids) > 0) {
                $transcripts->when($curriculum_ids, function ($query, $curriculum_ids) {
                    $query->whereIn('user.curriculum_id', $curriculum_ids);
                });
            } else {
                $transcripts->when($curriculum_id, function ($query, $curriculum_id) {
                    $query->where('user.curriculum_id', $curriculum_id);
                });
            }


            if (count($listSubjectReq) > 0) {
                $transcripts->where(function ($query) use ($listSubjectReq) {
                    $query->where(function ($q) use ($listSubjectReq) {
                        $q->whereIn('transcript_details.subject_code_pass', $listSubjectReq);
                        $q->whereNotNull('transcript_details.subject_code_pass');
                    })
                        ->orWhere(function ($q) use ($listSubjectReq) {
                            $q->whereIn('transcript_details.subject_code', $listSubjectReq);
                            $q->whereNull('transcript_details.subject_code_pass');
                        });
                });
            }
            // truy vấn theo danh sách user_code của sinh viên đã chuyền vào
            $transcripts->when(isset($request->list_user_code_check) && count($request->list_user_code_check) >= 1, function ($query) use ($request) {
                $query->whereIn('transcripts.user_login', $request->list_user_code_check)
                    ->orWhereIn('transcripts.user_code', $request->list_user_code_check);
            });

            $transcripts->when($keyword, function ($query, $keyword) {
                $query->where('transcripts.user_login', 'like', "%$keyword%")
                    ->orWhere('transcripts.user_code', 'like', "%$keyword%");
            })
                ->where('user.user_level', self::ROLE_STUDENT);

            if (count($khoa_hoc) > 1) {
                $transcripts->where(function ($query) use ($khoa_hoc) {
                    foreach ($khoa_hoc as $key => $value) {
                        if ($key == 0) {
                            $query->where('user.grade_create', 'LIKE', ($value . '%'));
                        } else {
                            $query->orWhere('user.grade_create', 'LIKE', ($value . '%'));
                        }
                    }
                });
            }

            $transcripts->groupBy('transcripts.id');
            $transcripts->orderBy('transcripts.id', 'desc');

            if ($export_excel) {
                $transcripts = $transcripts->get();
            } else {
                $transcripts = $transcripts->paginate(20);
            }


            $listStudentWillGraduate = [];
            foreach ($transcripts as $transcript) {
                $chua_dat = [];
                $chua_hoc = [];
                $dang_hoc = [];
                $thi_lai = [];
                $da_hoc = [];
                $mon_cham_ky = [];
                foreach ($transcript->details as $item) {
                    if ($item->status == -1 || $item->status == -3) {
                        $chua_dat[] = (!is_null($item->subject_code_replace)) ? $item->subject_code_replace . ' (' . $item->subject_code . ')' : ($item->subject_code_pass ?? $item->subject_code);
                    } else if ($item->status == 0) {
                        $chua_hoc[] = (!is_null($item->subject_code_replace)) ? $item->subject_code_replace . ' (' . $item->subject_code . ')' : ($item->subject_code_pass ?? $item->subject_code);

                        // lấy ra môn chậm kỳ
                        if($item->ki_thu < $transcript->kithu) {
                            $mon_cham_ky[] = (!is_null($item->subject_code_replace)) ? $item->subject_code_replace . ' (' . $item->subject_code . ')' : ($item->subject_code_pass ?? $item->subject_code);
                        }
                    } else if ($item->status == -2) {
                        $dang_hoc[] = (!is_null($item->subject_code_replace)) ? $item->subject_code_replace . ' (' . $item->subject_code . ')' : ($item->subject_code_pass ?? $item->subject_code);
                    } else if ($item->status == -4) {
                        $thi_lai[] = (!is_null($item->subject_code_replace)) ? $item->subject_code_replace . ' (' . $item->subject_code . ')' : ($item->subject_code_pass ?? $item->subject_code);
                    } else if ($item->status == 1) {
                        $da_hoc[] = (!is_null($item->subject_code_replace)) ? $item->subject_code_replace . ' (' . $item->subject_code . ')' : ($item->subject_code_pass ?? $item->subject_code);
                    }
                }

                $tong_mon_hoc = count($transcript->details);
                $da_hoc_total = count($da_hoc);
                $chua_dat_total = count($chua_dat);
                $chua_hoc_total = count($chua_hoc);
                $dang_hoc_total = count($dang_hoc);
                $thi_lai_total = count($thi_lai);
                $mon_cham_ky_total = count($mon_cham_ky);

                $transcript->tong_mon_hoc = $tong_mon_hoc;
                $transcript->da_hoc = $da_hoc_total ? implode(', ', $da_hoc) : 'Trống';
                $transcript->da_hoc_total = $da_hoc_total;
                $transcript->chua_dat = $chua_dat_total ? implode(', ', $chua_dat) : 'Trống';
                $transcript->chua_dat_total = $chua_dat_total;
                $transcript->chua_hoc = $chua_hoc_total ? implode(', ', $chua_hoc) : 'Trống';
                $transcript->chua_hoc_total = $chua_hoc_total;
                $transcript->dang_hoc = $dang_hoc_total ? implode(', ', $dang_hoc) : 'Trống';
                $transcript->dang_hoc_total = $dang_hoc_total;
                $transcript->thi_lai = $thi_lai_total ? implode(', ', $thi_lai) : 'Trống';
                $transcript->thi_lai_total = $thi_lai_total;
                $transcript->mon_cham_ky = $mon_cham_ky_total ? implode(', ', $mon_cham_ky) : 'Trống';
            }
            // nếu chuyền danh sách sinh viên vào để check sẽ trả về ngay ở đây
            if (isset($request->list_user_code_check) && count($request->list_user_code_check) >= 1) {
                return $listStudentWillGraduate;
            }

            if ($export_excel) {
                // $export = new ThieuNoMon($transcripts);
                // $user_export = $user_login_job == "" ? auth()->user()->user_login : $user_login_job;
                // $name = 'thieu_no_mon_' . date('YmdHis') . "_" . $user_export;
                // return Excel::download($export, "$name.xlsx");
                $file_name = 'danh_sach_sinh_vien_thieu_no_mon.csv';
                $filePath = storage_path('app/' . $file_name);

                // Ghi dữ liệu vào file CSV
                $csvFile = fopen($filePath, 'w');

                // Thêm BOM UTF-8 để tránh lỗi font khi mở bằng Excel
                fprintf($csvFile, chr(0xEF) . chr(0xBB) . chr(0xBF));
                $header = [
                    '#',
                    'Mã sinh viên',
                    'Tổng môn học',
                    'Đã học',
                    'Tổng đã học',
                    'Chưa đạt',
                    'Tổng chưa đạt',
                    'Chưa học',
                    'Môn chậm kỳ',
                    'Tổng chưa học',
                    'Đang học',
                    'Tổng đang học',
                    'Thi lại',
                    'Tổng thi lại',
                ];
                fputcsv($csvFile, $header);
                $count = 0;
                foreach ($transcripts as $key => $line) {
                    fputcsv($csvFile, [
                        $count++,
                        $line['user_code'],
                        $line['tong_mon_hoc'],
                        $line['da_hoc'],
                        $line['da_hoc_total'],
                        $line['chua_dat'],
                        $line['chua_dat_total'],
                        $line['chua_hoc'],
                        $line['mon_cham_ky'],
                        $line['chua_hoc_total'],
                        $line['dang_hoc'],
                        $line['dang_hoc_total'],
                        $line['thi_lai'],
                        $line['thi_lai_total'],
                    ]);
                }
                fclose($csvFile);

                return response()->download($filePath)->deleteFileAfterSend(true);
            }

            return $this->view('edu.thieu_no_mon', [
                'study_status' => $study_status,
                'transcript_status' => $transcript_status,
                'transcripts' => $transcripts,
                'curriculum' => $curriculum,
                'ki_thu' => $ki_thu,
                'majors' => $majors,
                'khoa_nhap_hoc' => $khoa_nhap_hoc,
                'list_subject' => $listSubject,
            ]);
        } catch (\Throwable $th) {
            Log::error($th);
        }
    }

    public function getFileExportThieuNoMon($request)
    {
        try {
            $user_login = auth()->user()->user_login;
            $before = Storage::disk('thieu_no_mon')->allFiles(DIRECTORY_SEPARATOR . 'proactive');
            if (Cache::has('thieu_no_mon')) {
                return redirect()->back()->with('success', 'Tồn tại tiến trình xuất file thiếu nợ môn, vui lòng chờ tiến trình xuất file thiếu nợ môn hoàn thành!');
            } else {
                $objRequest = (object)[
                    'status' => $request['status'],
                    'major' => $request['major'],
                    'khoa_hoc' => $request['khoa_hoc'],
                    'list_subject' => $request['list_subject'],
                    'subject_status' => $request['subject_status'],
                    'keyword' => $request['keyword'],
                    'export_excel' => 1,
                ];
                $dispatchJobExportThieuNoMon = new ExportThieuNoMon($objRequest, $user_login);
                dispatch(($dispatchJobExportThieuNoMon)->onConnection('thieuNoMon-queue')->onQueue('thieuNoMon-queue'));
            }
            $has_started = Cache::has('thieu_no_mon');
            sleep(2);
            if (Cache::has('thieu_no_mon')) {
                return redirect()->back()->with('success', 'Tiến trình xuất file thiếu nợ môn đã được đẩy vào hàng đợi!');
            } else {
                $after = Storage::disk('thieu_no_mon')->allFiles(DIRECTORY_SEPARATOR . 'proactive');
                $diff = array_diff($after, $before);
                if (count($diff) > 0 || $has_started) {
                    return redirect()->back()->with('success', 'Hoàn thành xuất file thiếu nợ môn!');
                } else {
                    return redirect()->back()->with('success', 'Khởi động tiến trình xuất file thiếu nợ môn thất bại!');
                }
            }
            return redirect()->back()->with('success', 'Tiến trình xuất file thiếu nợ môn đã được đẩy vào hàng đợi!');
        } catch (\Throwable $th) {
            Log::error(`getFileExportThieuNoMon error: \n` . $th);
            return response([
                'result' => 0,
                'message' => $th->getMessage() . " / "  . $th->getFile() . " / " .  $th->getLine()
            ], 500);
        }
    }

    public function getListExistedFileThieuNoMon()
    {
        try {
            $exist_proactive = Storage::disk('thieu_no_mon')->allFiles(DIRECTORY_SEPARATOR . 'proactive');
            $exist_proactive = collect($exist_proactive)->sort()->reverse()->toArray();
            $response_exist_proactive = [];
            foreach ($exist_proactive as $file_path) {
                $user_login = explode(".", explode("_", $file_path)[4])[0];
                $date_string = explode(".", explode("_", $file_path)[3])[0];
                $ymd = Carbon::createFromFormat('YmdHis', $date_string)->format('d/m/Y H:i:s');
                $response_exist_proactive[] = [
                    'file_name' => $file_path,
                    'date_time' => $ymd,
                    'user_login' => $user_login,
                ];
            }
            return response([
                'exist_proactive' => $response_exist_proactive,
                'has_queue_export' => Cache::has('thieu_no_mon')
            ], 200);
        } catch (\Throwable $th) {
            Log::error(`getListExistedFileThieuNoMon error: \n` . $th);
            return response([
                'exist_auto' => [],
                'exist_proactive' => [],
                'has_queue_export' => Cache::has('thieu_no_mon')
            ], 500);
        }
    }

    /**
     * tải file bảng điểm từ storage/app/public/thieu_no_mon/
     *
     * @param  mixed $request file_name
     * @return void
     */
    public function getDownloadExistedFileThieuNoMon(Request $request)
    {
        $exist = Storage::disk('thieu_no_mon')->exists($request->file_name);
        if ($exist) {
            return response()->download(storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'thieu_no_mon' .  DIRECTORY_SEPARATOR .  $request->file_name));
        }
        return response()->json([
            'message' => 'File not found',
        ], 404);
    }

    public function soDiem($request)
    {
        try {
            $qrImage = null;
            $data_2fa = [];
            $whoIsUser = Auth::user();
            if (!$whoIsUser->google2fa_secret) {
                $google2fa = app('pragmarx.google2fa');
                $data_2fa = [
                    'company' => 'SYBE_UNI-CRM',
                    'email' => $whoIsUser->user_email,
                    'google2fa_secret' => $google2fa->generateSecretKey(),
                ];
                $qrImage = $google2fa->getQRCodeInline(
                    $data_2fa['company'],
                    $data_2fa['email'],
                    $data_2fa['google2fa_secret']
                );
            }
            $term_id = $request->term_id;
            $department_id = $request->department_id;
            $course_id = $request->course_id;
            $group_id = $request->group_id;
            $code = CourseGrade::select('token')->where('groupid', $group_id)->orderBy('grade_id')->orderBy('id', 'desc')->first();
            $history_status = config('status')->history_status;
            $terms = Term::orderBy('id', 'desc')->get();
            $departments = Department::all();
            $courses = Course::select('psubject_code', 'psubject_name', 'course.id')->join('subject', 'subject.id', 'course.subject_id')
                ->where('subject.department_id', $department_id)
                ->where('term_id', $term_id)->orderBy('psubject_code')
                ->get();
            $groups = Group::where('body_id', $course_id)->where('list_group.is_virtual', 0)
                ->where('pterm_id', $term_id)
                ->where('type', 1)
                ->orderBy('group_name')
                ->get();
            $group = Group::find($group_id);

            if (count($groups) > 0 && $group) {
                $group_grade_id = [];
                $grade_filter = [];
                $grade_temp = [];
                $grade_lock_list = $group->grade_lock_list;
                if ($grade_lock_list != '') {
                    $grade_lock_list = explode(',', $grade_lock_list);
                } else {
                    $grade_lock_list = [];
                }

                $checkReload = false;
                $students = GroupMember::where('groupid', $group->id)
                    ->orderBy('member_login')
                    ->get();
                $group_grades = GradeGroup::query()
                    ->where('syllabus_id', $group->syllabus_id)
                    ->where('subject_id', $group->psubject_id)
                    ->orderBy('id')
                    ->get();
                $grades = Grade::select('id', 'grade_name', 'grade_group_id', 'weight', 'minimum_required', 'is_final_exam', 'bonus_type', 'max_point')
                    ->where('subject_id', $group->psubject_id)
                    ->where('syllabus_id', $group->syllabus_id)
                    ->orderBy('grade_name')
                    ->orderBy('grade_group_id')
                    ->get();

                if (sizeof($group_grades) == 0) {
                    $checkReload = true;
                    GradeGroup::where('subject_id', 0)
                        ->where('syllabus_id', $group->syllabus_id)
                        ->update(['subject_id' => $group->psubject_id]);
                }

                if (sizeof($grades) == 0) {
                    $checkReload = true;
                    Grade::where('subject_id', 0)
                        ->where('syllabus_id', $group->syllabus_id)
                        ->update(['subject_id' => $group->psubject_id]);
                }

                if ($checkReload) {
                    return back();
                }

                // $students = GroupMember::where('groupid', $group->id)->orderBy('member_login')->get();
                // $group_grades = GradeGroup::where('syllabus_id', $group->syllabus_id)->where('subject_id', $group->psubject_id)->orderBy('id')->get();
                // $grades = Grade::select('id','grade_name','grade_group_id','weight','minimum_required','is_final_exam','bonus_type','max_point')->where('subject_id', $group->psubject_id)->where('syllabus_id', $group->syllabus_id)->orderBy('grade_name')->orderBy('grade_group_id')->get();
                $syllabus = GradeSyllabus::find($group->syllabus_id);
                $tb_temp_grade = [];
                foreach ($group_grades as $item) {
                    $group_grade_id[] = $item->id;
                    $tb_temp_grade[$item->id]['weight'] = $item->weight;
                    $tb_temp_grade[$item->id]['tb_grade'] = 0;
                    $tb_temp_grade[$item->id]['minimum_required'] = $item->minimum_required ?? 0;

                    foreach ($grades as $grade) {
                        if ($item->id == $grade->grade_group_id) {
                            $grade_filter[] = $grade;
                        }
                    }
                }
                foreach ($grade_filter as $item) {
                    $item->locked = in_array($item->id, $grade_lock_list) ? 1 : 0;

                    $grade_temp[$item->id] = [
                        'point' => null,
                        'weight' => $item->weight,
                        'minimum_required' => $item->minimum_required,
                        'is_final_exam' => $item->is_final_exam,
                        'grade_group_id' => $item->grade_group_id,
                        'pass' => false,
                        'bonus_type' => $item->bonus_type,
                        'locked' => 0,
                        'max_point' => $item->max_point,
                    ];
                }
                foreach ($students as $student) {
                    $student->load('user');
                    if (!isset($student->user->user_code)) dd($student->user, $student);
                    $student->user_code = $student->user->user_code;
                    $tong = 0;
                    $grades_temp = CourseGrade::where('login', $student->member_login)->where('groupid', $student->groupid)->whereIn('grade_group_id', $group_grade_id)->get();
                    $lich_su = CourseResult::select('val', 'grade')->where('student_login', $student->member_login)->where('groupid', $student->groupid)->where('course_id', $course_id)->where('subject_id', $group->psubject_id)->first();
                    if ($lich_su) {
                        $status_subject = $lich_su->val;
                    } else {
                        $status_subject = 0;
                    }

                    $temp = $grade_temp;
                    // dd($temp);
                    $final_score = 0;
                    $countPointIsNull = count($grades_temp) == count($grade_filter);
                    $danger_point = 0;
                    $idGradeGroup = 0;
                    $tbGrade = 0;
                    foreach ($grades_temp as $item) {

                        if (isset($temp[$item->grade_id])) {
                            $temp[$item->grade_id]['point'] = $item->val;
                            $temp[$item->grade_id]['locked'] = $item->locked;
                            if ($temp[$item->grade_id]['point'] >= $temp[$item->grade_id]['minimum_required']) {
                                $temp[$item->grade_id]['pass'] = true;
                            }

                            // check + diem final 1 hay final 2nd vao tong
                            if ($item->is_final === 1) {
                                if ($item->is_resit < 1) {
                                    $final_score = ($temp[$item->grade_id]['point'] * $temp[$item->grade_id]['weight']) / 100;
                                } else {
                                    if ($temp[$item->grade_id]['point'] !== null) {
                                        $tong -= $final_score;
                                    }
                                }
                            }
                            $tong += ($temp[$item->grade_id]['point'] * $temp[$item->grade_id]['weight']) / 100;
                            // Kiểm tra sinh viên có trượt do nhóm điểm thành phần không
                            if ($status_subject == 0) {
                                // loai điểm, nhóm điểm kết thúc môn, điểm bonus
                                if ($temp[$item->grade_id]['is_final_exam'] == 1) {
                                    if (isset($temp[$item->grade_id]['grade_group_id'])) {
                                        unset($temp[$item->grade_id]['grade_group_id']);
                                    }
                                    continue;
                                }
                                if ($temp[$item->grade_id]['bonus_type'] == 1) {
                                    continue;
                                }
                                // kiểm tra có đầu điểm nào trượt trong nhóm không phải nhóm điểm kết thúc môn
                                if (!$temp[$item->grade_id]['pass']) {
                                    $danger_point++;
                                }

                                $idGradeGroup = $item['grade_group_id'];
                                if ($tb_temp_grade[$idGradeGroup]['weight'] != 0) {
                                    $tbGrade += floatval($temp[$item->grade_id]['point']) * ($temp[$item->grade_id]['weight'] / $tb_temp_grade[$idGradeGroup]['weight']);
                                }
                                $tb_temp_grade[$item->id]['tb_grade'] = 0;
                            }
                        }
                    }
                    if ($idGradeGroup != 0) {
                        if ($tbGrade < $tb_temp_grade[$idGradeGroup]['minimum_required']) {
                            $danger_point++;
                        }
                        if ($danger_point > 0) {
                            $status_subject = -2;
                        }
                    }
                    $student->grade_point = $temp;
                    $student->tong = round($lich_su->grade ?? 0, 1);
                    $student->status_subject = $history_status[$status_subject];
                    $student->enough_point = $countPointIsNull;
                }

                $group_grade_id_text = implode(',', $group_grade_id);
            }
            return $this->view('edu.so_diem', [
                'terms' => $terms,
                'departments' => $departments,
                'courses' => $courses,
                'groups' => $groups,
                'students' => $students ?? [],
                'group_grades' => $group_grades ?? [],
                'grades' => $grades ?? [],
                'grade_filter' => $grade_filter ?? [],
                'syllabus' => $syllabus ?? [],
                'group_grade_id_text' => $group_grade_id_text ?? '',
                'group_current' => $group,
                'qrImage' => $qrImage,
                'data_2fa' => $data_2fa,
                'code' => $code,
            ]);
        } catch (\Throwable $th) {
            Log::error(`---------------soDiem------------: \n` . $th);
        }
    }

    public function soDiemSave($request)
    {
        DB::beginTransaction();
        try {
            $grades = $request->grades;
            $group_id = $request->group_id;
            $course_id = $request->course_id;
            $group_grade_id_text = $request->group_grade_id_text;
            $lock_group = $request->lock_group;
            $whoIs = auth()->user()->user_login;
            $gradeToInsert = [];
            $term = Lib::getTermDetails();
            if ($lock_group) {
                $this->lockGradeGroup($group_id, $group_grade_id_text, $lock_group);
            }

            if ($group_grade_id_text == '') {
                return $this->redirectWithStatus('danger', 'Không tim thấy nhóm đầu điểm', url()->previous());
            }

            $syllabus_grade_info = $this->gradeInformation($group_grade_id_text, $group_id);

            $groupInfo = Group::where('id', $group_id)->firstOrFail();

            if ($groupInfo->pterm_id) {
                $termGroup = Term::where('id', $groupInfo->pterm_id)->firstOrFail();
                $term = [
                    'term_id' => $termGroup->id,
                    'term_name' => $termGroup->term_name
                ];
            }

            if ($grades && count($grades) > 0) {
                $this->createOrUpdateCourseGrade($syllabus_grade_info, $group_id, $whoIs, $course_id, $term, $grades, $groupInfo);
            }

            $this->syncDataSaveGradePoint($group_id);
            DB::commit();
            return $this->redirectWithStatus('success', 'Lưu thay đổi thành công');
        } catch (\Exception $exception) {
            DB::rollback();
            Log::error('Lỗi lưu điểm: ' . $exception);
            return $this->redirectWithStatus('danger', 'Đã xảy ra lỗi', url()->previous());
        }
    }

    public function gradeLockStore($request)
    {
        $grade_lock = $request->grade_lock;
        $group_id = $request->group_id;
        $unlock_grade_level_2 = $request->unlock_grade_level_2;
        $lock_strings = '';
        if ($unlock_grade_level_2) {
            CourseGrade::where('groupid', $group_id)->where('grade_id', $unlock_grade_level_2)->update([
                'locked' => 0,
            ]);
        } else {
            CourseGrade::where('groupid', $group_id)->update([
                'locked' => 0,
            ]);
            if (isset($grade_lock) && count($grade_lock)) {
                $lock_strings = implode(',', $grade_lock);
                foreach ($grade_lock as $grade) {
                    CourseGrade::where('grade_id', $grade)->where('groupid', $group_id)->update([
                        'locked' => 1,
                    ]);
                }
            }
            Group::where('id', $group_id)->update([
                'grade_lock_list' => $lock_strings,
            ]);
        }

        return $this->redirectWithStatus('success', 'Lưu khoá thành công');
    }

    public function lockGradeGroup($group_id, $group_grade, $lock_group)
    {
        $locked = 1;
        $group_grade = explode(',', $group_grade);
        $grades = Grade::whereIn('grade_group_id', $group_grade)->get();
        $grade_id = $grades->implode('id', ',');
        if ($lock_group == 2) {
            $locked = 0;
            $grade_id = '';
            ActionLog::create([
                'object'        => 'group',
                'auth'          => auth()->user()->user_login,
                'action'        => 'change_lock_group',
                'description'   => auth()->user()->user_login . " Mở sổ điểm lớp " . $group_id,
                'object_id'     => "$group_id",
                'data_changed'  => ('{ is_locked: từ (1) thành (2) }'),
                'ip'            => request()->getClientIp(),
            ]);
        } elseif ($lock_group == 1) {
            ActionLog::create([
                'object'        => 'group',
                'auth'          => auth()->user()->user_login,
                'action'        => 'change_lock_group',
                'description'   => auth()->user()->user_login . " Khóa sổ điểm lớp " . $group_id,
                'object_id'     => "$group_id",
                'data_changed'  => ('{ is_locked: từ (2) thành (1) }'),
                'ip'            => request()->getClientIp(),
            ]);
        }

        Group::where('id', $group_id)->update([
            'grade_lock_list' => $grade_id,
            'is_locked' => $locked,
        ]);

        return $this->redirectWithStatus('success', 'Khoá lớp thành công');
    }

    public function gradeLockProcess($group_id, $grade_lock = [])
    {
        $lock = [];
        $group = Group::find($group_id);
        if ($group) {
            $locked = explode(',', $group->grade_lock_list);
            foreach ($locked as $item) {
                $lock[$item] = $item;
            }
            foreach ($grade_lock as $item) {
                $lock[$item] = $item;
            }
            $lock_temp = implode(',', $lock);
            $group->grade_lock_list = $lock_temp;
            $group->save();
        }
    }

    public function gradeInformation($group_grade_id_text, $group_id = 0)
    {
        $syllabus_grade_info = [];
        $syllabus_grade_group_info = [];
        $group_grade_id = explode(',', $group_grade_id_text);
        $syllabus_grade_groups = GradeGroup::whereIn('id', $group_grade_id)->get();
        foreach ($syllabus_grade_groups as $syllabus_grade_group) {
            $syllabus_grade_group_info[$syllabus_grade_group->id] = [
                'id' => $syllabus_grade_group->id,
                'weight' => $syllabus_grade_group->weight,
                'minimum_required' => $syllabus_grade_group->minimum_required,
                'grade_group_name' => $syllabus_grade_group->grade_group_name,
                'syllabus_id' => $syllabus_grade_group->syllabus_id,
                'subject_id' => $syllabus_grade_group->subject_id,
                'subject_name' => $syllabus_grade_group->subject_name,
            ];
        }

        $syllabus_grades = Grade::whereIn('grade_group_id', $group_grade_id)->get();
        foreach ($syllabus_grades as $syllabus_grade) {
            $array_type = [0, 1, 2];
            if ($syllabus_grade->grade_type == 2) {
                $is_resit = 1;
            } else {
                $is_resit = 0;
            }

            $syllabus_grade_info[$syllabus_grade->id] = [
                'weight' => $syllabus_grade->weight,
                'is_final_exam' => $syllabus_grade->is_final_exam,
                'master_grade' => $syllabus_grade->master_grade,
                'grade_name' => $syllabus_grade->grade_name,
                'syllabus_name' => $syllabus_grade->syllabus_name,
                'minimum_required' => $syllabus_grade->minimum_required,
                'bonus_type' => $syllabus_grade->bonus_type,
                'grade_group_id' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['id'],
                'grade_group_weight' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['weight'],
                'grade_group_minimum_required' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['minimum_required'],
                'grade_group_name' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['grade_group_name'],
                'syllabus_id' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['syllabus_id'],
                'subject_id' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['subject_id'],
                'subject_name' => $syllabus_grade_group_info[$syllabus_grade->grade_group_id]['subject_name'],
                'is_resit' => $is_resit,
                'token' => substr(md5($group_id . $syllabus_grade->id), 0, 13),
            ];
        }

        return $syllabus_grade_info;
    }

    public function createOrUpdateCourseGrade($syllabus_grade_info, $group_id, $whoIs, $course_id, $term, $grades = [], $groupInfo = null)
    {
        $grade_lock = [];
        if (!$groupInfo) {
            $groupInfo = Group::find($group_id);
        }
        foreach ($grades as $user_login => $grade) {
            foreach ($grade as $grade_id => $point) {
                // if ($point == "") continue;
                $has_point = CourseGrade::where('groupid', $group_id)->where('grade_id', $grade_id)->where('login', $user_login)->first();
                if ($point !== null && trim($point) != '') {
                    $grade_lock[$grade_id] = $grade_id;
                    if ($has_point) {
                        $old_point = $has_point->val;
                        if ($has_point->locked) {
                            continue;
                        }

                        $has_point->modifier_login = $whoIs;
                        $has_point->token = $syllabus_grade_info[$grade_id]['token'];
                        $has_point->val = $point;
                        $has_point->locked = 1;
                        $has_point->grade_minimum_required = $syllabus_grade_info[$grade_id]['minimum_required'];
                        $has_point->grade_weight = $syllabus_grade_info[$grade_id]['weight'];
                        $has_point->grade_name = $syllabus_grade_info[$grade_id]['grade_name'];
                        $has_point->save();
                        if ($old_point != $point) {
                            $this->systemLog('group', 'update', "update grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $point, old value: $old_point", $user_login, $group_id, 0, '', '', 'grade');
                        }
                    } else {
                        CourseGrade::create([
                            'course_id' => $course_id,
                            'grade_id' => $grade_id,
                            'grade_minimum_required' => $syllabus_grade_info[$grade_id]['minimum_required'],
                            'grade_weight' => $syllabus_grade_info[$grade_id]['weight'],
                            'grade_name' => $syllabus_grade_info[$grade_id]['grade_name'],
                            'groupid' => $group_id,
                            'login' => $user_login,
                            'val' => $point,
                            'comment' => '',
                            'creator_login' => $whoIs,
                            'modifier_login' => $whoIs,
                            'grade_group_id' => $syllabus_grade_info[$grade_id]['grade_group_id'],
                            'grade_group_name' => $syllabus_grade_info[$grade_id]['grade_group_name'],
                            'grade_group_weight' => $syllabus_grade_info[$grade_id]['grade_group_weight'],
                            'grade_group_minimum_required' => $syllabus_grade_info[$grade_id]['grade_group_minimum_required'],
                            'syllabus_id' => $syllabus_grade_info[$grade_id]['syllabus_id'],
                            'subject_id' => $syllabus_grade_info[$grade_id]['subject_id'],
                            'is_used' => 0,
                            'subject_name' => $syllabus_grade_info[$grade_id]['subject_name'],
                            'course_group_name' => '',
                            'subject_code' => '',
                            'term_id' => $term['term_id'],
                            'term_name' => $term['term_name'],
                            'is_final' => $syllabus_grade_info[$grade_id]['is_final_exam'],
                            'is_resit' => $syllabus_grade_info[$grade_id]['is_resit'],
                            'master_grade' => $syllabus_grade_info[$grade_id]['master_grade'],
                            'token' => $syllabus_grade_info[$grade_id]['token'],
                            'group_val' => 0,
                            'temp' => 0,
                            'locked' => 1,
                            'start_date' => $groupInfo->start_date,
                            'end_date' => $groupInfo->end_date,
                        ]);

                        $this->systemLog('group', 'insert', "insert grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $point", $user_login, $group_id, 0, '', '', 'grade');
                    };
                } else {
                    if ($has_point) {
                        $old_point = $has_point->val;
                        $has_point->delete();
                        $this->systemLog('group', 'delete', "delete grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $old_point", $user_login, $group_id, 0, '', '', 'grade');
                    }
                }
            }
        }
        //        $this->gradeLockProcess($group_id, $grade_lock);
    }

    public function syncDataSaveGradePoint($group_id)
    {
        $member_detail = [];
        $temp_grade_group = [];
        $temp_grade = [];
        $diem_thuong_id = [];
        $status_group = 0;
        $tong_dau_diem_thi = 0;
        $group = Group::with('groupMembers')->find($group_id);
        $syllabus = GradeSyllabus::findOrFail($group->syllabus_id);
        $syllabus_minimum = $syllabus->minimum_required;

        // Buổi remote 2 không đc tính vào điều kiện điểm danh nên ko tính vào tổng số buổi học bắt đầu từ kỳ Summer 2022
        $checkUserdRemote2 = Term::where('id', $group->pterm_id)->where('startday', '>', '2022-05-01')->count();
        $arrSessionType = [8, 9, 10, 11, 18];
        if ($checkUserdRemote2 > 0) {
            $arrSessionType = [8, 9, 10, 11, 18, 21];
        }

        $syllabus_plan = SyllabusPlan::select('id')
            ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
            ->where('t7_syllabus_plan.syllabus_id', $group->syllabus_id)
            ->where('session_type.is_exam', 0)
            ->count();

        $grade_groups = GradeGroup::where('syllabus_id', $group->syllabus_id)->where('subject_id', $group->psubject_id)->get();
        $grade_group_ids = $grade_groups->pluck('id');
        $grades_table = Grade::where('syllabus_id', $group->syllabus_id)->whereIn('grade_group_id', $grade_group_ids)->get();
        $tong_dau_diem = $grades_table->count();
        $tong_dau_diem_khong_co_diem_thuong = $grades_table->where('bonus_type', 0)->where('master_grade', 0)->count();
        $id_diem_thuong = $grades_table->where('bonus_type', 1)->pluck('id');
        $subject = Subject::findOrFail($group->psubject_id);
        foreach ($grade_groups as $grade_group) {
            CourseGrade::where('grade_group_id', $grade_group->id)->where('groupid', $group_id)->update([
                'grade_group_weight' => $grade_group->weight,
                'grade_group_minimum_required' => $grade_group->minimum_required,
                'grade_group_name' => $grade_group->grade_group_name,
            ]);
            $temp_grade_group[$grade_group->id] = [
                'grade_name' => $grade_group->grade_group_name,
                'point' => 0,
                'total_point' => 0,
                'weight' => $grade_group->weight,
                'number_grade' => $grade_group->number_grade,
                'minimum_required' => $grade_group->minimum_required,
            ];
        }

        foreach ($grades_table as $grade) {
            CourseGrade::where('grade_id', $grade->id)->where('groupid', $group_id)->update([
                'grade_weight' => $grade->weight,
                'grade_minimum_required' => $grade->minimum_required,
                'grade_name' => $grade->grade_name,
            ]);

            if ($grade->is_final_exam && $grade->master_grade == 0) {
                $tong_dau_diem_thi += 1;
            }

            $temp_grade[$grade->id] = [
                'bonus_type' => $grade->bonus_type,
            ];

            if ($grade->bonus_type) {
                $diem_thuong_id[$grade->id] = $grade->id;
            }
        }

        $course_grades = CourseGrade::where('groupid', $group->id)->orderBy('master_grade')->get();
        $tong_diem_hoan_thanh_cua_lop = $course_grades->whereNotIn('grade_id', $id_diem_thuong)->groupBy('grade_id')->count();
        foreach ($course_grades as $course_grade) {
            $member_detail[$course_grade->login][] = [
                'grade_name' => $course_grade->grade_name,
                'grade_id' => $course_grade->grade_id,
                'grade_group_id' => $course_grade->grade_group_id,
                'point' => $course_grade->val,
                'master_grade' => $course_grade->master_grade,
                'weight' => $course_grade->grade_weight,
                'minimum_required' => $course_grade->grade_minimum_required,
                'is_final' => $course_grade->is_final,
                'is_resit' => $course_grade->is_resit,
                'grade_group_weight' => $course_grade->grade_group_weight,
            ];
        }

        foreach ($group->groupMembers as $member) {
            $temp = $temp_grade_group;
            $user_login = $member->member_login;
            $total_point = 0;
            $bonus_point = 0;
            $status_subject = 0;
            $grade_pass = 1;
            $exam_finish = 0;
            $exam_finish_grade = [];
            $tong_diem_hoan_thanh = 0;
            $tong_diem_thuong_hoan_thanh = 0;
            $grade_array = [];
            $di_thi = self::KHONG_DI_THI;
            $gradeGroupIdOfFinalScore = null;
            $gradeIdOfFinalScore = null;
            $flagCheckHaveResit = false;
            $course_result = $this->createOrUpdateCourseResult($group, $member->member_login, $subject);
            if (isset($member_detail[$member->member_login])) {
                $current_status_subject = $course_result->val;
                $grades = $member_detail[$member->member_login];
                $temp_grade_detail = [];
                $final_total_point = 0;

                foreach ($grades as $grade) {
                    if (!isset($diem_thuong_id[$grade['grade_id']])) {
                        $tong_diem_hoan_thanh += 1;
                    } else {
                        $tong_diem_thuong_hoan_thanh += 1;
                    }

                    $master_grade = $grade['master_grade'];
                    $grade_array[] = $grade['grade_name'] . ':' . $grade['weight'] . ':' . $grade['point'] . ':' . $grade['grade_id'];
                    if ($master_grade == 0) {
                        if ($temp_grade[$grade['grade_id']]['bonus_type'] == 1) {
                            $bonus_point += $grade['point'];
                        } else {
                            $temp_grade_detail[$grade['grade_group_id']][$grade['grade_id']] = ($grade['point'] * $grade['weight']);
                            if (!isset($temp[$grade['grade_group_id']]['point'])) {
                                $temp[$grade['grade_group_id']]['point'] = ($grade['point'] * $grade['weight']);
                            } else {
                                $temp[$grade['grade_group_id']]['point'] += ($grade['point'] * $grade['weight']);
                            }
                        }
                    } else {
                        $grade_pass = 1;
                        CourseGrade::where('groupid', $group->id)->where('login', $user_login)->where('grade_id', $master_grade)->update([
                            'is_used' => -1
                        ]);
                        $temp_grade_detail[$grade['grade_group_id']][$master_grade] = ($grade['point'] * $grade['weight']);
                        $temp[$grade['grade_group_id']]['point'] = ($grade['point'] * $grade['weight']);
                    }

                    if (!isset($temp[$grade['grade_group_id']]['total_point'])) {
                        $temp[$grade['grade_group_id']]['total_point'] = ($grade['point']);
                    } else {
                        $temp[$grade['grade_group_id']]['total_point'] += ($grade['point']);
                    }

                    // check + diem final 1 hay final 2nd vao tong
                    if ($grade['is_final'] === 1) {
                        if ($grade['is_resit'] < 1) {
                            $final_total_point = ($grade['point']);
                            $gradeGroupIdOfFinalScore = $grade['grade_group_id'];
                            $gradeIdOfFinalScore = $grade['grade_id'];
                        } else {

                            if ($grade['point'] !== null) {
                                $temp[$grade['grade_group_id']]['total_point'] -= $final_total_point;
                                $flagCheckHaveResit = true;
                            }
                        }
                    }

                    if ($grade['minimum_required'] != 0 && $grade['minimum_required'] > $grade['point']) {
                        $grade_pass = 0;
                    }

                    if ($grade['is_final'] && $grade['point']) {
                        $di_thi = self::CO_DI_THI;
                        $status_group = 1;
                    }

                    if ($grade['is_resit'] == 1) {
                        $di_thi = self::CO_DI_THI_2;
                    }

                    if ($grade['is_final'] && $grade['is_resit'] == 1) {
                        $status_group = 2;
                    }

                    if ($grade['is_final'] && $grade['is_resit'] == 0) {
                        $exam_finish += 1;
                        $exam_finish_grade[$grade['grade_id']] = $grade['grade_id'];
                    }
                }

                foreach ($temp_grade_detail as $key => $sub_item) {
                    if (isset($temp[$key]['point'])) {
                        if ($key === $gradeGroupIdOfFinalScore && !$flagCheckHaveResit) {
                            $temp[$key]['point'] = array_sum($sub_item);
                        }
                    }
                }

                // check nhóm đầu điểm
                foreach ($temp as $key => $item) {
                    if ($item['weight'] == 0) {
                        $point = 0;
                    } else {
                        $point = $item['point'] / $item['weight'];
                    }

                    // $point = round($point, 1);
                    $pointCheck = round($point, 1);
                    CourseGrade::where('login', $user_login)->where('groupid', $group->id)->where('grade_group_id', $key)->update([
                        'group_val' => $point,
                    ]);

                    $total_point += ($point * $item['weight']) / 100;
                    if ($item['minimum_required'] != 0 && $item['minimum_required'] > $pointCheck) {
                        $grade_pass = 0;
                    }
                }

                $total_point = $total_point + $bonus_point;
                if ($total_point > 10) {
                    $total_point = 10;
                }

                if ($total_point) {
                    $total_point = round($total_point, 1);
                }

                $grade_array = implode('$', $grade_array);
                if ($grade_pass && $total_point >= $syllabus_minimum) {
                    $status_subject = self::DAT;
                } else {
                    $status_subject = self::KHONG_DAT;
                }

                if ($current_status_subject < self::KHONG_DAT) {
                    $di_thi = self::CO_DI_THI;
                }

                if ($course_result->attendance_absent == '') {
                    $course_result->attendance_absent = 0;
                }

                if ($exam_finish < $tong_dau_diem_thi && $status_subject >= 0) {
                    $status_subject = self::KHONG_DAT;
                }

                if (count($exam_finish_grade) < $tong_dau_diem_thi && $status_subject > 0) {
                    $status_subject = self::KHONG_DAT;
                    $di_thi = self::KHONG_DI_THI;
                }

                if ($tong_diem_hoan_thanh_cua_lop < $tong_dau_diem_khong_co_diem_thuong && $status_subject > 0) {
                    $status_subject = self::KHONG_DAT;
                    $di_thi = self::KHONG_DI_THI;
                }

                if ($tong_diem_hoan_thanh_cua_lop == $tong_dau_diem_khong_co_diem_thuong && $tong_diem_hoan_thanh > 0) {
                    $di_thi = self::CO_DI_THI_2;
                }
            }

            if ($di_thi == 2) {
                $status_group = 1;
            }

            $checkAttendance = $course_result->attendance_absent == 0 ? 0 : ($syllabus_plan ? ($course_result->attendance_absent * 100 / $syllabus_plan) : 0);
            $status_subject = $checkAttendance > (100 - $syllabus->attendance_cutoff) ? self::TRUOT_DIEM_DANH : $status_subject;
            $course_result->grade = $total_point;
            $course_result->grade_detail = $grade_array;
            $course_result->is_finish = $di_thi;
            $course_result->val = $status_subject;
            $course_result->attendance_cutoff = $syllabus->attendance_cutoff;
            $course_result->minimum_required = $syllabus->minimum_required;
            $course_result->psubject_name = $group->psubject_name;
            $course_result->start_date = $group->start_date;
            $course_result->end_date = $group->end_date;
            $course_result->psubject_code = $group->psubject_code;
            $course_result->subject_id = $group->psubject_id;
            //            $course_result->skill_code = $group->skill_code;
            $course_result->total_session = $syllabus_plan;
            $course_result->total_exam = $tong_dau_diem_thi;
            $course_result->done_exam = count($exam_finish_grade);
            $course_result->taken_exam = $exam_finish;
            $course_result->total_grade = $tong_dau_diem;
            $course_result->done_grade = $tong_diem_hoan_thanh_cua_lop + $id_diem_thuong->count();
            $course_result->save();
        }

        if ($group->type == 1) {
            $group->finished = $status_group;
            $group->save();
        }
        //        die;
        //        return view('dump');
    }

    public function syncDataSaveGradePointTest($group_id)
    {
        $member_detail = [];
        $temp_grade_group = [];
        $temp_grade = [];
        $diem_thuong_id = [];
        $status_group = 0;
        $tong_dau_diem_thi = 0;
        $group = Group::with('groupMembers')->find($group_id);
        $syllabus = GradeSyllabus::findOrFail($group->syllabus_id);
        $syllabus_minimum = $syllabus->minimum_required;
        $syllabus_plan = SyllabusPlan::select('id')
            ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
            ->where('t7_syllabus_plan.syllabus_id', $group->syllabus_id)
            ->where('session_type.is_exam', 0)
            ->count();
        $grade_groups = GradeGroup::where('syllabus_id', $group->syllabus_id)->where('subject_id', $group->psubject_id)->get();
        $grades_table = Grade::where('syllabus_id', $group->syllabus_id)->get();
        $tong_dau_diem = $grades_table->count();
        $tong_dau_diem_khong_co_diem_thuong = $grades_table->where('bonus_type', 0)->where('master_grade', 0)->count();
        $id_diem_thuong = $grades_table->where('bonus_type', 1)->pluck('id');
        $subject = Subject::findOrFail($group->psubject_id);
        foreach ($grade_groups as $grade_group) {
            CourseGrade::where('grade_group_id', $grade_group->id)->where('groupid', $group_id)->update([
                'grade_group_weight' => $grade_group->weight,
                'grade_group_minimum_required' => $grade_group->minimum_required,
                'grade_group_name' => $grade_group->grade_group_name,
            ]);
            $temp_grade_group[$grade_group->id] = [
                'grade_name' => $grade_group->grade_group_name,
                'point' => 0,
                'total_point' => 0,
                'weight' => $grade_group->weight,
                'number_grade' => $grade_group->number_grade,
                'minimum_required' => $grade_group->minimum_required,
            ];
        }

        foreach ($grades_table as $grade) {
            CourseGrade::where('grade_id', $grade->id)->where('groupid', $group_id)->update([
                'grade_weight' => $grade->weight,
                'grade_minimum_required' => $grade->minimum_required,
                'grade_name' => $grade->grade_name,
            ]);
            if ($grade->is_final_exam && $grade->master_grade == 0) {
                $tong_dau_diem_thi += 1;
            }
            $temp_grade[$grade->id] = [
                'bonus_type' => $grade->bonus_type,
            ];
            if ($grade->bonus_type) {
                $diem_thuong_id[$grade->id] = $grade->id;
            }
        }

        $course_grades = CourseGrade::where('groupid', $group->id)->orderBy('master_grade')->get();
        $tong_diem_hoan_thanh_cua_lop = $course_grades->whereNotIn('grade_id', $id_diem_thuong)->groupBy('grade_id')->count();
        foreach ($course_grades as $course_grade) {
            $member_detail[$course_grade->login][] = [
                'grade_name' => $course_grade->grade_name,
                'grade_id' => $course_grade->grade_id,
                'grade_group_id' => $course_grade->grade_group_id,
                'point' => $course_grade->val,
                'master_grade' => $course_grade->master_grade,
                'weight' => $course_grade->grade_weight,
                'minimum_required' => $course_grade->grade_minimum_required,
                'is_final' => $course_grade->is_final,
                'is_resit' => $course_grade->is_resit,
                'grade_group_weight' => $course_grade->grade_group_weight,
            ];
        }

        foreach ($group->groupMembers as $member) {
            $temp = $temp_grade_group;
            $user_login = $member->member_login;
            echo "$user_login<br>";
            $total_point = 0;
            $bonus_point = 0;
            $status_subject = 0;
            $grade_pass = 1;
            $exam_finish = 0;
            $exam_finish_grade = [];
            $tong_diem_hoan_thanh = 0;
            $tong_diem_thuong_hoan_thanh = 0;
            $grade_array = [];
            $di_thi = self::KHONG_DI_THI;
            $course_result = $this->createOrUpdateCourseResult($group, $member->member_login, $subject);
            if (isset($member_detail[$member->member_login])) {
                $current_status_subject = $course_result->val;
                $grades = $member_detail[$member->member_login];
                //                dd($grades);
                //                $tong_diem_hoan_thanh = count($grades);
                $temp_grade_detail = [];
                foreach ($grades as $grade) {
                    if (!isset($diem_thuong_id[$grade['grade_id']])) {
                        $tong_diem_hoan_thanh += 1;
                    } else {
                        $tong_diem_thuong_hoan_thanh += 1;
                    }
                    $master_grade = $grade['master_grade'];
                    $grade_array[] = $grade['grade_name'] . ':' . $grade['weight'] . ':' . $grade['point'] . ':' . $grade['grade_id'];
                    if ($master_grade == 0) {
                        if ($temp_grade[$grade['grade_id']]['bonus_type'] == 1) {
                            $bonus_point += $grade['point'];
                        } else {
                            $temp_grade_detail[$grade['grade_group_id']][$grade['grade_id']] = ($grade['point'] * $grade['weight']);
                            $temp[$grade['grade_group_id']]['point'] += ($grade['point'] * $grade['weight']);
                        }
                    } else {
                        $grade_pass = 1;
                        CourseGrade::where('groupid', $group->id)->where('login', $user_login)->where('grade_id', $master_grade)->update([
                            'is_used' => -1
                        ]);
                        $temp_grade_detail[$grade['grade_group_id']][$master_grade] = ($grade['point'] * $grade['weight']);
                        $temp[$grade['grade_group_id']]['point'] = ($grade['point'] * $grade['weight']);
                    }
                    $temp[$grade['grade_group_id']]['total_point'] += $grade['point'];
                    if ($grade['minimum_required'] != 0 && $grade['minimum_required'] > $grade['point']) {
                        $grade_pass = 0;
                    }

                    if ($grade['is_final'] && $grade['point']) {
                        $di_thi = self::CO_DI_THI;
                        $status_group = 1;
                    }
                    if ($grade['is_resit'] == 1) {
                        $di_thi = self::CO_DI_THI_2;
                    }
                    if ($grade['is_final'] && $grade['is_resit'] == 1) {
                        $status_group = 2;
                    }
                    if ($grade['is_final'] && $grade['is_resit'] == 0) {
                        $exam_finish += 1;
                        $exam_finish_grade[$grade['grade_id']] = $grade['grade_id'];
                    }
                }

                foreach ($temp_grade_detail as $key => $sub_item) {
                    if (isset($temp[$key]['point'])) {
                        $temp[$key]['point'] = array_sum($sub_item);
                    }
                }

                foreach ($temp as $key => $item) {
                    if ($item['weight'] == 0) {
                        $point = 0;
                    } else {
                        $point = $item['point'] / $item['weight'];
                    }

                    // $point = round($point, 1);
                    $pointCheck = round($point, 1);
                    CourseGrade::where('login', $user_login)->where('groupid', $group->id)->where('grade_group_id', $key)->update([
                        'group_val' => $point,
                    ]);
                    $total_point += ($point * $item['weight']) / 100;
                    if ($item['minimum_required'] != 0 && $item['minimum_required'] > $pointCheck) {
                        $grade_pass = 0;
                    }
                }

                $total_point = $total_point + $bonus_point;
                if ($total_point > 10) {
                    $total_point = 10;
                }
                if ($total_point) {
                    $total_point = round($total_point, 1);
                }
                $grade_array = implode('$', $grade_array);

                if ($grade_pass && $total_point >= $syllabus_minimum) {
                    $status_subject = self::DAT;
                } else {
                    $status_subject = self::KHONG_DAT;
                }
                if ($current_status_subject < self::KHONG_DAT) {
                    $di_thi = self::CO_DI_THI;
                }

                if ($course_result->attendance_absent == '') {
                    $course_result->attendance_absent = 0;
                }

                echo "EXAM_FINISH:$exam_finish<br>";
                echo "TONG_DIEM_THI:$tong_dau_diem_thi<br>";
                echo "$status_subject<br>";
                if ($exam_finish < $tong_dau_diem_thi && $status_subject >= 0) {
                    $status_subject = self::KHONG_DAT;
                }

                if (count($exam_finish_grade) < $tong_dau_diem_thi && $status_subject > 0) {
                    $status_subject = self::KHONG_DAT;
                    $di_thi = self::KHONG_DI_THI;
                }

                if ($tong_diem_hoan_thanh_cua_lop < $tong_dau_diem_khong_co_diem_thuong && $status_subject > 0) {
                    $status_subject = self::KHONG_DAT;
                    $di_thi = self::KHONG_DI_THI;
                }

                if ($tong_diem_hoan_thanh_cua_lop == $tong_dau_diem_khong_co_diem_thuong && $tong_diem_hoan_thanh > 0) {
                    $di_thi = self::CO_DI_THI_2;
                }
                echo "$grade_pass<br>";
                echo "$status_subject<br>";
            }
            if ($di_thi == 2) {
                $status_group = 1;
            }
            $status_subject = ($syllabus_plan ? ($course_result->attendance_absent * 100 / $syllabus_plan) : 0) > (100 - $syllabus->attendance_cutoff) ? self::TRUOT_DIEM_DANH : $status_subject;
            $course_result->grade = $total_point;
            $course_result->grade_detail = $grade_array;
            $course_result->is_finish = $di_thi;
            $course_result->val = $status_subject;
            $course_result->attendance_cutoff = $syllabus->attendance_cutoff;
            $course_result->minimum_required = $syllabus->minimum_required;
            $course_result->psubject_name = $group->psubject_name;
            $course_result->psubject_code = $group->psubject_code;
            $course_result->start_date = $group->start_date;
            $course_result->end_date = $group->end_date;
            $course_result->subject_id = $group->psubject_id;
            //            $course_result->skill_code = $group->skill_code;
            $course_result->total_session = $syllabus_plan;
            $course_result->total_exam = $tong_dau_diem_thi;
            $course_result->done_exam = count($exam_finish_grade);
            $course_result->taken_exam = $exam_finish;
            $course_result->total_grade = $tong_dau_diem;
            $course_result->done_grade = $tong_diem_hoan_thanh_cua_lop + $id_diem_thuong->count();
            $course_result->save();
        }

        if ($group->type == 1) {
            $group->finished = $status_group;
            $group->save();
        }
        //        die;
        //        return view('dump');
    }

    public function createOrUpdateCourseResult($group, $user_login, $subject)
    {
        $result = CourseResult::where('student_login', $user_login)->where('groupid', $group->id)->first();
        if (!$result) {
            $result = new CourseResult();
            $result->course_id = request()->course_id;
            $result->subject_id = $group->psubject_id;
            $result->student_login = $user_login;
            $result->groupid = $group->id;
            $result->source = 2;
            $result->term_id = $group->pterm_id;
            $result->pgroup_name = $group->group_name;
            $result->syllabus_id = $group->syllabus_id;
            $result->skill_code = $subject->skill_code;
            // $result->save();
        }
        $result->source = 2;
        $result->subject_id = $group->psubject_id;
        $result->term_id = $group->pterm_id;
        $result->pterm_name = $group->pterm_name;
        $result->pgroup_name = $group->group_name;
        $result->syllabus_id = $group->syllabus_id;
        $result->skill_code = $subject->skill_code;
        $result->number_of_credit = $subject->num_of_credit;
        $result->start_date = $group->start_date;
        $result->end_date = $group->end_date;
        $result->save();

        return $result;
    }

    public function importForm($request)
    {
        $term_id = $request->term_id;
        $terms = Term::orderBy('id', 'desc')->where('is_locked', 0)->get();
        $course = Course::where('term_id', $term_id ?? $terms->first()->id)->orderBy('psubject_code')->get();

        return $this->view('edu.import', [
            'course' => $course,
            'terms' => $terms,
        ]);
    }


    public function importStore($request)
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $reports = [];
        $group_member_array = [];
        $grade_data = [];
        $grade_id_data = [];
        $course_id = $request->course_id;
        $whoIs = Auth::user()->user_login;
        $term = Lib::getTermDetails();
        if (!$users = $this->importDataFromFile($request->users)) {
            return $this->redirectWithStatus('danger', 'Không có sinh viên nào để import hoặc file tải lên bị lỗi', url()->previous());
        }

        $course = Course::find($course_id);
        $grades = Grade::where('syllabus_id', $course->syllabus_id)->orderBy('grade_name')->get();
        $grade_length = $grades->count();
        $group_grades = GradeGroup::where('syllabus_id', $course->syllabus_id)->get();
        $groups = Group::where('body_id', $course->id)->where('list_group.is_virtual', 0)->get();
        // $grade_bonus = $grades->where('bonus_type', 1)->pluck('max_point', 'id');
        $bonus_i = [];
        foreach ($grades as $grade) {
            $grade_id_data[] = $grade->id;
            if ($grade->bonus_type) {
                $bonus_i[count($grade_id_data) - 1] = $grade->max_point;
            }
        }

        $group_array_name = $groups->pluck('group_name', 'id');
        $group_array_id = $groups->pluck('id', 'id');
        $group_members = GroupMember::whereIn('groupid', $group_array_id)->get();
        foreach ($group_members as $member) {
            $member->load('user');
            $member->user_code = $member->user->user_code;
            $group_name = $groups->firstWhere('id', $member->groupid);
            $group_member_array[$group_name->group_name][$member->user_code] = $member->member_login;
        }

        $group_grade_id_text = $group_grades->implode('id', ',');
        foreach ($users as $key => $item) {
            $current_key = "Dòng $key:";
            if (isset($item[0]) && isset($item[1])) {
                $user_code = $item[0];
                $group_id = $item[1];
                if (!$group_array_name->contains($group_id)) {
                    $reports[] = "$current_key Mã lớp không thuộc khoá học này";
                    continue;
                }

                if (!isset($group_member_array[$group_id][$user_code])) {
                    $reports[] = "$current_key Sinh viên không thuộc khoá học này";
                    continue;
                }

                for ($i = 0; $i < $grade_length; $i++) {
                    $point = $item[$i + 2];
                    if ($point === null) {
                        continue;
                    }

                    if (isset($bonus_i[$i])) {
                        if ($point < 0 || $point > $bonus_i[$i]) {
                            $reports[] = "$current_key Điểm thưởng không nằm trong phạm vi 0-" . $bonus_i[$i];
                            continue;
                        }
                    } else {
                        if ($point < 0 || $point > 10) {
                            $reports[] = "$current_key Điểm số không nằm trong phạm vi 0-10";
                            continue;
                        }
                    }

                    $grade_data[$group_id][$group_member_array[$group_id][$user_code]][$grade_id_data[$i]] = $point;
                }
            }
        }
        if (count($grade_data)) {
            foreach ($grade_data as $key => $data) {
                $group_id = $groups->firstWhere('group_name', $key)->id;
                // kiểm tra các lớp thi lại
                $groupCheck = $group = Group::where('is_virtual', 0)
                    ->where('id', $group_id)
                    ->count();
                if ($groupCheck == 0) {
                    continue;
                }

                $syllabus_grade_info = $this->gradeInformation($group_grade_id_text, $group_id);
                $this->createOrUpdateCourseGrade($syllabus_grade_info, $group_id, $whoIs, $course_id, $term, $data);
                $this->syncDataSaveGradePoint($group_id);
            }
        }

        $reports[] = "Quá trình import hoàn tất";
        return $this->redirectWithStatus('success', implode('<br>', $reports));
    }

    public function importDataFromFile($file)
    {
        $data = Excel::toArray(new DisciplineImport(), $file);
        if (count($data[0]) <= 1) {
            return false;
        }
        unset($data[0][0]);

        return $data[0];
    }

    public function downloadFileImportExample($course_id)
    {
        return Excel::download(new ImportDiem($course_id), $course_id . now()->format('_d_m_y_h_i_s') . '.xlsx');
    }

    public function exportForm($request)
    {
        ini_set('max_execution_time', 360);
        $term = Lib::getTermDetails();
        $course_id = $request->course_id;
        $term_id = $request->term_id;
        $terms = Term::orderBy('id', 'desc')->get();
        $course = Course::where('term_id', $term_id ? $term_id : $term['term_id'])->orderBy('psubject_code')->get();
        if ($course_id == null) {
            $groups = [];
        } else {
            $groups = Group::when($course_id, function ($query, $course_id) {
                $query->where('body_id', $course_id);
            })->where('list_group.is_virtual', 0)->get();
        }

        return $this->view('edu.export', [
            'course' => $course,
            'groups' => $groups,
            'terms' => $terms,
        ]);
    }

    public function exportDownload($request)
    {
        ini_set("pcre.backtrack_limit", "20000000");
        ini_set('max_execution_time', '600');

        $course_id = $request->course_id;
        $group_id = $request->group_id;
        $display = $request->display;
        $grade_temp = [];
        $code = [];
        $groups = Group::with('groupMembers')->where('list_group.is_virtual', 0)->where('body_id', $course_id)->when($group_id, function ($query, $group_id) {
            $query->where('id', $group_id);
        })->get();
        if (!is_null($group_id)) {
            $code[$group_id] = CourseGrade::select('token')->where('groupid', $group_id)->orderBy('grade_id')->orderBy('id', 'desc')->first();
        } else {
            foreach ($groups as $group) {
                $code[$group->id] = CourseGrade::select('token')->where('groupid', $group->id)->orderBy('grade_id')->orderBy('id', 'desc')->first();
            }
        }

        $course = Course::find($course_id);
        $grades = Grade::where('syllabus_id', $course->syllabus_id)->when($display, function ($query, $display) {
            $query->where('is_final_exam', 0);
        })->orderBy('grade_name')->get();
        foreach ($grades as $grade) {
            $grade_temp[$grade->id] = [
                'grade_name' => $grade->grade_name,
                'grade_weight' => $grade->weight,
                'point' => '-',
            ];
        }

        foreach ($groups as $group) {
            foreach ($group->groupMembers as $member) {
                $member->result = CourseResult::where('student_login', $member->member_login)->where('groupid', $member->groupid)->first();
                $temp = $grade_temp;
                $member->load('user');
                $member->user_code = $member->user->user_code ?? "";
                $member->full_name = $member->user->full_name ?? "";
                if (!isset($member->user->user_code)) {
                    Log::debug("Giáp check sv này: " . $member->member_login);
                }

                $my_grades = CourseGrade::where('groupid', $member->groupid)->when($display, function ($query, $display) {
                    $query->where('is_final', 0);
                })->where('login', $member->member_login)->get();
                foreach ($my_grades as $my_grade) {
                    $temp[$my_grade->grade_id]['point'] = $my_grade->val;
                }

                $member->grades = $temp;
            }
        }

        $pdf = new PDF2();
        $pdf = $pdf::loadView('pdf.export_10a', [
            'groups' => $groups,
            'grades' => $grade_temp,
            'code' => $code,
            'display' => $display,
        ], [], [
            'orientation' => 'L',
        ]);
        //        $pdf->SetProtection(['print'], now()->format('dmY'), 'pass');

        return $pdf->download('so_diem_10a' . now()->format('_d_m_y_h_i_s') . '.pdf');
    }


    public function exportCSVInfoPoint(Request $request)
    {
        $termName = $request->term_name;
        $teacherName = $request->teacher_name;
        $term = Term::find($termName);

        $teacher = User::where('user_login', $teacherName)->where('user_level', 2)->first();
        $listGroupId = Activity::select('groupid')
            ->where('leader_login', $teacher->user_login)
            ->where('term_id', $term->id)
            ->groupBy('groupid')
            ->pluck('groupid')
            ->toArray();

        $groups = Group::where('pterm_id', $term->id)
            ->whereIn('id', $listGroupId)
            ->where('is_virtual', 0)
            ->get();

        $csvData = [];
        $header = ['Class', 'Student Code', 'Full Name', 'Subject'];

        $maxGrades = 0; // dùng để thêm các cột điểm động

        foreach ($groups as $group) {
            $members = $group->groupMembers;
            $subject = Subject::find($group->psubject_id);
            $course = Course::where('term_id', $term->id)
                ->where('subject_id', $subject->id)
                ->first();

            $courseGrades = CourseGrade::where('course_id', $course->id)
                ->where('groupid', $group->id)
                ->get();

            foreach ($members as $member) {
                $studentCode = $member->user->user_code ?? '';
                $fullName = $member->user->fullname ?? '';
                $grades = $courseGrades->where('login', $member->member_login);

                $gradeList = [];
                foreach ($grades as $g) {
                    $gradeName = '[' . str_replace('&', '-', $g->grade_group_name) . ']' . str_replace('&', '-', $g->grade_name);
                    $gradeList[$gradeName] = $g->val;
                }

                // cập nhật số lượng điểm nhiều nhất
                if (count($gradeList) > $maxGrades) {
                    $maxGrades = count($gradeList);
                    $header = array_merge(['Class', 'Student Code', 'Full Name', 'Subject'], array_keys($gradeList));
                }

                $csvData[] = array_merge([
                    $group->group_name,
                    $studentCode,
                    $fullName,
                    $subject->subject_name ?? '',
                ], array_values($gradeList));
            }
        }

        // nếu cần lấp đầy phần thiếu
        foreach ($csvData as &$row) {
            $row = array_pad($row, count($header), '');
        }

        $filename = $teacher->user_login . '-' . $term->term_name . '.csv';

        return Response::streamDownload(function () use ($csvData, $header) {
            echo "\xEF\xBB\xBF";
            $handle = fopen('php://output', 'w');
            fputcsv($handle, $header);
            foreach ($csvData as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
        }, $filename, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ]);
    }

    public function exportCSVInfoPoint2(Request $request)
    {
        $auth = auth()->user();
        $termName = $request->term_name;
        $subjectCode = $request->subject_code;
        $authName = $auth->user_login;

        $term = Term::where('id', $termName)->where('is_locked', 0)->first();
        if (!$term) {
            abort(404, 'Không tìm thấy học kỳ hoặc học kỳ đã bị khóa.');
        }

        $groups = Group::where('pterm_id', $term->id)
            ->where('psubject_code', $subjectCode)
            ->where('is_virtual', 0)
            ->get();

        $csvHeader = ['Mã lớp', 'Mã sinh viên', 'Họ tên'];

        $csvRows = [];

        foreach ($groups as $group) {
            $listMember = $group->groupMembers;
            $subject = Subject::find($group->psubject_id);

            if (!$subject) continue;

            $course = Course::where('term_id', $term->id)
                ->where('subject_id', $subject->id)
                ->first();
            if (!$course) continue;

            $courseGrade = CourseGrade::where('course_id', $course->id)
                ->where('groupid', $group->id)
                ->orderBy('groupid')
                ->orderBy('login')
                ->orderBy('grade_id')
                ->get();

            $groupGrades = Grade::where('syllabus_id', $group->syllabus_id)
                ->orderBy('id')
                ->get();

            $groupGradeCheck = GradeGroup::where('syllabus_id', $group->syllabus_id)->get();

            if ($groupGradeCheck->isEmpty()) continue;

            $gradeGroupNames = $groupGradeCheck->pluck('grade_group_name', 'id')->toArray();

            // Add headers (once)
            if (count($csvHeader) <= 3) {
                foreach ($groupGrades as $grade) {
                    $groupName = $gradeGroupNames[$grade->grade_group_id] ?? '';
                    $gradeName = $grade->grade_name ?? '';
                    $csvHeader[] = "[$groupName]$gradeName";
                }
            }

            foreach ($listMember as $member) {
                $row = [];
                $row[] = $group->group_name;
                $row[] = $member->user->user_code ?? '';
                $row[] = $member->user->fullname ?? '';

                $grades = $courseGrade->filter(function ($item) use ($member) {
                    return $item->login == $member->member_login;
                });

                $gradeMap = [];
                foreach ($grades as $g) {
                    $groupLabel = "[" . str_replace('&', '-', $g->grade_group_name) . "]" . str_replace('&', '-', $g->grade_name);
                    $gradeMap[$groupLabel] = $g->val;
                }

                foreach (array_slice($csvHeader, 3) as $colName) {
                    $row[] = $gradeMap[$colName] ?? '';
                }

                $csvRows[] = $row;
            }
        }

        $fileName = $auth->user_login . '-' . $term->term_name . '-' . $subjectCode . '.csv';

        return response()->streamDownload(function () use ($csvHeader, $csvRows) {
            // Ghi BOM để Excel hiển thị đúng tiếng Việt
            echo "\xEF\xBB\xBF";

            $handle = fopen('php://output', 'w');
            fputcsv($handle, $csvHeader);
            foreach ($csvRows as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
        }, $fileName, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"$fileName\"",
        ]);
    }

    public function indexGradeFUGE(Request $request)
    {
        $term_id = $request->get('term_log', null);
        $currentTerm = null;
        if ($term_id != null) {
            $currentTerm = Term::find($term_id);
        } else {
            $currentTerm = Term::where('startday', '<=', now())
                ->where('endday', '>=', now())
                ->first();
        }

        $fileCache = [];
        if ($currentTerm != null) {
            $basePath = "$currentTerm->term_name";
            if (Storage::disk('fuge')->exists("$basePath/cache.json")) {
                $listCache = json_decode(Storage::disk('fuge')->get("$basePath/cache.json"), true);
                $files = Storage::disk('fuge')->allFiles($basePath . '/');
                foreach ($listCache as $keyCache => $infoCache) {
                    $dataAdd = [
                        'process' => $infoCache['process'],
                        'name' => $infoCache['file_name_new'],
                        'fg' => null,
                        'csv' => null
                    ];

                    $fgFilePath = "$basePath/" . $infoCache['auth'] . "/" . $infoCache['file_name_new'] . "";
                    $csvFilePath = "$basePath/" . $infoCache['auth'] . "/" . $infoCache['file_name_new'] . ".csv";
                    $keySearch = array_search($fgFilePath, $files);
                    if ($keySearch !== false) {
                        $dataAdd['fg'] = $fgFilePath;
                    }

                    $keySearch = array_search($csvFilePath, $files);
                    if ($keySearch !== false) {
                        $dataAdd['csv'] = $csvFilePath;
                    }

                    $fileCache[$infoCache['auth']][] = $dataAdd;
                }
            }
        }

        $listUser = User::where('user_level', 2)->where('user_login', 'NOT LIKE', '%giangvienao%')->orderBy('id', 'DESC')->get();

        return $this->view('edu.index-fuge', [
            'reports' => [],
            'file_cache' => $fileCache,
            'term_log' => ($currentTerm->id ?? null),
            'terms' => Term::where('is_locked', 0)->orderBy('id', 'DESC')->get(),
            'terms2' => Term::orderBy('id', 'DESC')->get(),
            'teachers' => $listUser,
        ]);
    }

    public function importGradeFUGE(Request $request)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        $userUpdate = Auth::user();
        $dataFile2 = $request->list_file;

        $checkTerm = Term::where('term_name', $request->term_name)->where('is_locked', 0)->first();
        if (!$checkTerm) {
            return Redirect::route('edu.fuge.index')->with([
                'reports2' => [
                    'Kỳ hiện tại đã khóa, bạn không thể import điểm'
                ]
            ]);
        }

        $exists = Storage::disk('fuge')->has($request->term_name . "/cache.json");
        if (!$exists) {
            $fileCache = [];
        } else {
            $fileCache = json_decode(Storage::disk('fuge')->get("" . $request->term_name . "/cache.json"), true);
        }

        if (isset($request->list_file) && count($request->list_file) > 0) {
            $reports = [];
            foreach ($request->list_file as $key => $file) {
                $inforFile = explode('-', $file->getClientOriginalName());
                if (sizeof($inforFile) != 3) {
                    $reports[] = 'File ' . $file->getClientOriginalName() . ' tên không hợp lệ!';
                    continue;
                } else {
                    $reports[] = 'File ' . $file->getClientOriginalName() . ' Đã được lưu đang tiến hành xử lý';
                }

                $newName = implode('-', $inforFile);
                $fileCache[] = [
                    'process' => 0,
                    'time' => now(),
                    'auth' => $userUpdate->user_login,
                    'block' => $request->block_name,
                    'file_name_old' => $file->getClientOriginalName(),
                    'file_name_new' => $newName,
                ];
                Storage::disk('fuge')->putFileAs(
                    "" . $request->term_name . "/" . $userUpdate->user_login,
                    $file,
                    $newName
                );
            }

            Storage::disk('fuge')->put("" . $request->term_name . "/cache.json", json_encode($fileCache));
            // return Redirect::route('edu.fuge.index')->with([
            //     'reports2' => $reports
            // ]);
        }

        // Kiểm tra sự tồn tại của file
        if (!isset($request->file)) {
            return Redirect::route('edu.fuge.index')->with([
                'reports2' => ['File không hợp lệ!']
            ]);
        }

        $file = $request->file;
        $data = [];
        $header = [];

        if (($handle = fopen($file, 'r')) !== false) {
            // Bỏ qua BOM nếu có (EF BB BF)
            $bom = fread($handle, 3);
            if ($bom !== "\xEF\xBB\xBF") {
                rewind($handle); // quay lại nếu không có BOM
            }

            $rowIndex = 0;
            while (($row = fgetcsv($handle, 1000, ',')) !== false) {
                if ($rowIndex === 0) {
                    $header = $row;
                } else {
                    $item = [];
                    foreach ($header as $i => $key) {
                        $item[$key] = $row[$i] ?? null;
                    }
                    $data[] = $item;
                }
                $rowIndex++;
            }
            fclose($handle);
        }
        // xử lý điểm
        $reports = $this->importGrade($newName, $userUpdate, $data, $request->term_name, $request->block_name);

        // cập nhập file cache
        $fileCache[] = [
            'process' => 1,
            'time' => now(),
            'auth' => $userUpdate->user_login,
            'block' => $userUpdate->block_name,
            'file_name_old' => $request->file->getClientOriginalName(),
            'file_name_new' => $newName,
        ];

        // lưu file
        Storage::disk('fuge')->putFileAs(
            $request->term_name . "/" . $userUpdate->user_login,
            $request->file,
            $newName
        );

        Storage::disk('fuge')->put($request->term_name . "/cache.json", json_encode($fileCache));

        return Redirect::route('edu.fuge.index')->with([
            'reports2' => $reports
        ]);
    }

    public function importGrade(&$newName, $user, $dataFile, $term_name, $blockCheck = null)
    {
        $reports = [];
        // $dataFile = implode(' ', explode("\r\n", $dataFile));
        $dataFile = explode("\r\n", $dataFile)[0] ?? '';
        $dataFileArr = explode(' ', $dataFile);
        $dataAfterConvert = array_map(function ($a) {
            try {
                return pack("H*", $a);
            } catch (\Exception $th) {
                return false;
            }
        }, $dataFileArr);

        if ($dataAfterConvert == false) {
            return ['File import lỗi, vui lòng gửi lại thông tin cho IT'];
        }

        $xmlFile = implode('', $dataAfterConvert);
        $xmlFileData = simplexml_load_string($xmlFile);
        $json = json_encode($xmlFileData);
        $xmlFileData = json_decode($json, TRUE);
        // init data
        $termName = $xmlFileData['Semester'];
        $teacherLogin = $xmlFileData['Login'];
        $subjectClassGrades = $xmlFileData['SubjectClassGrades'];
        $newName = $termName . '-' . $teacherLogin;
        if (isset($xmlFileData['subject'])) {
            $newName = $newName . '-' . $xmlFileData['subject'];
        }

        $newName = $newName . '.fg';
        // find data form Database
        $term = Term::where('term_name', $termName)->orWhere('id', $termName)->first();
        if (!$term) {
            $reports[] = "Kỳ thứ không tồn tại";
            return $reports;
        } else {
            if (strtolower($term_name) != strtolower($termName)) {
                // $reports[] = "File import không đúng với kỳ import";
                // return $reports;
            }
        }

        $teacher = User::where('user_login', $teacherLogin)->first();
        if (!$teacher) {
            $reports[] = "giảng viên không tồn tại";
            return $reports;
        }

        // kiểm tra theo block
        $blockCheck = null;
        $blockObj = null;
        if ($blockCheck != null) {
            $blockObj = Block::where('block_name', 'Block ' . $blockCheck)->where('term_id', $term->id)
                ->first();
        }

        $listSubjectProcess = [];
        foreach ($subjectClassGrades as $subjectClassGrade) {
            if (isset($subjectClassGrade['Subject'])) {
                if (!isset($listSubjectProcess[$subjectClassGrade['Subject']])) {
                    $listSubjectProcess[$subjectClassGrade['Subject']] = [
                        'subject_code' => $subjectClassGrade['Subject'],
                        'class' => [],
                    ];
                }

                $classAdd = [
                    'class_name' => $subjectClassGrade['Class'],
                    'students' => ($subjectClassGrade['Students']['Student'] ?? [])
                ];

                $listSubjectProcess[$subjectClassGrade['Subject']]['class'][] = $classAdd;
            } else {
                foreach ($subjectClassGrade as $value) {
                    if (!isset($listSubjectProcess[$value['Subject']])) {
                        $listSubjectProcess[$value['Subject']] = [
                            'subject_code' => $value['Subject'],
                            'class' => [],
                        ];
                    }

                    $classAdd = [
                        'class_name' => $value['Class'],
                        'students' => ($value['Students']['Student'] ?? [])
                    ];

                    $listSubjectProcess[$value['Subject']]['class'][] = $classAdd;
                }
            }
        }

        $listSubjectProcess = array_values($listSubjectProcess);
        // DB::beginTransaction();
        try {
            $dataError = null;
            foreach ($listSubjectProcess as $key => $value) {
                // $dataCheck = 1;
                $dataProcess = call_user_func(function () use ($value, $term, $blockCheck, $blockObj) {
                    $gradesCheck = [];
                    // $gradesGroupCheck = [];
                    $res = [];
                    $course_id = null;
                    foreach ($value['class'] as $group) {
                        $errorGroup = false;
                        $groupObject = Group::where('group_name', $group['class_name'])
                            ->where('list_group.is_virtual', 0)
                            ->where('psubject_code', $value['subject_code'])
                            ->where('pterm_id', $term->id);

                        if ($blockCheck != null) {
                            if ($blockObj != null) {
                                $groupObject = $groupObject->where('block_id', $blockObj->block_id);
                            } else {
                                $groupObject = $groupObject->whereIn('psubject_code', $this->listSubject2Blocks);
                            }
                        }

                        $groupObject = $groupObject->first();
                        if (!$groupObject) {
                            continue;
                        }

                        $course_id = $groupObject->body_id;
                        if (!isset($gradesCheck[$groupObject->syllabus_id])) {
                            $gradesCheck[$groupObject->syllabus_id] = Grade::select('t7_grade.id', 't7_grade.campus_id', 't7_grade.grade_name', 't7_grade.grade_group_id', 't7_grade.syllabus_id', 't7_grade.syllabus_name', 't7_grade.subject_id', 't7_grade.subject_name', 't7_grade_group.grade_group_name')
                                ->join('t7_grade_group', 't7_grade_group.id', '=', 't7_grade.grade_group_id')
                                ->where('t7_grade.syllabus_id', $groupObject->syllabus_id)
                                ->orderBy('grade_name')
                                ->get();
                        }

                        if (isset($group['students']['Roll'])) {
                            $dataAdd = [
                                $group['students']['Roll'],
                                $group['class_name']
                            ];

                            foreach ($gradesCheck[$groupObject->syllabus_id] as $grade) {
                                $dataPoint = call_user_func(function () use ($grade, $group) {
                                    $res = null;
                                    if (isset($group['students']['Grades']['GradeComponent'][0])) {
                                        foreach ($group['students']['Grades']['GradeComponent'] ?? [] as $value) {
                                            $nameSave = $grade->grade_group_name;
                                            $nameSave = str_replace('&', '-', $nameSave);
                                            $gradeNameSave = str_replace('&', '-', $grade->grade_name);
                                            $checkName = [
                                                "$gradeNameSave",
                                                "[$grade->grade_group_id]$gradeNameSave",
                                                "[$nameSave]$gradeNameSave",
                                                "[ $nameSave ] $gradeNameSave",
                                                "[$grade->grade_group_name]$gradeNameSave",
                                                "[ $grade->grade_group_name ] $gradeNameSave"
                                            ];

                                            if (in_array($value['Component'], $checkName)) {
                                                $res = is_numeric($value['Grade']) ? (float)$value['Grade'] : null;
                                                break;
                                            }
                                        }
                                    } elseif (isset($group['students']['Grades']['GradeComponent'])) {
                                        $nameSave = $grade->grade_group_name;
                                        $nameSave = str_replace('&', '-', $nameSave);
                                        $gradeNameSave = str_replace('&', '-', $grade->grade_name);
                                        $checkName = [
                                            "$gradeNameSave",
                                            "[$grade->grade_group_id]$gradeNameSave",
                                            "[$nameSave]$gradeNameSave",
                                            "[ $nameSave ] $gradeNameSave",
                                            "[$grade->grade_group_name]$gradeNameSave",
                                            "[ $grade->grade_group_name ] $gradeNameSave"
                                        ];

                                        if (in_array($group['students']['Grades']['GradeComponent']['Component'], $checkName)) {
                                            $res = is_numeric($group['students']['Grades']['GradeComponent']['Grade']) ?
                                                (float)$group['students']['Grades']['GradeComponent']['Grade'] :
                                                null;
                                        }
                                    } else {
                                        return false;
                                    }

                                    return $res;
                                });

                                if ($dataPoint !== false) {
                                    $dataAdd[] = $dataPoint;
                                } else {
                                    continue;
                                    $errorGroup = true;
                                }
                            }

                            if ($errorGroup == true) {
                                continue;
                            }

                            $dataAdd[] = $group['students']['Comment'] ?? null;
                            $res[] = $dataAdd;
                        } else {
                            foreach ($group['students'] ?? [] as $student) {
                                $dataAdd = [
                                    $student['Roll'],
                                    $group['class_name']
                                ];

                                foreach ($gradesCheck[$groupObject->syllabus_id] as $grade) {
                                    $dataAdd[] = call_user_func(function () use ($value, $grade, $student) {
                                        $res = null;
                                        $nameSave = $grade->grade_group_name;
                                        $nameSave = str_replace('&', '-', $nameSave);
                                        $gradeNameSave = str_replace('&', '-', $grade->grade_name);
                                        $checkName = [
                                            "$gradeNameSave",
                                            "[$grade->grade_group_id]$gradeNameSave",
                                            "[$nameSave]$gradeNameSave",
                                            "[ $nameSave ] $gradeNameSave",
                                            "[$grade->grade_group_name]$gradeNameSave",
                                            "[ $grade->grade_group_name ] $gradeNameSave"
                                        ];

                                        if (isset($student['Grades']['GradeComponent']['Component'])) {
                                            if (in_array($student['Grades']['GradeComponent']['Component'], $checkName)) {
                                                $res = is_numeric($student['Grades']['GradeComponent']['Grade']) ? (float)$student['Grades']['GradeComponent']['Grade'] : null;
                                            }
                                        } else {
                                            foreach ($student['Grades']['GradeComponent'] ?? [] as $value) {
                                                if (!isset($value['Component'])) {
                                                    Log::error("Lỗi khi import điểm cho sinh viên!");
                                                    throw new \Exception('Có lỗi xảy ra!');
                                                }
                                                if (in_array($value['Component'], $checkName)) {
                                                    $res = is_numeric($value['Grade']) ? (float)$value['Grade'] : null;
                                                    break;
                                                }
                                            }
                                        }

                                        return $res;
                                    });
                                }

                                $dataAdd[] = $student['Comment'] ?? null;
                                $res[] = $dataAdd;
                            }
                        }
                    }

                    return [
                        'course_id' => $course_id,
                        'res' => $res
                    ];
                });

                if ($dataProcess['course_id'] != null) {
                    $dataError = [$dataProcess, $value];
                    $reports = array_merge($reports, $this->importFUGE($user, $dataProcess, $term, $teacher, $value));
                }
                // $reports = array_merge($reports, ["dev $key 1", "dev $key 1"]);
            }

            // lưu file cache
            $exportFileName = "$term->term_name/$user->user_login/$newName" . ".csv";
            foreach ($reports as $key => $value) {
                Storage::disk('fuge')->append($exportFileName, $value);
            }

            // DB::commit();
            // DB::rollback();
            return $reports;
        } catch (\Exception $exception) {
            // DB::rollback();
            dd(__LINE__, $exception, $dataError);
        }
    }

    public function importFUGE($user, $data, $term, $teacher, $dataUpdate)
    {
        $reports = [];
        $group_member_array = [];
        $grade_data = [];
        $grade_id_data = [];
        $course_id = $data['course_id'];
        $whoIs = $user;
        // $term = Lib::getTermDetails();
        $term = $term->toArray();
        $term['term_id'] = $term['id'];

        $users = $data['res'];
        $course = Course::find($course_id);
        $grades = Grade::where('syllabus_id', $course->syllabus_id)->orderBy('grade_name')->get();
        $grade_length = $grades->count();
        $group_grades = GradeGroup::where('syllabus_id', $course->syllabus_id)->get();
        $groups = Group::where('list_group.is_virtual', 0)
            ->where('body_id', $course->id)->get();
        // $grade_bonus = $grades->where('bonus_type', 1)->pluck('max_point', 'id');
        $bonus_i = [];
        foreach ($grades as $grade) {
            $grade_id_data[] = $grade->id;
            if ($grade->bonus_type) {
                $bonus_i[count($grade_id_data) - 1] = $grade->max_point;
            }
        }

        $group_array_name = $groups->pluck('group_name', 'id');
        $group_array_id = $groups->pluck('id', 'id');
        $group_members = GroupMember::whereIn('groupid', $group_array_id)->get();
        foreach ($group_members as $member) {
            $member->load('user');
            $member->user_code = $member->user->user_code ?? $member->member_login;
            $group_name = $groups->firstWhere('id', $member->groupid);
            $group_member_array[$group_name->group_name][$member->user_code] = $member->member_login;
        }

        $group_grade_id_text = $group_grades->implode('id', ',');
        foreach ($users as $key => $item) {
            $current_key = "Dòng $key:";
            if (isset($item[0]) && isset($item[1])) {
                $user_code = $item[0];
                $group_id = $item[1];

                if (!$group_array_name->contains($group_id)) {
                    $reports[] = "Lớp $item[1] - $item[0] Mã lớp không thuộc khoá học này";
                    continue;
                }

                if (isset($item[0]) && empty($item[0])) {
                    $reports[] = "SV thứ $key của lớp $item[1] có vấn đề";
                    continue;
                }

                $grmeberCheck = $group_member_array[$group_id][$user_code] ?? null;
                if (!isset($grmeberCheck)) {
                    $reports[] = "Lớp $item[1] - $item[0] Sinh viên không thuộc khoá học này";
                    continue;
                }

                for ($i = 0; $i < $grade_length; $i++) {
                    // kiểm tra không có điểm
                    if (!isset($item[$i + 2])) {
                        continue;
                    }

                    $point = $item[$i + 2];
                    if ($point === null) {
                        continue;
                    }

                    if (isset($bonus_i[$i])) {
                        if ($point < 0 || $point > $bonus_i[$i]) {
                            $reports[] = "Lớp $item[1] - $item[0] Điểm thưởng không nằm trong phạm vi 0-" . $bonus_i[$i];
                            continue;
                        }
                    } else {
                        if ($point < 0 || $point > 10) {
                            $reports[] = "Lớp $item[1] - $item[0] Điểm số không nằm trong phạm vi 0-10";
                            continue;
                        }
                    }

                    $grade_data[$group_id][$group_member_array[$group_id][$user_code]][$grade_id_data[$i]] = $point;
                }
            }
        }

        if (count($grade_data)) {
            $FUGERepository = new FUGERepository();
            foreach ($grade_data as $key => $data) {
                $group_id = $groups->firstWhere('group_name', $key)->id;
                // kiểm tra các lớp thi lại
                $groupCheck = $group = Group::where('is_virtual', 0)
                    ->where('id', $group_id)
                    ->count();
                if ($groupCheck == 0) {
                    continue;
                }

                DB::beginTransaction();
                try {
                    $groupObj = $groups->firstWhere('group_name', $key);
                    $syllabus_grade_info = $FUGERepository->gradeInformationFUGE($group_grade_id_text, $groupObj->id);
                    $reportAdd = $this->createOrUpdateCourseGradeFUGE($syllabus_grade_info, $groupObj, $whoIs, $course_id, $term, $data);
                    $FUGERepository->syncDataSaveGradePointFUGE($group_id);
                    DB::commit();
                } catch (\Throwable $th) {
                    DB::rollback();
                    $reports[] = "Lớp " . $key . " Lỗi import!";
                    continue;
                }

                $reports = array_merge($reports, $reportAdd);
                $reports[] = "Lớp " . $key . " đã hoàn tất quá trình import!";
            }
        }

        $reports[] = "Môn " . $dataUpdate['subject_code'] . " quá trình import hoàn tất";
        return $reports;
    }

    /**
     *
     */

    public function createOrUpdateCourseGradeFUGE($syllabus_grade_info, $groupObj, $whoIs, $course_id, $term, $grades = [])
    {
        $reports = [];
        $grade_lock = [];
        foreach ($grades as $user_login => $grade) {
            $listGrade = CourseGrade::where('groupid', $groupObj->id)->where('login', $user_login)->get();
            foreach ($grade as $grade_id => $point) {
                if ($point === "" || $point === null) {
                    continue;
                }

                // $has_point = CourseGrade::where('groupid', $groupObj->id)->where('grade_id', $grade_id)->where('login', $user_login)->first();
                $has_point = $listGrade->where('grade_id', $grade_id)->first();
                if ($point !== null && trim($point) != '') {
                    $grade_lock[$grade_id] = $grade_id;
                    if ($has_point) {
                        $old_point = $has_point->val;
                        if ($groupObj->is_lock == 1) {
                            $reports[] = "Lớp $groupObj->group_name ($groupObj->id) Giáo viên cố ý thay đổi điểm " . ($syllabus_grade_info[$grade_id]['grade_name'] ?? "") . " của sinh viên $user_login từ $old_point => $point khi lớp đã khóa!";

                            continue;
                        }

                        if ($has_point->locked == 1) {
                            if ($old_point != $point) {
                                $reports[] = "Lớp $groupObj->group_name ($groupObj->id) Giáo viên cố ý thay đổi điểm " . ($syllabus_grade_info[$grade_id]['grade_name'] ?? "") . " của sinh viên $user_login từ $old_point => $point khi điểm đã khóa!";
                            }
                            continue;
                        }

                        $has_point->modifier_login = $whoIs->user_login ?? $whoIs;
                        $has_point->token = $syllabus_grade_info[$grade_id]['token'];
                        $has_point->val = $point;
                        $has_point->locked = 1;
                        $has_point->grade_minimum_required = $syllabus_grade_info[$grade_id]['minimum_required'];
                        $has_point->grade_weight = $syllabus_grade_info[$grade_id]['weight'];
                        $has_point->grade_name = $syllabus_grade_info[$grade_id]['grade_name'];
                        $has_point->save();
                        if ($old_point != $point) {
                            $this->systemLog('group', 'update', "update grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $point, old value: $old_point", $user_login, $groupObj->id, 0, '', '', 'grade');
                            $reports[] = "update grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $point, old value: $old_point";
                        }
                    } else {
                        CourseGrade::create([
                            'course_id' => $course_id,
                            'grade_id' => $grade_id,
                            'grade_minimum_required' => $syllabus_grade_info[$grade_id]['minimum_required'],
                            'grade_weight' => $syllabus_grade_info[$grade_id]['weight'],
                            'grade_name' => $syllabus_grade_info[$grade_id]['grade_name'],
                            'groupid' => $groupObj->id,
                            'login' => $user_login,
                            'val' => $point,
                            'comment' => '',
                            'creator_login' => $whoIs->user_login ?? $whoIs,
                            'modifier_login' => $whoIs->user_login ?? $whoIs,
                            'grade_group_id' => $syllabus_grade_info[$grade_id]['grade_group_id'],
                            'grade_group_name' => $syllabus_grade_info[$grade_id]['grade_group_name'],
                            'grade_group_weight' => $syllabus_grade_info[$grade_id]['grade_group_weight'],
                            'grade_group_minimum_required' => $syllabus_grade_info[$grade_id]['grade_group_minimum_required'],
                            'syllabus_id' => $syllabus_grade_info[$grade_id]['syllabus_id'],
                            'subject_id' => $syllabus_grade_info[$grade_id]['subject_id'],
                            'is_used' => 0,
                            'subject_name' => $syllabus_grade_info[$grade_id]['subject_name'],
                            'course_group_name' => '',
                            'subject_code' => '',
                            'term_id' => $term['term_id'],
                            'term_name' => $term['term_name'],
                            'is_final' => $syllabus_grade_info[$grade_id]['is_final_exam'],
                            'is_resit' => $syllabus_grade_info[$grade_id]['is_resit'],
                            'master_grade' => $syllabus_grade_info[$grade_id]['master_grade'],
                            'token' => $syllabus_grade_info[$grade_id]['token'],
                            'group_val' => 0,
                            'temp' => 0,
                            'locked' => 1,
                        ]);
                        $this->systemLog('group', 'insert', "insert grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $point", $user_login, $groupObj->id, 0, '', '', 'grade');
                    };
                } else {
                    if ($has_point) {
                        $old_point = $has_point->val;
                        $has_point->delete();
                        $reports[] = "delete grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $old_point";
                        $this->systemLog('group', 'delete', "delete grade: " . $syllabus_grade_info[$grade_id]['grade_name'] . " for student $user_login with value: $old_point", $user_login, $groupObj->id, 0, '', '', 'grade');
                    }
                }
            }
        }

        return $reports;
    }

    /**
     * Lấy dữ liệu cho filter xuất bảng điểm
     *
     * @return void
     */
    public function getInfoExportGradeBook(Request $request)
    {
        try {
            $current_date = date('Y-m-d');
            $list_term = Term::orderBy('id', 'desc')->take(10)->get([
                'id',
                'term_name',
            ]);
            $list_block = [];
            $list_group = [];
            $list_subject = [];

            if (isset($request->term_id) && $request->term_id > 0) {
                $list_block = Block::where('term_id', '=', $request->term_id)->whereDate('start_day', '<=', $current_date)->orderBy('id', 'desc')->get(
                    [
                        'id',
                        'block_name',
                    ]
                );

                $query_list_subject = Group::where('pterm_id', '=', $request->term_id);

                $query_list_group = Group::where('pterm_id', '=', $request->term_id);

                if (isset($request->block_id) && $request->block_id > 0) {

                    $list_group = $query_list_group->where('block_id', '=', $request->block_id);

                    $query_list_subject = $query_list_subject->where('block_id', '=', $request->block_id);
                }
                if (isset($request->subject_codes) && count($request->subject_codes) > 0) {
                    $request->subject_codes = array_map(function ($subject_code) {
                        return json_decode($subject_code)->subject_code;
                    }, $request->subject_codes);
                    $query_list_group = $query_list_group->whereIn('psubject_code', $request->subject_codes);
                }

                $list_subject = $query_list_subject->groupBy('psubject_code')->get([
                    'psubject_code as subject_code',
                    'psubject_name as subject_name',
                ]);
                $list_group = $query_list_group->orderBy('group_name', 'asc')->get([
                    'id',
                    'group_name',
                ]);
            }
            return response([
                'list_term' => $list_term,
                'list_block' => $list_block,
                'list_group' => $list_group,
                'list_subject' => $list_subject,
                'has_queue_export' => Cache::has('export_gradebook')
            ], 200);
        } catch (\Throwable $th) {
            $list_term = [];
            $list_block = [];
            $list_group = [];
            $list_subject = [];
            Log::error(`---------------getInfoExportGradeBook------------: \n` . $th);
            return response([
                'list_term' => $list_term,
                'list_block' => $list_block,
                'list_group' => $list_group,
                'list_subject' => $list_subject,
                'has_queue_export' => Cache::has('export_gradebook')
            ], 500);
        }
    }

    public function getExportGradeBook(Request $request)
    {
        try {
            if (isset($request->group_ids) && count($request->group_ids) > 0) {
                $request->group_ids = array_map(function ($group) {
                    return json_decode($group)->id;
                }, $request->group_ids);
            }
            if (isset($request->subject_codes) && count($request->subject_codes) > 0) {
                $request->subject_codes = array_map(function ($subject_code) {
                    return json_decode($subject_code)->subject_code;
                }, $request->subject_codes);
            }
            $block_id = 0;
            if (isset($request->block_id) && $request->block_id > 0) {
                $block_id = $request->block_id;
            }

            $data = (object)[
                'term_id' => $request->term_id,
                'block_id' => $block_id,
                'group_ids' => $request->group_ids,
                'subject_codes' => $request->subject_codes,
            ];
            $before = Storage::disk('gradebook')->allFiles(DIRECTORY_SEPARATOR . 'proactive');
            if (Cache::has('export_gradebook')) {
                return response([
                    'result' => 0,
                    'message' => 'Tồn tại tiến trình xuất bảng điểm, vui lòng chờ tiến trình xuất bảng điểm hoàn thành!',
                ], 200);
            } else {
                dispatch((new ExportGradebook($data, auth()->user()->user_login))->onConnection('grades-queue')->onQueue('grades-queue'));
            }
            $has_started = Cache::has('export_gradebook');
            sleep(2);
            if (Cache::has('export_gradebook')) {
                return response([
                    'result' => 1,
                    'message' => 'Tiến trình xuất bảng điểm đã được đẩy vào hàng đợi!',
                ], 200);
            } else {
                $after = Storage::disk('gradebook')->allFiles(DIRECTORY_SEPARATOR . 'proactive');
                $diff = array_diff($after, $before);
                if (count($diff) > 0 || $has_started) {
                    return response([
                        'result' => 1,
                        'message' => 'Hoàn thành xuất bảng điểm!',
                    ], 200);
                } else {
                    return response([
                        'result' => 0,
                        'message' => 'Khởi động tiến trình xuất bảng điểm thất bại!',
                    ], 200);
                }
            }

            return response([
                'result' => 1,
                'message' => 'Tiến trình xuất bảng điểm đã được đẩy vào hàng đợi!',
            ], 200);
        } catch (\Throwable $th) {
            Log::error(`getExportGradeBook error: \n` . $th);
            return response([
                'result' => 0,
            ], 500);
        }
    }


    /**
     * export file csv danh sách điểm theo lớp
     *
     * @param  mixed $data [required term_id | optional: block_id, group_ids, subject_codes]
     * @param  bool $is_auto [optional: is_auto] - default tự động tạo file vào storage/app/public/gradebook/auto
     * @return bool $result thành công hay không
     */
    public static function exportGradeBook($data, $by_user_login = null)
    {
        try {
            ini_set("pcre.backtrack_limit", "20000000");
            ini_set('max_execution_time', '14400');
            ini_set('memory_limit', '-1');

            if (Cache::has('export_gradebook')) {
                return 0;
            } else {
                Cache::put('export_gradebook', 1, 18000);
            }

            if (!isset($data->term_id)) {
                return false;
            }
            $groups = Group::with('groupMembers')
                ->when($data, function ($query, $data) {
                    $query->where('pterm_id', '=', $data->term_id);
                    if (isset($data->block_id) && $data->block_id > 0) {
                        $query->where('block_id', '=', $data->block_id);
                    }
                    if (isset($data->group_ids) && count($data->group_ids) > 0) {
                        $query->whereIn('id', $data->group_ids);
                    } else {
                        if (isset($data->subject_codes) && count($data->subject_codes) > 0) {
                            $query->whereIn('psubject_code', $data->subject_codes);
                        }
                    }
                })
                // ->where('list_group.is_virtual', 0)
                ->orderBy('id', 'desc')->get();
            $list_export_data = new stdClass();
            $history_status = config('status')->history_status;
            foreach ($groups as $group) {
                $grade_temp = [];
                $grade_list = [];
                $grade_filter = [];
                $group_grade_id = [];
                $grade_lock_list = $group->grade_lock_list;
                if ($grade_lock_list != '') {
                    $grade_lock_list = explode(',', $grade_lock_list);
                } else {
                    $grade_lock_list = [];
                }
                $course = Course::find($group->body_id);
                $group_grades = GradeGroup::where('syllabus_id', $group->syllabus_id)
                    ->where('subject_id', $group->psubject_id)
                    ->orderBy('id')
                    ->get();
                $grades = Grade::select('id', 'grade_name', 'grade_group_id', 'weight', 'minimum_required', 'is_final_exam', 'bonus_type', 'max_point')
                    ->where('subject_id', $group->psubject_id)
                    ->where('syllabus_id', $group->syllabus_id)
                    ->orderBy('id', 'ASC')
                    ->get();
                foreach ($grades as $grade) {
                    $grade_list[$grade->id] = [
                        'grade_name' => $grade->grade_name,
                        'grade_weight' => $grade->weight,
                        'point' => '-',
                    ];
                }
                $export_data = (object)[
                    'group_name' => $group->group_name,
                    'subject_code' => $group->psubject_code,
                    'term_name' => $group->pterm_name,
                    'grades' => $grades,
                    'group_members' => [],
                ];

                foreach ($group_grades as $item) {
                    $group_grade_id[] = $item->id;
                    $tb_temp_grade[$item->id]['weight'] = $item->weight;
                    $tb_temp_grade[$item->id]['tb_grade'] = 0;
                    $tb_temp_grade[$item->id]['minimum_required'] = $item->minimum_required ?? 0;

                    foreach ($grades as $grade) {
                        if ($item->id == $grade->grade_group_id) {
                            $grade_filter[] = $grade;
                        }
                    }
                }
                foreach ($grade_filter as $item) {
                    $item->locked = in_array($item->id, $grade_lock_list) ? 1 : 0;
                    $grade_temp[$item->id] = [
                        'point' => null,
                        'weight' => $item->weight,
                        'minimum_required' => $item->minimum_required,
                        'is_final_exam' => $item->is_final_exam,
                        'grade_group_id' => $item->grade_group_id,
                        'pass' => false,
                        'bonus_type' => $item->bonus_type,
                        'locked' => 0,
                        'max_point' => $item->max_point,
                    ];
                }
                foreach ($group->groupMembers as $member) {
                    $group_grade_list = $grade_list;
                    $member->user_code = $member->user->user_code ?? "";
                    $member->full_name = $member->user->full_name ?? "";
                    if (!isset($member->user->user_code)) {
                        Log::error("exportGradeBook error: group_id " . $member->groupid . "| member_login: " . $member->member_login);
                    }
                    $my_grades = CourseGrade::where('groupid', $member->groupid)->when(0, function ($query) {
                        $query->where('is_final', 0);
                    })->where('login', $member->member_login)->orderBy('grade_id', 'ASC')->get();
                    foreach ($my_grades as $my_grade) {
                        $group_grade_list[$my_grade->grade_id]['point'] = $my_grade->val;
                    }
                    $member->grades = $group_grade_list;
                    $member->result = CourseResult::where('student_login', $member->member_login)->where('groupid', $member->groupid)->first();
                    if (!$member->result) {
                        throw new \Exception("Không tìm thấy lịch sử học lớp $member->groupid của sinh viên $member->member_login.");
                        return false;
                    }
                    $tbGrade = 0;
                    $danger_point = 0;
                    $idGradeGroup = 0;
                    $temp = $grade_temp;
                    $status_subject = $member->result->val;
                    $grades_temp = CourseGrade::where('login', $member->member_login)->where('groupid', $member->groupid)->whereIn('grade_group_id', $group_grade_id)->get();
                    foreach ($grades_temp as $item) {
                        $temp[$item->grade_id]['point'] = $item->val;
                        $temp[$item->grade_id]['locked'] = $item->locked;
                        if ($temp[$item->grade_id]['point'] >= $temp[$item->grade_id]['minimum_required']) {
                            $temp[$item->grade_id]['pass'] = true;
                        }
                        // Kiểm tra sinh viên có trượt do nhóm điểm thành phần không
                        if ($status_subject == 0) {
                            // loai điểm, nhóm điểm kết thúc môn, điểm bonus
                            if ($temp[$item->grade_id]['is_final_exam'] == 1)    continue;
                            if ($temp[$item->grade_id]['bonus_type'] == 1)       continue;
                            // kiểm tra có đầu điểm nào trượt trong nhóm không phải nhóm điểm kết thúc môn
                            if (!$temp[$item->grade_id]['pass']) {
                                $danger_point++;
                            }
                            $idGradeGroup = $item['grade_group_id'];
                            if ($tb_temp_grade[$idGradeGroup]['weight'] != 0) {
                                $tbGrade += floatval($temp[$item->grade_id]['point']) * ($temp[$item->grade_id]['weight'] / $tb_temp_grade[$idGradeGroup]['weight']);
                            }
                            $tb_temp_grade[$item->id]['tb_grade'] = 0;
                        }
                    }
                    if ($idGradeGroup != 0) {
                        if ($tbGrade < $tb_temp_grade[$idGradeGroup]['minimum_required']) {
                            $danger_point++;
                        }
                        if ($danger_point > 0) {
                            $status_subject = -2;
                        }
                    }
                    $member->result['status_subject'] = $history_status[$status_subject] ?? "";
                    $export_data->group_members[] = $member;
                }
                $list_export_data->{$group->psubject_code}[] = $export_data;
            }
            $export = new GradeBookExport($list_export_data);
            if ($by_user_login == null) {
                $name = 'gradebook_' . date('YmdHis');

                $exist = Storage::disk('gradebook')->allFiles(DIRECTORY_SEPARATOR . 'auto');
                rsort($exist);
                for ($i = 4; $i < count($exist); $i++) {
                    Storage::disk('gradebook')->delete($exist[$i]);
                }
                $name = DIRECTORY_SEPARATOR . "auto" . DIRECTORY_SEPARATOR . $name;
            } else {
                $exist = Storage::disk('gradebook')->allFiles(DIRECTORY_SEPARATOR . 'proactive');
                rsort($exist);
                for ($i = 1; $i < count($exist); $i++) {
                    Storage::disk('gradebook')->delete($exist[$i]);
                }
                $name = 'gradebook_' . date('YmdHis') . "_" . $by_user_login;
                $name = DIRECTORY_SEPARATOR . "proactive" . DIRECTORY_SEPARATOR . $name;
            }
            Excel::store($export, "$name.xlsx", 'gradebook');
            unset($export);
            unset($list_export_data);
            unset($groups);
            unset($grade_temp);
            unset($course);
            unset($grades);
            Cache::forget('export_gradebook');
            return true;
        } catch (\Throwable $th) {
            Cache::forget('export_gradebook');
            Log::error(`exportGradeBook error: \n` . $th);
            throw new \Exception(`exportGradeBook error: \n` . $th);
            return false;
        }
    }

    /**
     * lấy danh sách những file bảng điểm đã tồn tại trong storage/app/public/gradebook/
     *
     * @return response
     */
    public function getListExistedFileGradebook()
    {
        try {
            $exist_auto = Storage::disk('gradebook')->allFiles(DIRECTORY_SEPARATOR . 'auto');
            $exist_auto = collect($exist_auto)->sort()->reverse()->toArray();

            $response_exist_auto = [];
            foreach ($exist_auto as $file_path) {
                $date_string = explode(".", explode("_", $file_path)[1])[0];
                $ymd = Carbon::createFromFormat('YmdHis', $date_string)->format('d/m/Y H:i:s');
                $response_exist_auto[] = [
                    'file_name' => $file_path,
                    'date_time' => $ymd,
                ];
            }
            $exist_proactive = Storage::disk('gradebook')->allFiles(DIRECTORY_SEPARATOR . 'proactive');
            $exist_proactive = collect($exist_proactive)->sort()->reverse()->toArray();

            $response_exist_proactive = [];
            foreach ($exist_proactive as $file_path) {
                $user_login = explode(".", explode("_", $file_path)[2])[0];
                $date_string = explode(".", explode("_", $file_path)[1])[0];
                $ymd = Carbon::createFromFormat('YmdHis', $date_string)->format('d/m/Y H:i:s');
                $response_exist_proactive[] = [
                    'file_name' => $file_path,
                    'date_time' => $ymd,
                    'user_login' => $user_login,
                ];
            }

            return response([
                'exist_auto' => $response_exist_auto,
                'exist_proactive' => $response_exist_proactive,
                'has_queue_export' => Cache::has('export_gradebook')
            ], 200);
        } catch (\Throwable $th) {
            Log::error(`getListExistedFileGradebook error: \n` . $th);
            return response([
                'exist_auto' => [],
                'exist_proactive' => [],
                'has_queue_export' => Cache::has('export_gradebook')
            ], 500);
        }
    }

    /**
     * tải file bảng điểm từ storage/app/public/gradebook/
     *
     * @param  mixed $request file_name
     * @return void
     */
    public function getDownloadExistedFileGradebook(Request $request)
    {
        $exist = Storage::disk('gradebook')->exists($request->file_name);
        if ($exist) {
            return response()->download(storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'gradebook' . DIRECTORY_SEPARATOR . $request->file_name));
        }
        return response()->json([
            'message' => 'File not found',
        ], 404);
    }

    public function gradeView(Request $request)
    {
        $term_id = $request->get('term_id', null);
        $terms = Term::orderBy('startday', 'desc')->get();
        $countFinishGroup = 0;

        if ($term_id) {
            $countFinishGroup = Group::where('finished', '<', 2)
                ->where('list_group.is_virtual', 0)
                ->where('pterm_id', $term_id)
                ->count();
            $term = Term::find($term_id);
        } else {
            $last_term = $terms[0];
            $term_id = $last_term->id;
        }

        $datas = $this->getGradeDataByTerm($term_id);

        return $this->view('grade_view.index', [
            'terms' => $terms,
            'datas' => $datas,
            'countFinishGroup' => $countFinishGroup,
            'term' => $term ?? null
        ]);
    }

    public function chotKy(Request $request)
    {
        $term_id = $request->get('term_id', null);
        try {
            DB::beginTransaction();
            Group::where('pterm_id', $term_id)
                ->where('list_group.is_virtual', 0)
                ->update([
                    'finished' => 2,
                ]);

            CourseResult::where('term_id', $term_id)->update([
                'is_finish' => 2,
            ]);

            Group::where('finished', '>', 0)
                ->where('list_group.is_virtual', 0)
                ->where('is_locked', 0)
                ->where('pterm_id', $term_id)
                ->update([
                    'is_locked' => 1,
                ]);

            Term::where('id', $term_id)->update(['is_locked' => 1]);
            DB::commit();
            return redirect()->back()->with('message', 'Chốt kỳ thành công');
        } catch (\Exception $ex) {
            //throw $th;
            Log::error("chotKy error: \n" . $ex);
            DB::rollBack();
            throw new \Exception("exportGradeBook error: \n" . $ex);
        }
    }

    public function bangDiemKyView(Request $request, $id)
    {
        $tong = CourseResult::join('list_group', 't7_course_result.groupid', '=', 'list_group.id')
            ->where('term_id', $id)
            ->where('list_group.is_virtual', 0)
            // ->groupBy('student_login')
            ->count(DB::raw('DISTINCT student_login'));
        $ipp = 50; // ipp: số phiếu trên một trang
        $tong_trang = (int)$tong / $ipp + 1;
        $term = Term::find($id);

        return $this->view('grade_view.bang_diem_ky', [
            'tong' => $tong,
            'tong_trang' => round($tong_trang),
            'term' => $term,
            'ipp' => $ipp
        ]);
    }

    public function bangDiemKy($term_id, $page, $block, $userCode = null)
    {
        try {
            ini_set("max_execution_time", '0');
            ini_set("memory_limit", '2056M');
            $term_ds = Term::findOrFail($term_id);
            if ($userCode != null) {
                $studentCheck = User::where('user_code', $userCode)
                    ->first();
                CourseResult::join('subject', 't7_course_result.psubject_code', '=', 'subject.subject_code')
                    ->where('t7_course_result.term_id', $term_id)
                    ->where('t7_course_result.student_login', $studentCheck->user_login)
                    ->update(['t7_course_result.number_of_credit' => 'subject.num_of_credit']);

                User::where('user_login', $studentCheck->user_login)
                    ->update(['ky_phieu_bao_diem' => '0', 'tinh_trang_theo_ky' => '0']);

                $student_ky_phieu_bao_diem = CourseResult::select('student_login')
                    ->distinct()
                    ->where('term_id', $term_id)
                    ->where('t7_course_result.student_login', $studentCheck->user_login)
                    ->get()
                    ->pluck('student_login');

                User::whereIn('user_login', $student_ky_phieu_bao_diem)
                    ->where('user_login', $studentCheck->user_login)
                    ->update(['ky_phieu_bao_diem' => $term_id]);

                $studentLogins = GroupMember::select('member_login as student_logins')
                    ->distinct()
                    ->where('term_id', $term_id)
                    ->get()
                    ->pluck('student_login');

                User::whereIn('user_login', $studentLogins)
                    ->where('user_login', $studentCheck->user_login)
                    ->update(['tinh_trang_theo_ky' => '1']);
            }

            $term_name = $term_ds->term_name;
            $tong = User::where('ky_phieu_bao_diem', $term_id)->orWhere('tinh_trang_theo_ky', 1)->count();
            $ipp = 50;
            $tong_trang = (int)$tong / $ipp + 1;
            $trang_hien_tai = $page ? $page - 1 : 0;
            $start = $trang_hien_tai * $ipp;
            for ($i = 0; $i < $tong_trang; $i++) {
                $page = $i + 1;
                $page_arr[$i] = (object)array("name" => "$page", "value" => "$i");
            }


            $blocks = Block::where('term_id', $term_id)->get();
            $blocks = $blocks->toArray();
            $hk_text = 'Kết quả ';

            //ini_set("display_errors",1);
            $styleTable = array('borderSize' => 6, 'borderColor' => '006699', 'cellMargin' => 10);
            $styleFirstRow = array('borderBottomSize' => 18, 'borderBottomColor' => '0000FF', 'bgColor' => '66BBFF');
            // New Word Document\
            //Config area
            $name_row_height = 200;
            $nameColWidth = 5000;
            $name_col_width3 = 2000;
            $khen_thuong = 2200;
            $noidungkt = 6000;
            $khoang_trang = 4000;
            $diem = 2000;
            $tt = 400;
            $ma_mon = 1500;
            $tinh_trang = 2000;
            $dau_cach = 800;
            $mon_hoc = 7000;
            $he_so = 1000;
            $diem_so = 1000;
            $mssv_style = 1200;
            $ten_style = 3300;
            //EnD config Area

            $PHPWord = new PHPWord();
            /// formating area
            $PHPWord->addFontStyle('rStyle', array('bold' => true, 'size' => 16, 'name' => 'Times New Roman'));
            $PHPWord->addFontStyle('tStyle', array('bold' => true, 'underline' => true, 'size' => 9, 'name' => 'Times New Roman'));
            $PHPWord->addFontStyle('eStyle', array('italic' => true, 'size' => 12, 'align' => 'center', 'name' => 'Times New Roman'));
            $PHPWord->addParagraphStyle('pStyle', array('align' => 'center', 'spaceAfter' => 0));
            $addParagraphStyle1 = array('align' => 'left', 'spaceAfter' => 0);
            $addParagraphStyle = array('align' => 'center', 'spaceAfter' => 0);
            $styleCell = array('align' => 'center', 'size' => 7, 'name' => 'Times New Roman', 'cellMarginBottom' => 0);
            $fontStyle = array('align' => 'center', 'name' => 'Times New Roman');
            $fontstyletieude = array('align' => 'center', 'size' => 13, 'name' => 'Times New Roman');
            $fontstyletieudebold = array('bold' => true, 'align' => 'center', 'size' => 13, 'name' => 'Times New Roman');
            $fontStylebold = array('bold' => true, 'name' => 'Times New Roman');
            $chuky = array('bold' => true, 'size' => 12, 'name' => 'Times New Roman');
            $styleTable = array('borderSize' => 2, 'borderColor' => '006699', 'cellMargin' => 10, 'cellMarginBottom' => 0);
            $styleFirstRow = array('borderBottomSize' => 2, 'borderBottomColor' => '0000FF');
            $styleTable1 = array('borderColor' => '006699', 'cellMargin' => 0,);
            $styleFirstRow1 = array('borderBottomColor' => '0000FF');

            $PHPWord->addTableStyle('bangdiem', $styleTable, $styleFirstRow);
            $PHPWord->addTableStyle('mon', $styleTable, $styleFirstRow);
            $PHPWord->addTableStyle('chuky', $styleTable1, $styleFirstRow1);
            $PHPWord->addTableStyle('monkymoi', $styleTable, $styleFirstRow);
            $PHPWord->addTableStyle('monky', $styleTable1, $styleFirstRow1);

            $ds_students = User::select(
                'user.user_code AS student_code',
                'user.user_login AS student_login',
                'user.user_address AS student_address',
                'user.user_DOB AS dob',
                'user.user_code',
                'user.study_status',
                'user.curriculum_id',
                DB::raw("CONCAT(TRIM(user.user_surname), ' ', TRIM(CONCAT(TRIM(user.user_middlename), ' ', TRIM(user.user_givenname)))) AS fullname")
            )
                ->join('t7_course_result', 'user.user_login', '=', 't7_course_result.student_login')
                ->join('list_group', 't7_course_result.groupid', '=', 'list_group.id')
                ->where('list_group.is_virtual', 0)
                ->where('t7_course_result.term_id', $term_id)
                ->groupBy('user.user_code');
                // ->orWhere('tinh_trang_theo_ky', 1);
            if ($userCode != null) {
                $ds_students = $ds_students->where('user_login', $studentCheck->user_login);
            }

            $ds_students = $ds_students->orderBy('user_code', 'ASC')
                ->skip($start)
                ->take($ipp)
                ->get();

            $ds_student  = $ds_students->toArray();

            $trangthai = array();
            $trangthai[0] = "Đạt";
            $trangthai[1] = "Không đạt";
            $trangthai[2] = "Đang học";
            $trangthai[3] = "Đã tham gia";

            $xep_loai = array();
            $xep_loai[-2] = "Xuất sắc";
            $xep_loai[-1] = "Giỏi";
            $xep_loai[0] = "Khá";
            $xep_loai[1] = "Trung bình khá";
            $xep_loai[2] = "Trung bình";
            $xep_loai[3] = "Không đạt";

            $subjectNoScore = [
                'SH',
                'OT',
                'HN1',
                'HN2',
                'SHTT',
                'DH',
                'DH1',
                'DH2',
            ];

            $subjectNoScoreName = [
                'SH' => "Sinh hoạt",
                'OT' => "Ôn tập",
                'HN1' => "Hướng nghiệp 1",
                'HN2' => "Hướng nghiệp 2",
                'SHTT' => "Sinh hoạt trực tuyến",
                'DH' => "Định hướng",
                'DH1' => "Định hướng 1",
                'DH2' => "Định hướng 2"
            ];

            //Get trạng thái kỳ trước all
            $previous_term_status_all_objs = AcademicDecisionManagement::select('academic_decision_students.student_login', 'academic_decision_management.drop_type')
                ->join('academic_decision_students', 'academic_decision_management.id', '=', 'academic_decision_students.drop_out_id')
                ->where('academic_decision_management.term_id', $term_id)
                ->groupBy('academic_decision_students.student_login')
                ->get();
            $previous_term_status_all = [];
            foreach ($previous_term_status_all_objs as $key => $value) {
                $previous_term_status_all[$value->student_login] = $value;
            }

            //Lấy kỳ thứ trước all
            $period_ordering_all_objs = StudentSubject::select('student_login', 'period_ordering')->groupBy('student_login')->get();

            $period_ordering_all = [];
            foreach ($period_ordering_all_objs as $key => $value) {
                $period_ordering_all[$value->student_login] = $value;
            }

            //Get nganh by curriculum_id  all
            $nganh_all_objs = CurriCulum::select('id', 'chuyen_nganh')->groupBy('id')->get();
            $nganh_all = [];
            foreach ($nganh_all_objs as $key => $value) {
                $nganh_all[$value->id] = $value;
            }

            //Get diem trugn binh all
            $diem_tb_all_objs  = CourseResult::select([
                't7_course_result.student_login',
                DB::raw('ROUND(SUM(t7_course_result.grade * subject.num_of_credit) / SUM(subject.num_of_credit), 1) AS diem')
            ])
                // ->selectRaw(', ')
                ->join('subject', 'subject.subject_code', 't7_course_result.psubject_code')
                ->join('list_group', 't7_course_result.groupid', '=', 'list_group.id')
                ->where('list_group.is_virtual', 0)
                ->where('list_group.pterm_id', $term_id)
                ->where('t7_course_result.is_finish', '>=', 1)
                ->groupBy('t7_course_result.student_login')
                ->get();

            $diem_tb_all = [];
            foreach ($diem_tb_all_objs as $key => $value) {
                $diem_tb_all[$value->student_login] = $value;
            }

            //Get hệ số môn all
            $he_so_mon_all_objs = Subject::select('subject_code', 'num_of_credit')->get();
            $he_so_mon_all = [];
            foreach ($he_so_mon_all_objs as $key => $value) {
                $he_so_mon_all[$value->subject_code] = $value;
            }

            //Get tổng sinh viên dựa vào group_id
            $total_student_all_objs = GroupMember::select('group_member.groupid', DB::raw('COUNT(group_member.id) as total'))
                ->join('list_group', 'group_member.groupid', '=', 'list_group.id')
                ->where('list_group.pterm_id', $term_id)
                ->groupBy('groupid')
                ->get();
            $total_student_all = [];
            foreach ($total_student_all_objs as $key => $value) {
                $total_student_all[$value->groupid] = $value;
            }

            //Get tổng sinh viên pass
            $total_pass_student_all_objs = CourseResult::select('groupid', DB::raw('COUNT(*) as total'))
                ->where('val', '>=', 1)
                ->groupBy('groupid')
                ->get();

            $total_pass_student_all = [];
            foreach ($total_pass_student_all_objs as $key => $value) {
                $total_pass_student_all[$value->groupid] = $value;
            }

            for ($i = 0; $i < count($ds_student); $i++) {
                $obj = &$ds_student[$i];
                $fullname = $obj['fullname'];
                $user_code = $obj['user_code'];
                $address = $obj['student_address'];
                $student_login = trim($obj['student_login']);
                $curriculum_id = $obj['curriculum_id'];
                $nganh = $nganh_all[$curriculum_id]->chuyen_nganh;
                // xuất word
                $section = $PHPWord->addSection(array('marginLeft' => 700, 'marginRight' => 700, 'marginTop' => 500, 'marginBottom' => 300));
                $table4 = $section->addTable('gui');
                $table4->addRow($name_row_height, 'exact');
                $table4->addCell($khoang_trang, $styleCell)->addText('', $fontStyle, $addParagraphStyle1);
                $table4->addCell($khoang_trang, $styleCell)->addText('', $fontStyle, $addParagraphStyle1);
                $table4->addRow($name_row_height, 'exact');
                $table4->addCell($khoang_trang, $styleCell)->addText('', $fontStyle, $addParagraphStyle1);
                $table4->addCell($khoang_trang, $styleCell)->addText('', $fontStyle, $addParagraphStyle1);
                $table4->addRow($name_row_height, 'exact');

                // Add cells
                $table4->addCell($khoang_trang, $styleCell)->addText('', $fontStyle, $addParagraphStyle1);
                $kinh_gui_cell = $table4->addCell($noidungkt, $styleCell);
                $kinhGuiTextrun = $kinh_gui_cell->addTextRun($addParagraphStyle1);
                $kinhGuiTextrun->addText("Kính gửi phụ huynh sinh viên: ", $fontstyletieude);
                $kinhGuiTextrun->addText($fullname, $fontstyletieudebold);

                $table4->addRow($name_row_height, 'exact');
                $table4->addCell($khoang_trang, $styleCell)->addText('', $fontStyle, $addParagraphStyle1);
                $diaChiCell = $table4->addCell($noidungkt, $styleCell);
                $diaChiTextrun = $diaChiCell->addTextRun($addParagraphStyle1);
                $diaChiTextrun->addText("Địa chỉ: ", $fontstyletieude);
                $diaChiTextrun->addText(htmlspecialchars($address), $fontstyletieudebold);
                $section->addText('');
                $section->addText('');
                $section->addText("PHIẾU THÔNG TIN KẾT QUẢ HỌC TẬP", 'rStyle', 'pStyle');
                $section->addText('');
                $table = $section->addTable('thongtin');
                // Add row
                $table->addRow($name_row_height, 'exact');

                // Add cells
                $table->addCell($name_col_width3, $styleCell)->addText('Họ và tên:', $fontStyle, $addParagraphStyle1);
                $table->addCell($ten_style, $styleCell)->addText($fullname, $fontStylebold, $addParagraphStyle1);

                $table->addCell($mssv_style, $styleCell)->addText('MSSV:', $fontStyle, $addParagraphStyle1);
                $table->addCell($ten_style, $styleCell)->addText($user_code, $fontStyle, $addParagraphStyle1);
                $table->addRow($name_row_height, 'exact');

                // Add cells
                $table->addCell($name_col_width3, $styleCell)->addText('Hình thức đào tạo:', $fontStyle, $addParagraphStyle1);
                $table->addCell($ten_style, $styleCell)->addText('Chính quy', $fontStyle, $addParagraphStyle1);
                $table->addCell($mssv_style, $styleCell)->addText('Học kỳ:', $fontStyle, $addParagraphStyle1);
                $table->addCell($ten_style, $styleCell)->addText($term_name, $fontStyle, $addParagraphStyle1);

                $section->addText('I THÔNG TIN HỌC KỲ ' . mb_strtoupper($term_name), 'tStyle');
                $table1 = $section->addTable('sv');
                $table1->addRow($name_row_height, 'exact');
                // Add cells
                $table1->addCell($dau_cach, $styleCell)->addText('', $fontStylebold);
                $table1->addCell($nameColWidth, $styleCell)->addText('1.Tình trạng học tập:', $fontStylebold, $addParagraphStyle1);

                $dsSubjectDetail = CourseResult::select([
                    't7_course_result.psubject_name AS subject_name',
                    't7_course_result.start_date',
                    't7_course_result.end_date',
                    'g.group_name AS group_name',
                    'groupid',
                    'attendance_absent',
                    't7_course_result.total_session',
                    'grade',
                    'val',
                    't7_course_result.psubject_code AS subject_code',
                    't7_course_result.skill_code',
                    'g.block_id',
                    'subject.num_of_credit AS number_of_credit'
                ])
                    ->join('list_group AS g', 't7_course_result.groupid', '=', 'g.id')
                    ->join('subject', 't7_course_result.psubject_code', '=', 'subject.subject_code')
                    //
                    ->where('t7_course_result.term_id', $term_id)
                    ->where('student_login', $student_login)
                    ->where('g.is_virtual', 0)
                    ->orderBy('subject_id')
                    ->get();

                if (count($dsSubjectDetail) == 0) {
                    $table1->addCell($nameColWidth, $styleCell)->addText('- Trạng thái: --', $fontStyle, $addParagraphStyle1);
                    $table1->addRow($name_row_height, 'exact');
                    $table1->addCell($dau_cach, $styleCell)->addText('', $fontStylebold, $addParagraphStyle1);
                    $table1->addCell($nameColWidth, $styleCell)->addText('', $fontStyle, $addParagraphStyle1);
                    $table1->addCell($nameColWidth, $styleCell)->addText('- Ngành: --', $fontStyle, $addParagraphStyle1);
                } else {
                    $table1->addCell($nameColWidth, $styleCell)->addText('', $fontStyle, $addParagraphStyle1);
                    $table1->addRow($name_row_height, 'exact');
                    $table1->addCell($dau_cach, $styleCell)->addText('', $fontStylebold, $addParagraphStyle1);
                    $table1->addCell($nameColWidth, $styleCell)->addText('', $fontStyle, $addParagraphStyle1);
                    $table1->addCell($nameColWidth, $styleCell)->addText('- Ngành: ' . $nganh, $fontStyle, $addParagraphStyle1);
                }
                $table1->addRow($name_row_height, 'exact');
                $table1->addCell($dau_cach, $styleCell)->addText('', $fontStylebold, $addParagraphStyle1);

                if (count($dsSubjectDetail) == 0) {
                    $table1->addCell($nameColWidth, $styleCell)->addText('2.Kết quả học tập: Không có', $fontStylebold, $addParagraphStyle1);
                    $table1->addCell($nameColWidth, $styleCell)->addText(' ', $fontStyle, $addParagraphStyle1);
                } else {
                    $table1->addCell($nameColWidth, $styleCell)->addText('2.Kết quả học tập:', $fontStylebold, $addParagraphStyle1);
                    $table1->addCell($nameColWidth, $styleCell)->addText(' ', $fontStyle, $addParagraphStyle1);
                    $table2 = $section->addTable('bangdiem');
                    $table2->addRow(50, 'exact');
                    $table2->addCell($tt, $styleCell)->addText('TT', $fontStylebold, $addParagraphStyle);
                    $table2->addCell($ma_mon, $styleCell)->addText('Mã môn', $fontStylebold, $addParagraphStyle);
                    $table2->addCell($mon_hoc, $styleCell)->addText('Môn học', $fontStylebold, $addParagraphStyle);
                    $table2->addCell($he_so, $styleCell)->addText('Hệ số ', $fontStylebold, $addParagraphStyle);
                    $table2->addCell($diem_so, $styleCell)->addText('Điểm số', $fontStylebold, $addParagraphStyle);
                    $table2->addCell($tinh_trang, $styleCell)->addText('Lần học', $fontStylebold, $addParagraphStyle);
                    $table2->addCell($tinh_trang, $styleCell)->addText('Trạng thái', $fontStylebold, $addParagraphStyle);
                    $table2->addCell($tinh_trang, $styleCell)->addText('Vắng', $fontStylebold, $addParagraphStyle);
                    $table2->addCell($tinh_trang, $styleCell)->addText('Xếp hạng trong lớp', $fontStylebold, $addParagraphStyle);
                }

                $diemTb = 0;
                // for ($j = 0; $j < count($dsSubjectDetail); $j++) {
                // dd(2, $dsSubjectDetail);
                foreach ($dsSubjectDetail as $j => $obj_diem) {
                    // $obj_diem = $dsSubjectDetail[$j];
                    $subject_name = $obj_diem->subject_name;
                    $start_date = $obj_diem->start_date;
                    $subject_code = $obj_diem->subject_code;

                    $absent = $obj_diem->attendance_absent . "/" . $obj_diem->total_session;
                    $final_result = $obj_diem->grade;
                    $diemTb = $diem_tb_all[$student_login]->diem;
                    $status_val = $obj_diem->val;
                    $groupid = $obj_diem->groupid;
                    $he_so_mon = $he_so_mon_all[$subject_code]->num_of_credit;
                    if (!isset($total_student_all[$groupid])) {
                        continue;
                    }
                    $totalStudent = $total_student_all[$groupid]->total;
                    $totalStudentBetter = CourseResult::where('groupid', $groupid)->where('grade', '>', $final_result)->count() + 1;
                    $rank = $totalStudentBetter . "/" . $totalStudent;
                    $today = date('Y-m-d');
                    if ($status_val == 1) {
                        $diem = 0;
                    }
                    if ($status_val != 1) {
                        $diem = 1;
                    }
                    $diem = $obj_diem->diem ? $obj_diem->diem : $diem;

                    //update lần học thứ:
                    $lanthu1 = CourseResult::where('student_login', $student_login)
                        ->where('psubject_code', $subject_code)
                        ->where('start_date', '<', $start_date)
                        ->count() + 1;
                    $table2->addRow(10, "exact");
                    $table2->addCell($tt, $styleCell)->addText($j + 1, $fontStyle, $addParagraphStyle);
                    $table2->addCell($ma_mon, $styleCell)->addText($subject_code, $fontStyle, $addParagraphStyle);
                    if (in_array($subject_code, $subjectNoScore)) {
                        if (strlen(trim($subject_name)) == 0) {
                            $table2->addCell($mon_hoc, $styleCell)->addText(htmlspecialchars($subjectNoScoreName[$subject_code]), $fontStyle, $addParagraphStyle);
                        } else {
                            $table2->addCell($mon_hoc, $styleCell)->addText(htmlspecialchars($subject_name), $fontStyle, $addParagraphStyle);
                        }

                        $table2->addCell($he_so, $styleCell)->addText("", $fontStyle, $addParagraphStyle);
                        $table2->addCell($diem_so, $styleCell)->addText("", $fontStyle, $addParagraphStyle);
                    } else {
                        $table2->addCell($mon_hoc, $styleCell)->addText(htmlspecialchars($subject_name), $fontStyle, $addParagraphStyle);
                        $table2->addCell($he_so, $styleCell)->addText(htmlspecialchars($he_so_mon), $fontStyle, $addParagraphStyle);
                        $table2->addCell($diem_so, $styleCell)->addText($final_result, $fontStyle, $addParagraphStyle);
                    }

                    $table2->addCell($tinh_trang, $styleCell)->addText($lanthu1, $fontStyle, $addParagraphStyle);
                    $table2->addCell($tinh_trang, $styleCell)->addText(in_array($subject_code, $subjectNoScore) ? $trangthai[3] : $trangthai[$diem], $fontStyle, $addParagraphStyle);
                    $table2->addCell($tinh_trang, $styleCell)->addText((in_array($subject_code, $subjectNoScore) ? "" : $absent), $fontStyle, $addParagraphStyle);
                    $table2->addCell($tinh_trang, $styleCell)->addText((in_array($subject_code, $subjectNoScore) ? "" : $rank), $fontStyle, $addParagraphStyle);
                }
                $xep_loai = array();
                $xep_loai[-2] = "Xuất sắc";
                $xep_loai[-1] = "Giỏi";
                $xep_loai[0] = "Khá";
                $xep_loai[1] = "Trung bình khá";
                $xep_loai[2] = "Trung bình";
                $xep_loai[3] = "Không đạt";

                if ($diemTb >= 9.0 && $diemTb <= 10) {
                    $status = -2;
                }

                if ($diemTb >= 8.0 && $diemTb <= 8.9) {
                    $status = -1;
                }

                if ($diemTb >= 7.0 && $diemTb <= 7.9) {
                    $status = 0;
                }

                if ($diemTb >= 6.0 && $diemTb <= 6.9) {
                    $status = 1;
                }

                if ($diemTb >= 5.0 && $diemTb <= 5.9) {
                    $status = 2;
                }

                if ($diemTb < 5.0) {
                    $status = 3;
                }

                if (count($dsSubjectDetail) == 0) {
                    $section->addText('Điểm trung bình: --Xếp loại: -- ', $fontStylebold, $addParagraphStyle1);
                } else {
                    $section->addText('Điểm trung bình: ' . $diemTb . 'Xếp loại: ' . $xep_loai[$status], $fontStylebold, $addParagraphStyle1);
                }

                //lấy danh sách khen thưởng kỷ luật của sinh viên
                //khen thuong
                $union = Decision::select([
                    'decision.name as decision_name',
                    'decision.decision_num as decision_no',
                    'decision.sign_day as date_affected',
                    DB::raw('1 AS type'),
                    'decision.signer as signee',
                    'decision_user.note as reason',
                    'decision.note'
                ])
                    ->join('decision_user', 'decision.id', '=', 'decision_user.decision_id')
                    ->where('decision.term_id', $term_id)
                    ->where(function ($query) use ($student_login, $user_code) {
                        $query->where('decision_user.user_code', $student_login)
                            ->orWhere('decision_user.user_code', $user_code);
                    })
                    ->where('decision.type', Decision::TYPE_KHEN_THUONG);

                $ds_kt = Discipline::select(
                    't7_discipline.decision_name',
                    't7_discipline.decision_no',
                    't7_discipline.date_affected',
                    't7_discipline.type',
                    't7_discipline.signee',
                    't7_discipline_student.reason',
                    't7_discipline.note'
                )
                    ->join('t7_discipline_student', 't7_discipline.id', '=', 't7_discipline_student.discipline_id')
                    ->where(function ($query) use ($student_login, $user_code) {
                        $query->where('t7_discipline_student.student_login', $student_login)
                            ->orWhere('t7_discipline_student.student_login', $user_code);
                    })
                    ->where('t7_discipline.term_id', $term_id)
                    ->where('t7_discipline.type', 1)
                    ->union($union)
                    ->get();

                $union = Decision::select([
                    'decision.name as decision_name',
                    'decision.decision_num as decision_no',
                    'decision.sign_day as date_affected',
                    DB::raw('2 AS type'),
                    'decision.signer as signee',
                    'decision_user.note as reason',
                    'decision.note'
                ])
                    ->join('decision_user', 'decision.id', '=', 'decision_user.decision_id')
                    ->where(function ($query) use ($student_login, $user_code) {
                        $query->where('decision_user.user_code', $student_login)
                            ->orWhere('decision_user.user_code', $user_code);
                    })
                    ->where('decision.term_id', $term_id)
                    ->where('decision.type', Decision::TYPE_KY_LUAT);

                //kỷ luật
                $ds_kl = Discipline::select([
                    't7_discipline.decision_name',
                    't7_discipline.decision_no',
                    't7_discipline.date_affected',
                    't7_discipline.type',
                    't7_discipline.signee',
                    't7_discipline_student.reason',
                    't7_discipline.note'
                ])
                    ->join('t7_discipline_student', 't7_discipline.id', '=', 't7_discipline_student.discipline_id')
                    ->where(function ($query) use ($student_login, $user_code) {
                        $query->where('t7_discipline_student.student_login', $student_login)
                            ->orWhere('t7_discipline_student.student_login', $user_code);
                    })->where('t7_discipline.term_id', $term_id)
                    ->where('t7_discipline.type', 2)
                    ->union($union)
                    ->get();

                //Hiển thị
                $table1 = $section->addTable('monky');
                $table1->addRow($name_row_height, 'exact');
                // Add cells
                $table1->addCell($dau_cach, $styleCell)->addText('', $fontStylebold, $addParagraphStyle1);
                $table1->addCell($khen_thuong, $styleCell)->addText('3.Khen thưởng/kỷ luật:', $fontStylebold, $addParagraphStyle1);

                if (count($ds_kt) != 0 || count($ds_kl) != 0) {
                    for ($b = 0; $b < count($ds_kt); $b++) {
                        $kt = $ds_kt[$b];
                        $table1->addCell($noidungkt, $styleCell)->addText('Đạt danh hiệu ' . $kt->reason, $fontStyle, $addParagraphStyle1);
                        //$kt->reason;
                    }

                    for ($t = 0; $t < count($ds_kl); $t++) {
                        $kl = $ds_kl[$t];
                        $table1->addCell($noidungkt, $styleCell)->addText('Kỷ luật ' . $kl->reason, $fontStyle, $addParagraphStyle1);
                        //echo "<li>Sinh viên bị kỷ luật <strong>$kl->reason</strong></li>";
                    }
                }

                $table = $section->addTable('diem');
                $table->addRow($name_row_height, 'exact');
                $table->addCell($nameColWidth, $styleCell)->addText(' ', $fontStylebold, $addParagraphStyle);
                $ah_title = !empty($ah_title) ? $ah_title : 'PHÒNG TỔ CHỨC VÀ QUẢN LÝ ĐÀO TẠO';
                $table->addCell($nameColWidth, $styleCell)->addText($ah_title, $fontStylebold, $addParagraphStyle);
                $table->addRow($name_row_height, 'exact');
                $table->addCell($nameColWidth, $styleCell)->addText(' ', $fontStylebold, $addParagraphStyle);

                $signatures  = [
                    'name' => env('HEADMASTER_NAME'),
                    'image' => env('HEADMASTER_IMAGE'),
                ];
                $chu_ky_ah = $signatures['image'] ? $_SERVER["DOCUMENT_ROOT"] . '/images/' . $signatures['image'] : "";
                $name_ah = $signatures['name'];

                if (file_exists($chu_ky_ah)) {
                    $table->addCell($nameColWidth, $styleCell)->addImage($chu_ky_ah, $addParagraphStyle);
                    $table->addRow($name_row_height, 'exact');
                } else {
                    $table->addCell($nameColWidth, $styleCell)->addText(' ', $fontStylebold, $addParagraphStyle);
                    $table->addRow($name_row_height, 'exact');
                    $table->addCell($nameColWidth, $styleCell)->addText(' ', $fontStylebold, $addParagraphStyle);
                    $table->addRow($name_row_height, 'exact');
                    $table->addCell($nameColWidth, $styleCell)->addText(' ', $fontStylebold, $addParagraphStyle);
                    $table->addRow($name_row_height, 'exact');
                    $table->addCell($nameColWidth, $styleCell)->addText(' ', $fontStylebold, $addParagraphStyle);
                    $table->addRow($name_row_height, 'exact');
                }

                $table->addCell($nameColWidth, $styleCell)->addText(' ', $fontStylebold, $addParagraphStyle);
                $table->addCell($nameColWidth, $styleCell)->addText($name_ah, $chuky, $addParagraphStyle);
                $footer = $section->addFooter();
                // Thêm nội dung vào footer
                $textRun = $footer->addTextRun();
                // $textRun->addText('04.28-BM/ĐT/HDCV/FE', array('size' => 5, 'align' => 'left'));
                $textRun->addText('07.01.04-BM/FPL/HDCV/FE', array('size' => 5, 'align' => 'left'));
            }

            // Save File
            $name = $term_name . "_Trang" . ($trang_hien_tai + 1);
            $objWriter = IOFactory::createWriter($PHPWord, 'Word2007');
            $name = str_replace(' ', '_', $name);
            // $outputFileName = "$name.docx";
            // $outputFileName = public_path('storage/documents/'.$name.'.docx');
            $outputDir = storage_path('app/documents');
            if (!file_exists($outputDir)) {
                mkdir($outputDir, 0777, true); // Tạo thư mục nếu chưa tồn tại
            }
            $outputFileName = $outputDir . '/Fall_2024_Trang1.docx';
            $objWriter = IOFactory::createWriter($PHPWord, 'Word2007');
            $objWriter->save($outputFileName);


            $file = $outputFileName;
            // /* Force user to download doc file */
            if (file_exists($file)) {
                header('Content-Description: File Transfer');
                header('Content-Type: application/octet-stream');
                header('Content-Disposition: attachment; filename=' . basename($file));
                header('Content-Transfer-Encoding: binary');
                header('Expires: 0');
                header('Cache-Control: must-revalidate');
                header('Pragma: public');
                header('Content-Length: ' . filesize($file));
                if (ob_get_length()) {
                    ob_clean();
                }
                flush();
                readfile($file);

                /* Alway delete file after done */
                unlink($file);
            }
            return redirect()->back()->with([
                'status' => [
                    'type' => 'success',
                    'messages' => 'Tải xuống thành công!'
                ]
            ]);
        } catch (\Throwable $th) {
            Log::error("bangDiemKy error: \n" . $th->getFile() . " " .  $th->getLine() . ": " . $th->getMessage());
            Log::error($th);
            return redirect()->back()->with([
                'status' => [
                    'type' => 'danger',
                    'messages' => 'Tải xuống thất bại!'
                ]
            ]);
        }
    }


    public function gradeByTerm(Request $request)
    {
        $khoa_nhap_hoc = KhoaNhapHoc::all();
        $terms = Term::orderBy('id', 'desc')->get();
        return $this->view('grade_view.bang_diem_theo_ky', [
            'terms' => $terms,
            'khoa_nhap_hoc' => $khoa_nhap_hoc,
        ]);
    }

    private function getGradeDataByTerm($termId)
    {
        $query = DB::table('t7_course_result')
            ->join('subject', 'subject.subject_code', '=', 't7_course_result.psubject_code')
            ->join('user', 'user.user_login', '=', 't7_course_result.student_login')
            ->join('list_group', 't7_course_result.groupid', '=', 'list_group.id')
            ->where('term_id', $termId)
            ->whereNotIn('t7_course_result.skill_code', ['SH', 'HN1', 'DH']);

        $subSql = $query
            ->selectRaw("
                user.user_login,
                user.user_code,
                CONCAT(user.user_surname,' ',user.user_middlename,' ', user.user_givenname) as full_name,
                ROUND(SUM(t7_course_result.grade * subject.num_of_credit) / SUM(subject.num_of_credit), 2) AS point_avg
            ")
            ->groupBy('student_login')
            ->toSql();

        $datas = DB::table(DB::raw("($subSql) as custom_tbl"))
            ->addBinding($query->getBindings())
            ->select([
                'custom_tbl.user_login',
                'custom_tbl.user_code',
                'custom_tbl.full_name',
                'custom_tbl.point_avg',
                DB::raw("
                    CASE
                        WHEN (point_avg >= 9) THEN 'Xuất sắc'
                        WHEN (point_avg >= 8 AND point_avg < 9) THEN 'Giỏi'
                        WHEN (point_avg >= 7 AND point_avg < 8) THEN 'Khá'
                        WHEN (point_avg >= 6 AND point_avg < 7) THEN 'Trung bình khá'
                        WHEN (point_avg >= 5 AND point_avg < 6) THEN 'Trung bình'
                        WHEN (point_avg < 5  AND point_avg >= 0) THEN 'Yếu'
                        ELSE 'Yếu'
                    END AS status
                ")
            ]);
        return $datas->paginate(20);
    }

    public function showGradeReport(Request $request)
    {
        $termId = $request->get('term_id');
        $khoaHoc = $request->get('khoa_hoc', []);

        $term = Term::find($termId);
        $datas = $this->getGradeDataByTerm($termId, $khoaHoc);

        return view('admin.grade_view.report', [
            'term' => $term,
            'datas' => $datas,
        ]);
    }

    public function gradeByTermExport(Request $request)
    {
        ini_set("max_execution_time", '600');
        ini_set("memory_limit", '-1');
        $khoaHoc = $request->get('khoa_hoc', []);
        $filteredArr = array_filter($khoaHoc, function ($value) {
            return $value !== null;
        });
        $termId = $request->get('term_id', null);
        $term = Term::find($termId);
        if (count($filteredArr) > 0) {
            $strKhoaHoc = "AND user.grade_create IN (" . implode(', ', $khoaHoc) . ")";
        } else {
            $strKhoaHoc = '';
        }

        $datas = DB::connection()
            ->select("SELECT
            custom_tbl.user_login,
            custom_tbl.user_code,
            custom_tbl.full_name,
            point_avg,
            (
                CASE
                    WHEN (point_avg >= 9) THEN 'Xuất sắc'
                    WHEN (point_avg >= 8 AND point_avg < 9) THEN 'Giỏi'
                    WHEN (point_avg >= 7 AND point_avg < 8) THEN 'Khá'
                    WHEN (point_avg >= 6 AND point_avg < 7) THEN 'Trung bình khá'
                    WHEN (point_avg >= 5 AND point_avg < 6) THEN 'Trung bình'
                    WHEN (point_avg < 5  AND point_avg >= 0) THEN 'Yếu'
                    ELSE 'Yếu'
                END
            ) 'status'
        FROM (
            SELECT
                user.user_login,
                user.user_code,
                CONCAT(user.user_surname,' ',user.user_middlename,' ', user.user_givenname) as full_name,
                ROUND(SUM(t7_course_result.grade * subject.num_of_credit) / SUM(subject.num_of_credit) , 2) AS point_avg
            FROM t7_course_result
            JOIN subject ON subject.subject_code = t7_course_result.psubject_code
            JOIN user ON user.user_login = t7_course_result.student_login
            JOIN list_group ON t7_course_result.groupid = list_group.id
            WHERE term_id = ?
            AND t7_course_result.skill_code NOT IN ('SH', 'HN1', 'DH')
            $strKhoaHoc
            GROUP BY student_login
        ) custom_tbl", [$termId]);


        $dataBySubjects = DB::connection()
            ->table('t7_course_result')
            ->select([
                "user.user_login",
                "user.user_code",
                DB::raw("CONCAT(user.user_surname,' ',user.user_middlename,' ', user.user_givenname) as full_name"),
                "t7_course_result.grade",
                "list_group.group_name",
                "subject.subject_name",
                "subject.subject_code",
                "subject.num_of_credit",
            ])
            ->join('subject', 'subject.subject_code', '=', 't7_course_result.psubject_code')
            ->join('user', 'user.user_login', '=', 't7_course_result.student_login')
            ->join('list_group', 't7_course_result.groupid', '=', 'list_group.id')
            ->where('term_id', $termId)
            ->where('list_group.is_virtual', 0)
            ->whereNotIn('t7_course_result.skill_code', ['SH', 'HN1', 'DH'])
            ->when(count($khoaHoc) > 0, function ($q) use ($khoaHoc) {
                $q->whereIn('user.grade_create', $khoaHoc);
            })
            ->get();

        return Excel::download(new GradeBookTermExportMain($datas, $dataBySubjects, $term), 'bang_diem_theo_ky' . now()->format('_d_m_y_h_i_s') . '.xlsx');
    }
}
