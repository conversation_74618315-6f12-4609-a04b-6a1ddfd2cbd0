<?php


namespace App\Repositories\Admin;


use App\Exports\Graduation\BangDiemDoiSoat;
use App\Exports\Graduation\BangDiemDoiSoatAll;
use App\Exports\Graduation\DuDieuKienTotNghiep;
use App\Exports\Graduation\SoGoc;
use App\Http\Lib;
use App\Models\Crm\People;
use App\Models\Crm\Rcm;
use App\Models\Dra\CurriCulum;
use App\Models\Fu\Graduation;
use App\Models\Fu\Decision;
use App\Models\Fu\Term;
use App\Models\Fu\GraduationCampaign;
use App\Models\Fu\GraduationCampaignUser;
use App\Models\Fu\Subject;
use App\Models\GradeGdqp;
use App\Models\KhoaNhapHoc;
use App\Models\T7\AcademicDecisionManagement;
use App\Models\Transcript;
use App\Models\TranscriptDetail;
use App\Repositories\BaseRepository;
use App\Models\Fu\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use niklasravnsborg\LaravelPdf\Facades\Pdf as PDF2;
use App\Exports\Graduation\BangDiemDoiExcel;
use App\Models\Dra\StudentHistory;
use App\Models\Fu\DecisionUser;
use App\Models\Fu\SubjectsForGraduation;
use App\Models\Iaps\GraduationDecision;
use App\Models\Sna\MonthlyStatusReport;
use App\Models\Sna\TermlyStatusReport;
use App\Models\SystemLog;
use App\Models\T7\DropOut;
use App\Utils\ResponseBuilder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class GraduationRepository extends BaseRepository
{
    const ALL = 0;
    const CAMPUS = 1;
    const BB2 = '13';
    const THI_TOT_NGHIEP = 1;
    const BAO_VE_TOT_NGHIEP = 2;

    public function getModel()
    {
        return Graduation::class;
    }

    public function exportTotNghiep($request)
    {
        return Excel::download(new DuDieuKienTotNghiep([$request->campaign_id], self::CAMPUS), 'dssv_du_dk_tn' . now()->format('_d_m_y_h_i_s') . '.xlsx');
    }

    public function exportTotNghiepAll($request)
    {
        return Excel::download(new DuDieuKienTotNghiep([$request->term_name], self::ALL), 'dssv_du_dk_tn_all' . now()->format('_d_m_y_h_i_s') . '.xlsx');
    }

    public function bangDiemDoiSoat()
    {
        $nganh = DB::select("select DISTINCT c.nganh_in_bang from graduation a, user b, curriculum c where a.student_code = b.user_code and b.curriculum_id = c.id and a.chot = 0 and a.student_code != ''");

        return $this->view('graduation.bang_diem_doi_soat', [
            'nganh' => $nganh,
        ]);
    }

    public function exportBangDiemDoiSoatAll($request)
    {
        $export = new BangDiemDoiSoatAll($request->campaign_id);
        $sheets = $export->sheets();
        if (empty($sheets)) {
            return redirect()->back()->with([
                'status' => [
                    'type'     => 'warning',
                    'messages' => 'Không có dữ liệu để xuất'
                ]
            ]);
        }
        return Excel::download($export, 'bang_diem_doi_soat' . now()->format('_d_m_y_h_i_s') . '.xlsx');
    }
    public function exportBangDiemDoiSoat($request)
    {
        $nganh = $request->nganh;
        $type = $request->type;
        if ($type == 1) {
            $files = new BangDiemDoiSoatAll();
            $files->setData($nganh);

            return Excel::download($files, 'bang_diem_doi_soat' . now()->format('_d_m_y_h_i_s') . '.xlsx');
        } else if ($type == 2) {
            $array_curriculum = $this->getCurriculumByMajor($nganh);
            $result = $this->getGraduations($array_curriculum, '8', 0);
            $result = $this->xuLyInBangDiem($result);
            $pdf = new PDF2();
            $pdf = $pdf::loadView('pdf.in_bang_diem_all', ['data' => $result, 'title' => $nganh]);

            return $pdf->download('document.pdf');
            return view('pdf.in_bang_diem_all', ['data' => $result, 'title' => $nganh]);
        }
    }

    public function syncing() {}

    public function getCurriculumByMajor($nganh)
    {
        $array_curriculum = [];
        $list_curriculum = CurriCulum::select('id')->where('nganh_in_bang', $nganh)->get();
        foreach ($list_curriculum as $item) {
            $array_curriculum[] = $item->id;
        }

        return $array_curriculum;
    }

    public function getGraduations($campaign_id, $curriculum_list = [])
    {
        $breakxx = 300;
        $page = request()->get('page_print', null);
        $return = GraduationCampaignUser::where('campaign_id', $campaign_id)
            ->when($curriculum_list, function ($query, $curriculum_list) {
                $query->whereIn('curriculum_id', $curriculum_list);
            });
        if (is_numeric($page)) {
            $return = $return->limit($breakxx)
                ->offset($breakxx * ($page - 1));
        }

        $return = $return->orderBy('user_code')->get();
        return $return;
    }

    public function xuLyInBangDiem($result, $dropout, &$listGDQP = [])
    {
        try {
            DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
            DB::getPdo()->setAttribute(\PDO::ATTR_EMULATE_PREPARES, true);
            $subjects = [];
            $listCurri = [];
            $subjects2 = call_user_func(function () {
                $res = [];
                $listUbject = Subject::get();
                foreach ($listUbject as $subject) {
                    $res[$subject->id] = $subject;
                }

                return $res;
            });
            $subject_pro = SubjectsForGraduation::where('status', 1)->where('subject_type', 1)->pluck('subject_code')->toArray();
            $gdqp_subject_code = SubjectsForGraduation::where('status', 1)->where('subject_type', 2)->pluck('subject_code')->toArray();

            $listTranscript = Transcript::with(['details' => function ($q) use ($subject_pro) {
                $q->where(function ($q) use ($subject_pro) {
                    return $q->whereNotIn('subject_code_pass', $subject_pro)->orWhereNull('subject_code_pass');
                })
                ->where('num_of_credit', '!=', 0)
                ->where('type', '!=', 3)
                ->orderBy('ki_thu');
            }])
            ->whereIn('user_login', $result->pluck('user_login'))
            ->get();

            $listGDQP = TranscriptDetail::join('transcripts', 'transcript_details.transcript_id', '=', 'transcripts.id')
                ->whereIn('user_login', $result->pluck('user_login'))
                ->whereIn('transcript_details.subject_code', $gdqp_subject_code)
                ->pluck('transcript_details.skill_code', 'user_login')
                ->toArray();

            $subjects = call_user_func(function () use ($result) {
                $res = [];
                $listSkillCode = TranscriptDetail::join('transcripts', 'transcript_details.transcript_id', '=', 'transcripts.id')
                    ->whereIn('user_login', $result->pluck('user_login'))
                    ->groupBy('transcript_details.skill_code')
                    ->pluck('transcript_details.skill_code')
                    ->toArray();
                $listSubject = Subject::whereIn('skill_code', $listSkillCode)->get();
                foreach ($listSubject as $value) {
                    $res[$value->subject_code] = $value;
                }

                return $res;
            });

            foreach ($result as $item) {
                $ngay_cap = Carbon::createFromDate($dropout->date_affected);
                $qd_day = $ngay_cap->format('d');
                $qd_month = $ngay_cap->format('m');
                $qd_year = $ngay_cap->format('Y');
                if ($qd_month > 2 && $qd_month < 10) {
                    $qd_month = str_replace('0', '', $qd_month);
                }

                $item->reasonsForLoweringGraduationGrade = $item->reason_down_rank ?? "";
                $item->qd_day = $qd_day;
                $item->qd_month = $qd_month;
                $item->qd_year = $qd_year;
                $item->ngay_cap = "$qd_day/$qd_month/$qd_year";
                $item->so_quyet_dinh = $dropout->so_quyet_dinh;
                // $bang_diem = Transcript::where('user_login', $item->user_login)->first();
                $bang_diem = $listTranscript->where('user_login', $item->user_login)->first();

                if (!isset($listCurri[$bang_diem->curriculum_id])) {
                    $listCurri[$bang_diem->curriculum_id] = CurriCulum::findOrFail($bang_diem->curriculum_id);
                }

                $item->chi_tiet = $listCurri[$bang_diem->curriculum_id];
                if ($item->type == 1) {
                    $item->i = 18;
                } else {
                    $item->i = 16;
                    if (count($bang_diem->details) >= 34) {
                        $item->i = floor(count($bang_diem->details) / 2);
                    }
                }

                foreach ($bang_diem->details as $bdd => $subject) {
                    $chi_tiet_mon = null;
                    if ($subject->type == 1) {
                        if (!isset($subjects[$subject->subject_code_replace])) {
                            $subjects[$subject->subject_code_replace] = Subject::where('subject_code', $subject->subject_code_replace)->first();
                        }

                        $chi_tiet_mon = $subjects[$subject->subject_code_replace];
                    } else {
                        if (!isset($subjects2[$subject->subject_id])) {
                            $subjects2[$subject->subject_id] = Subject::find($subject->subject_id);
                        }

                        $chi_tiet_mon = $subjects2[$subject->subject_id];
                    }

                    if (in_array($subject->subject_code_pass, $subject_pro)) {
                        $subject->type = 5;
                    }

                    $gradeChar = call_user_func(function () use ($subject) {
                        $res = null;
                        // &nbsp; là dấu cách để căn nhìn cho thẳng hàng
                        if ($subject->point >= 9.0) {
                            $res = 'A+';
                        } elseif ($subject->point >=  8.5 && $subject->point < 9) {
                            $res = 'A';
                        } elseif ($subject->point >=  8.0 && $subject->point < 8.5) {
                            $res = 'A&#8722;';
                        } elseif ($subject->point >=  7.5 && $subject->point < 8.0) {
                            $res = 'B+';
                        } elseif ($subject->point >=  7.0 && $subject->point < 7.5) {
                            $res = 'B';
                        } elseif ($subject->point >=  6.5 && $subject->point < 7.0) {
                            $res = 'B&#8722;';
                        } elseif ($subject->point >=  6.0 && $subject->point < 6.5) {
                            $res = 'C+';
                        } elseif ($subject->point >=  5.5 && $subject->point < 6.0) {
                            $res = 'C';
                        } elseif ($subject->point >=  5.0 && $subject->point < 5.5) {
                            $res = 'C&#8722;';
                        } elseif ($subject->point >=  4.5 && $subject->point < 5.0) {
                            $res = 'D+';
                        } elseif ($subject->point >=  4.0 && $subject->point < 4.5) {
                            $res = 'D';
                        } elseif (4.0 < $subject->point) {
                            $res = 'F';
                        }

                        return $res;
                    });

                    $grade4Scale = number_format($this->convertScale4Point($subject->point), 2);
                    $subject->chi_tiet_mon = $chi_tiet_mon;
                    $subject->grade_char = $gradeChar;
                    $subject->grade_4_scale = $grade4Scale;
                }

                $item->diem_ly_thuyet = round($item->diem_ly_thuyet, 1);
                $item->diem_thuc_hanh = round($item->diem_thuc_hanh, 1);
                $item->chinh_tri = round($item->chinh_tri, 1);
                $item->diem_tot_nghiep = round($item->diem_tot_nghiep, 1);
                if (strlen($item->diem_ly_thuyet) == 1) {
                    $item->diem_ly_thuyet = $item->diem_ly_thuyet .= ".0";
                }

                if (strlen($item->diem_thuc_hanh) == 1) {
                    $item->diem_thuc_hanh = $item->diem_thuc_hanh .= ".0";
                }

                if (strlen($item->chinh_tri) == 1) {
                    $item->chinh_tri = $item->chinh_tri .= ".0";
                }

                if (strlen($item->diem_tot_nghiep) == 1) {
                    $item->diem_tot_nghiep = $item->diem_tot_nghiep .= ".0";
                }

                $item->bang_diem = $bang_diem;
                $item->dob = Carbon::createFromDate($item->dob);
                $dob_month = $item->dob->format('m');
                if ($dob_month > 2 && $dob_month < 10) {
                    $dob_month = str_replace('0', '', $dob_month);
                }

                $item->dob_month = $dob_month;
                $item->dob_day = $item->dob->format('d');
                $item->dob_year = $item->dob->format('Y');
                $item->dob_trans_vi = "$item->dob_day/$item->dob_month/$item->dob_year";
            }

            return $result;
        } catch (\Throwable $th) {
            Log::error($th);
            return null;
        }
    }

    public function inBangDiem($request)
    {
        return $this->view('graduation.in_bang_diem');
    }

    public function exportSoGoc($campaign_id)
    {
        return Excel::download(new SoGoc($campaign_id), 'so_goc_' . now()->format('_d_m_y_h_i_s') . '.xlsx');
    }

    public function campaign($request)
    {
        $terms = Term::orderBy('ordering', 'desc')->get();
        $campaigns = GraduationCampaign::select([
            'graduation_campaign.*',
            'graduation_decision_campaign.graduation_decision_id'
        ])
            ->leftJoin('graduation_decision_campaign', 'graduation_decision_campaign.campaign_id', '=', 'graduation_campaign.id')
            ->orderBy('id', 'desc')
            ->get();

        $collection = GraduationCampaignUser::orderBy('diem_tot_nghiep', 'desc')->orderBy('loai_hoc')->limit(10)->get();
        // Lấy loại quyết định ra
        $listGraduationLegalEntity = GraduationDecisioN::select([
            'id',
            'Legal_entity',
            'name'
        ])
            ->orderBy('id', 'desc')->get();

        return $this->view('graduation.campaign', [
            'campaigns' => $campaigns,
            'collection' => $collection,
            'top10' => $collection,
            'terms' => $terms,
            'listGraduationLegalEntity' => $listGraduationLegalEntity
        ]);
    }

    public function campaignStore($request)
    {
        try {
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', -1);
            ini_set("pcre.backtrack_limit", "100000000");

            $lock = $request->lock;
            $freeze = (int)$request->freeze;

            $locked = $request->locked;
            $is_freeze = $request->is_freeze;

            $nganh = $request->nganh;
            $dropout_id = $request->dropout_id;
            $campaign_id = $request->campaign_id;
            $so_quyet_dinh = $request->so_quyet_dinh;
            $update_dropout = $request->update_AcademicDecisionManagement;

            $export_pdf = $request->export_pdf;
            $export_pdf2 = $request->export_pdf2;
            $export_pdf3 = $request->export_pdf3;
            $export_pdf4 = $request->export_pdf4;

            $users = GraduationCampaignUser::where('campaign_id', $campaign_id)->orderBy('user_code')->get();

            $data = $this->kiemTraThongTinTotNghiep($users, $campaign_id);
            if ($is_freeze) {
                if ($export_pdf == 1) {
                    $dropout = DropOut::findOrFail($dropout_id);
                    $array_curriculum = $this->getCurriculumByMajor($nganh);
                    $listLearnGDQP = [];
                    $result = $this->getGraduations($campaign_id, $array_curriculum);
                    $result = $this->xuLyInBangDiem($result, $dropout, $listLearnGDQP);
                    $pdf = new PDF2();
                    $pagePrint = request()->get('page_print', null);

                    // return view('pdf.in_bang_diem_all', [
                    //     'data' => $result,
                    //     'title' => $nganh
                    // ]);
                    $pdf = $pdf::loadView('pdf.in_bang_diem_all', [
                        'data' => $result,
                        'title' => $nganh,
                        'listLearnGDQP' => $listLearnGDQP
                    ]);
                    // return view('pdf.in_bang_diem_all', ['data' => $result, 'title' => $nganh]);
                    return $pdf->download('in_bang_diem' . ($pagePrint == null ? "" : "_page_$pagePrint\_") . now()->format('_d_m_y_h_i_s') . '.pdf');
                }

                if (!$export_pdf2 && !$export_pdf3 && !$export_pdf4) {
                    return $this->redirectWithStatus('danger', "Dữ liệu đã được đóng băng");
                }
            }

            if ($is_freeze) {
                if ($export_pdf2 == 1 || $export_pdf3 == 1 || $export_pdf4 == 1) {
                    $graduationCampaign = GraduationCampaign::find($campaign_id);
                    $dropout = DropOut::findOrFail($dropout_id);
                    $array_curriculum = $this->getCurriculumByMajor($nganh);
                    $listLearnGDQP = [];
                    $result = $this->getGraduations($campaign_id, $array_curriculum);
                    $result = $this->xuLyInBangDiem($result, $dropout, $listLearnGDQP);
                    $pagePrint = request()->get('page_print', null);
                    $pdf = new PDF2();
                    if ($export_pdf3 == 1) {
                        return view('pdf.in_bang_diem_all_new2', [
                            'graduationCampaign' => $graduationCampaign,
                            'data' => $result,
                            'title' => $nganh,
                            'listLearnGDQP' => $listLearnGDQP,
                        ]);
                    }

                    if ($export_pdf4 == 1) {

                        $pdf = $pdf::loadView(
                            'pdf.in_bang_diem_all_thang_4',
                            [
                                'graduationCampaign' => $graduationCampaign,
                                'data' => $result,
                                'title' => $nganh,
                                'listLearnGDQP' => $listLearnGDQP,
                            ],
                            [],
                            [
                                'title' => 'Certificate',
                                'format' => 'A4',
                                'orientation' => 'L',
                                'margin_left' => '10',
                                'margin_right' => '10',
                            ]
                        );
                    } elseif ($graduationCampaign->training_regulations == 'QĐ48/QĐ-CĐFPL') {
                        // xuất ở đây
                        $pdf = $pdf::loadView(
                            'pdf.in_bang_diem_all_new_kbeauty',
                            [
                                'graduationCampaign' => $graduationCampaign,
                                'data' => $result,
                                'title' => $nganh,
                                'listLearnGDQP' => $listLearnGDQP,
                            ],
                            [],
                            [
                                'title' => 'Certificate',
                                'format' => 'A4',
                                'orientation' => 'L',
                                'margin_left' => '10',
                                'margin_right' => '10',
                            ]
                        );
                    } else {
                        // xuất ở đây
                        $pdf = $pdf::loadView(
                            'pdf.in_bang_diem_all_new2',
                            [
                                'graduationCampaign' => $graduationCampaign,
                                'data' => $result,
                                'title' => $nganh,
                                'listLearnGDQP' => $listLearnGDQP,
                            ],
                            [],
                            [
                                'title' => 'Certificate',
                                'format' => 'A4',
                                'orientation' => 'L',
                                'margin_left' => '10',
                                'margin_right' => '10',
                            ]
                        );
                    }

                    // return view('pdf.in_bang_diem_all', ['data' => $result, 'title' => $nganh]);
                    return $pdf->download('in_bang_diem' . ($pagePrint == null ? "" : "_page_$pagePrint\_") . now()->format('_d_m_y_h_i_s') . '.pdf');
                }
            }

            if ($data[1] == false) {
                return $this->redirectWithStatus('danger', "Dữ liệu không đúng, không thể chốt");
            }
            if ($lock == 1) {
                GraduationCampaign::where('id', $campaign_id)->update(['locked' => 1]);
            } else if ($lock == -1) {
                GraduationCampaign::where('id', $campaign_id)->update(['locked' => 0]);
            }

            if ($update_dropout == 1) {
                if ($locked) {
                    GraduationCampaign::where('id', $campaign_id)->update(['dropout_id' => $so_quyet_dinh]);
                    return $this->redirectWithStatus('success', "Cập nhật quyết định thành công");
                } else {
                    return $this->redirectWithStatus('danger', "Chưa chốt dữ liệu, không thể cập nhật quyết định");
                }
            }

            if ($freeze) {
                if ($so_quyet_dinh === null) {
                    return $this->redirectWithStatus('danger', "Chưa cập nhật quyết định, không thể đóng băng");
                }
                if ($locked) {
                    $this->changeStatusStudent($campaign_id);
                    GraduationCampaign::where('id', $campaign_id)->update(['freeze' => 1]);
                    // Tạo danh sách sổ điểm đóng băng
                    $result = $this->createTranscript($campaign_id);
                    if ($result) {
                        return $this->redirectWithStatus('success', "Đóng băng dữ liệu của cơ sở thành công");
                    } else {
                        return $this->redirectWithStatus('success', "Đóng băng dữ liệu không thành công. Lỗi khi xử lý bảng điểm.");
                    }
                } else {
                    return $this->redirectWithStatus('danger', "Chưa chốt dữ liệu, không thể đóng băng");
                }
            }
            // else if ($is_freeze == -1) {
            //     GraduationCampaign::where('id', $campaign_id)->update(['freeze' => 0]);
            // }

            return $this->redirectWithStatus('warning', "Lỗi");
        } catch (\Throwable $th) {
            Log::error($th);
            return $this->redirectWithStatus('warning', "Lỗi");
        }
    }

    public function campaignFindStudent($campaign_id, $request)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        $data_curriculum = [];
        $total = 0;
        $update = 0;
        $data_curriculum = [];
        $campaign = GraduationCampaign::findOrFail($campaign_id);
        if ($campaign->freeze) {
            return $this->redirectWithStatus('danger', "Thất bại, dữ liệu đã bị đóng băng");
        }

        if ($campaign->locked) {
            return $this->redirectWithStatus('danger', "Thất bại, dữ liệu đã bị khoá cập nhật.");
        }

        $listStatus = config('status')->trang_thai_hoc;
        $statusTng = null;
        foreach ($listStatus as $key => $status) {
            if ($status['uid'] == 'TNG') {
                $statusTng = $status;
                break;
            }
        }
        $users = User::query()
            ->with(['curriculum'])
            ->leftJoin('transcripts', 'transcripts.user_code', '=', 'user.user_code')
            ->whereNotNull('transcripts.id')
            ->where('user.study_status', $statusTng['id'])
            ->where('user.Legal_entity', $campaign->Legal_entity)
            ->get();
        $curriculum = CurriCulum::whereIn('id', $users->pluck('curriculum_id', 'curriculum_id'))->get();
        foreach ($curriculum as $item) {
            $data_curriculum[$item->id] = [
                'nganh_in_bang' => $item->nganh_in_bang,
                'nganh_in_bang_en' => $item->nganh_in_bang_en,
                'nganh' => $item->nganh,
                'chuyen_nganh' => $item->chuyen_nganh,
            ];
        }
        $gdqp_subject_code = SubjectsForGraduation::where('status', 1)->where('subject_type', 2)->pluck('subject_code')->toArray();

        foreach ($users as $user) {
            $dlt = 0;
            $dth = 0;
            $gdqp = 0;
            $gdqp_point = 0;
            $brand_code = $user->curriculum->brand_code ?? $user->brand_code;
            $khoa_thuc_hoc = $user->curriculum->khoa ?? $user->grade;
            $result = GraduationCampaignUser::where('user_code', $user->user_code)->where('campaign_id', $campaign->id)->first();

            $gdqp_check = GradeGdqp::where('student_code', $user->user_code)->first();
            if ($gdqp_check) {
                $gdqp = 1;
            }

            $khoa_nhap_hoc = $user->grade_create;
            $loai_thi = $this->checkLoaiThiTotNghiep($brand_code, $user->grade);
            // $khoa_nhap_hoc = $this->xuLyKhoaThucHoc($khoa_nhap_hoc);
            $loai_hoc = $this->checkLoaiHoc($khoa_nhap_hoc, $campaign);
            if ($result) {
                $dlt = $result->diem_ly_thuyet;
                $dth = $result->diem_thuc_hanh;
            }

            $old_data = Graduation::where('student_code', $user->user_code)->first();
            if ($old_data) {
                $dlt = $old_data->diem_ly_thuyet;
                $dth = $old_data->diem_thuc_hanh;
            }

            $bang_diem = $this->xuLyBangDiem($user, $loai_thi, $dlt, $dth);
            if ($gdqp == 0) {
                $gdqp = call_user_func(function () use (&$gdqp_point, $gdqp, $user, $gdqp_subject_code) {
                    $transcript = Transcript::where('user_login', $user->user_login)->first();
                    $transcriptQldt = $transcript->details()->where(function ($q) use ($gdqp_subject_code) {
                        return $q->whereIn('subject_code_pass', $gdqp_subject_code)->orWhereIn('subject_code', $gdqp_subject_code);
                    })->first();

                    if ($transcriptQldt) {
                        $gdqp_point = $transcriptQldt->point;
                        if ($transcriptQldt->status == 1) {
                            $gdqp = 1;
                        }
                    }

                    return $gdqp;
                });
            }

            $bang_diem['gdtc_tc'] = 2;
            //check có vi phạm kỷ luật hay không
            $studentHasDiscipline = Decision::join('decision_user', 'decision.id', 'decision_user.decision_id')
                ->where('decision_user.user_code', $user->user_code)->where('decision.type', 26)->get();
            $data = [
                'campaign_id' => $campaign->id,
                'user_code' => $user->user_code,
                'user_login' => $user->user_login,
                'full_name' => $user->fullname(),
                'brand_code' => $brand_code,
                'curriculum_id' => $user->curriculum_id,
                'dob' => Carbon::createFromDate($user->user_DOB)->format('Y-m-d'),
                'gender' => $user->gender == 1 ? 'Nam' : 'Nữ',
                'type' => $loai_thi,
                'dan_toc' => $user->dantoc ?? 'Kinh',
                'loai_hoc' => $loai_hoc,
                'khoa_nhap_hoc' => $khoa_nhap_hoc,
                'khoa_thuc_hoc' => $khoa_thuc_hoc,
                'nganh_in_bang' => $data_curriculum[$user->curriculum_id]['nganh_in_bang'],
                'nganh_in_bang_en' => $data_curriculum[$user->curriculum_id]['nganh_in_bang_en'],
                'nganh' => $data_curriculum[$user->curriculum_id]['nganh'],
                'chuyen_nganh' => $data_curriculum[$user->curriculum_id]['chuyen_nganh'],
                'diem_ly_thuyet' => $dlt,
                'diem_thuc_hanh' => $dth,
                'loai_tot_nghiep' => $bang_diem['loai_tot_nghiep'],
                'loai_tot_nghiep_en' => $bang_diem['loai_tot_nghiep_en'],
                'diem_tot_nghiep' => $bang_diem['diem_tot_nghiep'],
                'diem_tot_nghiep_thang_4' => $bang_diem['diem_tot_nghiep_t4'],
                'diem_trung_binh' => $bang_diem['diem_trung_binh'],
                'diem_tot_nghiep_kt' => $bang_diem['diem_trung_binh_khen_thuong'],
                'tin_chi_dat' => $bang_diem['tin_chi_dat'],
                'tin_chi_mien_giam' => $bang_diem['tin_chi_mien_giam'],
                'tong_tin_chi' => $bang_diem['tong_tin_chi'],
                'mon_dat' => $bang_diem['mon_dat'],
                'mon_chua_dat' => $bang_diem['mon_chua_dat'],
                'tong_mon' => $bang_diem['tong_mon'],
                'the_chat' => $bang_diem['the_chat'],
                'chinh_tri' => $bang_diem['chinh_tri'],
                'thuc_tap' => $bang_diem['thuc_tap'],
                'gdqp' => $gdqp,
                'gdqp_point' => $gdqp_point,
                'gdqp_tc' => $bang_diem['gdqp_tc'],
                'the_chat_tc' => $bang_diem['gdtc_tc'],
                'thuc_tap_tc' => $bang_diem['thuc_tap_tc'],
                'percented_relearn' => $bang_diem['percented_relearn'],
                'disciplined' => count($studentHasDiscipline)
            ];

            if ($result) {
                GraduationCampaignUser::where('user_code', $user->user_code)->where('campaign_id', $campaign->id)->update($data);
                $update += 1;
            } else {
                GraduationCampaignUser::create($data);
                $total += 1;
            }
        }

        $graduation_users = GraduationCampaignUser::where('campaign_id', $campaign->id)->get();
        foreach ($graduation_users as $graduation_user) {
            $graduation_user->user = User::where('user_code', $graduation_user->user_code)->first();
            if ($graduation_user->user->study_status != 7 && $graduation_user->user->study_status != 8) {
                GraduationCampaignUser::where('campaign_id', $campaign->id)->where('user_code', $graduation_user->user_code)->delete();
                $total -= 1;
            }
        }

        $campaign->total = GraduationCampaignUser::where('campaign_id', $campaign->id)->count();
        $campaign->save();

        return $this->redirectWithStatus('success', "Tạo mới $total, cập nhật $update");
    }

    public function campaignCreate($request)
    {
        $term = Lib::getTermDetails();
        $campaign = GraduationCampaign::where('term_id', $term['term_id'])->first();
        if (!$campaign) {
            GraduationCampaign::create([
                'term_name' => $term['term_name'],
                'term_id' => $term['term_id'],
            ]);

            return $this->redirectWithStatus('success', "Tạo đợt tốt nghiệp " . $term['term_name'] . " thành công");
        }

        return $this->redirectWithStatus('danger', "Đợt tốt nghiệp kỳ này đã được tạo");
    }

    public function checkLoaiThiTotNghiep($brand_code, $grade)
    {
        if ($brand_code == 'QTKS' || $brand_code == 'HDDL' || $brand_code == 'QTNH') {
            if ($grade >= 14.1) {
                $loai_thi = self::BAO_VE_TOT_NGHIEP;
            } else {
                $loai_thi = self::THI_TOT_NGHIEP;
            }
        } else if ($brand_code == 'DIG' || $brand_code == 'PR') {
            $loai_thi = self::BAO_VE_TOT_NGHIEP;
        } else if ($grade >= 13.3) {
            $loai_thi = self::BAO_VE_TOT_NGHIEP;
        } else {
            $loai_thi = self::THI_TOT_NGHIEP;
        }

        return $loai_thi;
    }

    // public function xuLyKhoaThucHoc($text)
    // {
    //     $text = mb_strtoupper($text);
    //     $text = explode(' ', $text);
    //     $text = $text[1];
    //     $text = str_replace(['V1', 'V2', 'V3', 'K'], '', $text);

    //     return $text;
    // }

    public function checkLoaiHoc($khoa_nhap_hoc, $campaign)
    {
        $khoa_nhap_hoc = trim($khoa_nhap_hoc);
        $knh = KhoaNhapHoc::all();
        $kht = $knh->keys()->last();
        $minus_7 = $knh[$kht - 7];
        $khoa_nhap_hoc_str = explode('.', $khoa_nhap_hoc);
        if (count($khoa_nhap_hoc_str) > 2) {
            $khoa_nhap_hoc = $khoa_nhap_hoc_str[0] . "." . $khoa_nhap_hoc_str[1];
        }

        if ($campaign->training_regulations == 'QĐ48/QĐ-CĐFPL') {
            $minus_2 = $knh[$kht - 2];
            if ($khoa_nhap_hoc > $minus_2->khoa) {
                $tn_dung_han = 'Vượt hạn';
            } else if ($khoa_nhap_hoc == $minus_2->khoa) {
                $tn_dung_han = 'Đúng hạn';
            } else {
                $tn_dung_han = 'Trễ hạn';
            }

            return $tn_dung_han;
        }

        if ($khoa_nhap_hoc > $minus_7->khoa) {
            $tn_dung_han = 'Vượt hạn';
        } else if ($khoa_nhap_hoc == $minus_7->khoa) {
            $tn_dung_han = 'Đúng hạn';
        } else {
            $tn_dung_han = 'Trễ hạn';
        }

        return $tn_dung_han;
    }

    public function xuLyBangDiem($user, $loai_thi, $diem_ly_thuyet, $diem_thuc_hanh)
    {
        $user_login = $user->user_login;
        $tot_nghiep = ['Xuất sắc','Giỏi','Khá','Trung bình khá','Trung bình','Trượt'];
        $tot_nghiep_en = ['High distinction','Distinction','Merit','Strong pass','Pass','Fail'];
        $tong_tc = 0;
        $tong_mon = 0;
        $tong_mon_dat = 0;
        $tong_tc_dat = 0;
        $dtb_point = 0;
        $dtb_point_t4 = 0;
        $dtb_tc = 0;
        $gdtc = 0;
        $dtb = 0;
        $dtb_khen_thuong = 0;
        $dtb_t4 = 0;
        $tong_tc_mg = 0;
        $thuc_tap = 0;
        $chinh_tri = 0;
        $temp = [];
        $gdtc_tc = 0;
        $gdqp_tc = 0;
        $thuc_tap_tc = 0;
        $relearn_tc = 0;
        $percented_relearn = 0;
        $bang_diem = Transcript::where('user_login', $user_login)->first();
        $bang_diem->details = TranscriptDetail::where('transcript_id', $bang_diem->id)->get();
        $subject_for_graduation = SubjectsForGraduation::all();
        // Môn học thực tập tốt nghiệp
        $tttn_subject_code = $subject_for_graduation->where('status', 1)->where('subject_type', 1)->pluck('subject_code')->toArray();
        // Môn học tín chỉ
        $tc_subject_code = $subject_for_graduation->where('status', 1)->where('subject_type', 3)->pluck('subject_code')->toArray();
        // Môn học chính trị
        $ct_subject_code = $subject_for_graduation->where('status', 1)->where('subject_type', 2)->pluck('subject_code')->toArray();

        $subjectDontCalNumCredit = Subject::where('num_of_credit', 0)->pluck('subject_code')->toArray();
        foreach ($bang_diem->details as $mon) {
            $mon_co_diem[$mon->subject_code] = 1;
            $temp[$mon->subject_code] = [
                'point' => $mon->point,
                'type' => $mon->type,
            ];
            if($mon->luot_hoc > 1) {
                $relearn_tc += $mon->num_of_credit;
            }

            $tong_tc += $mon->num_of_credit;
            $tong_mon += 1;
            if ($mon->status == 1) {
                $tong_mon_dat += 1;
                $tong_tc_dat += $mon->num_of_credit;
            }
            // Tính toán điểm tích lũy - chỉ tính các môn đã đạt (status = 1)
            if ($mon->status == 1 && $mon->type != 2 && $mon->type != 3) {
                if (!in_array($mon->subject_code, $tttn_subject_code) && !in_array($mon->subject_code, $subjectDontCalNumCredit)) {
                    $dtb_point += ($mon->point * $mon->num_of_credit);
                    $dtb_point_t4 += ($this->convertScale4Point($mon->point) * $mon->num_of_credit);
                    $dtb_tc += $mon->num_of_credit;
                } else {
                    if ($mon->status == 1) {
                        $trang_thai = 1;
                    } else {
                        $trang_thai = 0;
                    }

                    if (!in_array($mon->subject_code, $subjectDontCalNumCredit)) {
                        $thuc_tap = $trang_thai;
                        $thuc_tap_tc = $mon->num_of_credit;
                    }
                }
            }

            if ($mon->type == 2 || $mon->type == 3) {
                $tong_tc_mg += $mon->num_of_credit;
            }

            // Check môn thực tập
            if (in_array($mon->subject_code, $tttn_subject_code)) {
                if ($mon->status == 1) {
                    $trang_thai = 1;
                } else {
                    $trang_thai = 0;
                }
            }

            // Check môn giáo dục thể chất
            if (in_array($mon->subject_code, $tc_subject_code)) {
                $gdtc = $mon->point;
                $gdtc_tc = $mon->num_of_credit;
            }

            if (in_array($mon->subject_code, $ct_subject_code)) {
                $chinh_tri = $mon->point;
                if ($mon->type == 2) {
                    $chinh_tri = -1;
                }
            }
        }

        if ($dtb_tc != 0 && $dtb_tc != 0) {
            $dtb = round($dtb_point / $dtb_tc, 1);
            $dtb_khen_thuong = round($dtb_point / $dtb_tc, 2);
            $dtb_t4 = round($dtb_point_t4 / $dtb_tc, 2);
        } else {
            $dtb = 0;
        }
        // if ($loai_thi == 1) {
        //     $dtn = (($dtb * 3) + $diem_ly_thuyet + ($diem_thuc_hanh * 2)) / 6;
        // } else if ($loai_thi == 2) {
            $dtn = $dtb;
        // }
        $percented_relearn = 0;
        if ($tong_tc != 0) {
            $percented_relearn = round(($relearn_tc/$tong_tc)*100,1);
        }
        $dtn_orignal = round($dtn,  1);
        $dtn_orignal = number_format($dtn_orignal, 1);
        $dtn = $dtn_orignal;
        if ($dtn >= 9) {
            $loai_tn = 0;
        } else if ($dtn >= 8) {
            $loai_tn = 1;
        } else if ($dtn >= 7) {
            $loai_tn = 2;
        } else if ($dtn >= 6) {
            $loai_tn = 3;
        } else if ($dtn >= 5) {
            $loai_tn = 4;
        } else {
            $loai_tn = 5;
        }

        $ltn = $tot_nghiep[$loai_tn];
        $ltn_en = $tot_nghiep_en[$loai_tn];

        return [
            'loai_tot_nghiep' => $ltn,
            'loai_tot_nghiep_en' => $ltn_en,
            'diem_tot_nghiep' => $dtn,
            'diem_tot_nghiep_t4' =>  $dtb_t4,
            'diem_trung_binh' => $dtb,
            'diem_trung_binh_khen_thuong' => $dtb_khen_thuong,
            'tin_chi_dat' => $tong_tc_dat,
            'tin_chi_mien_giam' => $tong_tc_mg,
            'tong_tin_chi' => $tong_tc,
            'mon_dat' => $tong_mon_dat,
            'mon_chua_dat' => $tong_mon - $tong_mon_dat,
            'tong_mon' => $tong_mon,
            'the_chat' => $gdtc,
            'chinh_tri' => $chinh_tri,
            'thuc_tap' => $thuc_tap,
            'gdtc_tc' => $gdtc_tc,
            'gdqp_tc' => $gdqp_tc,
            'thuc_tap_tc' => $thuc_tap_tc,
            'percented_relearn' => $percented_relearn
        ];
    }

    //Chuyển đổi điểm hệ 10 sang hệ 4
    private function convertScale4Point($dtn)
    {
        if ($dtn >= 9.0) {
            return 4.0;
        } elseif ($dtn >=  8.5 && $dtn < 9) {
            return 3.75;
        } elseif ($dtn >=  8.0 && $dtn < 8.5) {
            return 3.5;
        } elseif ($dtn >=  7.5 && $dtn < 8.0) {
            return 3.25;
        } elseif ($dtn >=  7.0 && $dtn < 7.5) {
            return 3.0;
        } elseif ($dtn >=  6.5 && $dtn < 7.0) {
            return 2.75;
        } elseif ($dtn >=  6.0 && $dtn < 6.5) {
            return 2.5;
        } elseif ($dtn >=  5.5 && $dtn < 6.0) {
            return 2;
        } elseif ($dtn >=  5.0 && $dtn < 5.5) {
            return 1.75;
        } elseif ($dtn >=  4.5 && $dtn < 5.0) {
            return 1.25;
        } elseif ($dtn >=  4.0 && $dtn < 4.5) {
            return 1;
        } elseif (4.0 < $dtn) {
            return 0;
        }
    }

    public function campaignDetail($campaign_id, $request)
    {
        $campaign = GraduationCampaign::findOrFail($campaign_id);
        $users = GraduationCampaignUser::where('campaign_id', $campaign_id)->orderBy('diem_tot_nghiep', 'desc')->get();
        $listCheckRelearn = Transcript::select([
            'transcripts.user_login',
            DB::raw('GROUP_CONCAT(transcript_details.subject_code SEPARATOR ", ") AS relearn_subjects')
        ])
            ->join('transcript_details', 'transcript_details.transcript_id', '=', 'transcripts.id')
            ->where('luot_hoc', '>', 1)
            ->groupBy('transcripts.id')
            ->whereIn('user_login', $users->pluck('user_login'))
            ->pluck('relearn_subjects', 'user_login')
            ->toArray();

        foreach ($users as $key => $value) {
            $value->relearn_subjects = '';
            $value->decisiones = '';
            if ($value->percented_relearn > 10) {
                if (isset($listCheckRelearn[$value->user_login])) {
                    $value->relearn_subjects = $listCheckRelearn[$value->user_login];
                }
            }
            if ($value->disciplined > 0) {
                $decision = Decision::join('decision_user', 'decision_user.decision_id', 'decision.id')->where('decision.type', 26)
                    ->where('decision_user.user_code', $value->user_code)->pluck('name')->toArray();
                $value->decisiones = implode(", ", $decision);
            }
        }
        $data = $this->kiemTraThongTinTotNghiep($users);
        $so_quyet_dinh = AcademicDecisionManagement::where('drop_type', 'TNG')->orderBy('id', 'desc')->get();
        $nganh = GraduationCampaignUser::select('nganh_in_bang', 'brand_code')->where('campaign_id', $campaign_id)->distinct()->get();
        return $this->view('graduation.campaign_detail', [
            'users' => $users,
            'data' => $data[0],
            'decision_types' => Decision::TYPES,
            'terms' => Term::orderBy('id', 'DESC')->get() ?? [],
            'campaign' => $campaign,
            'so_quyet_dinh' => $so_quyet_dinh,
            'nganh' => $nganh,
        ]);
    }

    public function kiemTraThongTinTotNghiep($users, $campaign_id = null)
    {
        $dataCamp = null;
        if ($campaign_id != null) {
            $dataCamp = GraduationCampaign::find($campaign_id);
        }

        $data = [
            'tot_nghiep_pass' => 0,
            'tot_nghiep_not_pass' => 0,
            'chinh_tri_pass' => 0,
            'chinh_tri_not_pass' => 0,
            'gdqp_pass' => 0,
            'gdqp_not_pass' => 0,
            'thuc_tap_pass' => 0,
            'thuc_tap_not_pass' => 0,
            'the_chat_pass' => 0,
            'the_chat_not_pass' => 0,
        ];
        $check = false;
        foreach ($users as $user) {

            if ($user->loai_tot_nghiep == 'Chưa đạt' || $user->mon_chua_dat > 0) {
                $data['tot_nghiep_not_pass'] += 1;
            } else {
                $data['tot_nghiep_pass'] += 1;
            }

            // Ktra Chính trị
            if ($user->chinh_tri >= 5 || $user->chinh_tri == -1) {
                $data['chinh_tri_pass'] += 1;
            } else {
                $data['chinh_tri_not_pass'] += 1;
            }

            // Kiểm tra GDQP
            if ($user->gdqp) {
                $data['gdqp_pass'] += 1;
            } else {
                $data['gdqp_not_pass'] += 1;
            }

            // Ktra thực tập
            if ($user->thuc_tap) {
                $data['thuc_tap_pass'] += 1;
            } else {
                $data['thuc_tap_not_pass'] += 1;
            }

            // kiểm tra thể chất
            if ($user->the_chat >= 5 || $user->the_chat == -1) {
                $data['the_chat_pass'] += 1;
            } else {
                $data['the_chat_not_pass'] += 1;
            }
        }


        if ($dataCamp && $dataCamp->training_regulations == 'QĐ48/QĐ-CĐFPL') {
            $check = true;
        } else {
            if ($data['tot_nghiep_not_pass'] == 0 && $data['chinh_tri_not_pass'] == 0 && $data['gdqp_not_pass'] == 0 && $data['thuc_tap_not_pass'] == 0 && $data['the_chat_not_pass'] == 0) {
                $check = true;
            }
        }

        return [$data, $check];
    }

    /**
     *
     * Up quyết định
     *
     * <AUTHOR>
     * @since 04/10/2022
     * @version 1.0
     */
    public function addDecision($request)
    {
        $rule  =  [
            'file' => 'required',
            'so_quyet_dinh' => 'required',
            'nguoi_ky' => 'required',
            'drop_out_date' => 'required',
            'quyet_dinh_tu' => 'required',
        ];

        $messages = [
            'file.required' => 'File quyết định không được để trống',
            'nguoi_ky.required' => 'Người ký không được để trống',
            'drop_out_date.required' => 'Ngày ký không được để trống',
            'quyet_dinh_tu.required' => 'Quyết định từ không được để trống',
            'so_quyet_dinh.required' => 'Số quyết định không được để trống',
            'so_quyet_dinh.regex' => 'Số quyết định chỉ được nhập số',
        ];

        $validator = Validator::make($request->all(), $rule, $messages);
        if ($validator->fails()) {
            return redirect(url()->previous())
                ->withErrors($validator)
                ->withInput();
        }

        $graduationCampaign = GraduationCampaign::find($request->campaign_id);
        if (!$graduationCampaign) {
            return redirect(url()->previous())
                ->withErrors(["Không tìm thấy quyết định"])
                ->withInput();
        }

        // Lấy thông tin của đợt tốt nghiệp hiện tại
        $term = Term::find($graduationCampaign->term_id);
        DB::beginTransaction();
        DB::beginTransaction();
        try {
            $term = Term::where('term_name', $term->term_name)->first();

            // Kiểm tra file để up quyết định
            if ($request->file('file') != null) {
                $listDataAdd = $request->only([
                    'so_quyet_dinh',
                    'nguoi_ky',
                    'drop_out_date',
                    'quyet_dinh_tu'
                ]);

                // Kiểm tra lại dữ liệu của quyết định
                foreach ($listDataAdd as $field => $dataField) {
                    if (trim($dataField) == "" || $dataField == null) {
                        DB::rollBack();
                        return redirect(url()->previous())->withErrors([$field . ' Không được bỏ trống'])->withInput();
                    }
                }

                $fileDecision = $this->uploadFileDecisionPdf($request->file('file'), $request->so_quyet_dinh, $term->term_name);
                $dataDecision = Decision::where('term_id', $term->term_id)
                    ->where('type', $request->decision_type)
                    ->where('term_id', $term->id)
                    ->where('decision_num', $request->so_quyet_dinh)
                    ->first();

                if (!$dataDecision) {
                    $dataDecision = Decision::create([
                        'name' => $request->file('file')->getClientOriginalName(),
                        'decision_num' => $request->so_quyet_dinh,
                        'term_id' => $term->id,
                        'from' => $request->quyet_dinh_tu,
                        'type' => 8,
                        'signer' => $request->nguoi_ky,
                        'sign_day' => $request->drop_out_date,
                        'effective_time' => $request->date_affected,
                        'file' => $fileDecision,
                        'file_status' => 1,
                        'note' => ($request->note ?? ""),
                        'created_by' => (auth()->user()->user_login ?? null),
                    ]);
                }

                $array = $request->all();
                $file = $request->file('file') != null ? $this->uploadFilePdf($request->file('file'), $array['so_quyet_dinh']) : "";
                $array['so_quyet_dinh'] = $array['so_quyet_dinh'] . '/' . $array['quyet_dinh_tu'];
                $array['file_name'] = $file;
                $array['drop_type'] = 'TNG';
                $array['drop_user'] = auth()->user()->user_login;
                $array['date_created'] = now();
                $array['note'] = $array['note'] ?? '';
                $array['drop_out_date'] = $array['drop_out_date'] ?? now();
                $dropout = AcademicDecisionManagement::create($array);
                $graduationCampaign->dropout_id = $dropout->id;
                $graduationCampaign->decision_id = $dataDecision->id;
            }

            $graduationCampaign->save();
            DB::commit();
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            DB::rollBack();
            Log::debug($exception);
            return redirect(url()->previous())
                ->withErrors([$exception->getMessage()])
                ->withInput();
        }

        return $this->redirectWithStatus('success', "Cập nhật quyết định thành công");
    }

    /**
     *
     * Export Bảng điểm excel
     *
     * <AUTHOR>
     * @since 04/10/2022
     * @version 1.0
     */
    public function exportBangDiemExcel($request)
    {
        ini_set('max_execution_time', 1800);
        ini_set("memory_limit", "-1");
        $graduationCampaign = GraduationCampaign::find($request->campaign_id);
        if (!$graduationCampaign) {
            return back();
        }
        // tải xuống
        return Excel::download(new BangDiemDoiExcel($graduationCampaign->id), "bang_diem_doi_soat_chi_tiet_" . now()->format('_d_m_y_h_i_s') .".xlsx");
    }

    public function createCampaign($request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required',
            'so_quyet_dinh' => 'required|alpha_num',
            'nguoi_ky' => 'required',
            'drop_out_date' => 'required',
            'quyet_dinh_tu' => 'required',
            // 'users' => 'required',
            'trainingRegulations' => 'required'
        ], [
            'file.required' => 'Bạn chưa tải file quyết định lên',
            'so_quyet_dinh.required' => 'Số quyết định không được để trống',
            'nguoi_ky.required' => 'Người ký không được để trống',
            'drop_out_date.required' => 'Ngày ký không được để trống',
            'quyet_dinh_tu.required' => 'Quyết định từ không được để trống',
            'so_quyet_dinh.regex' => 'Số quyết định chỉ được nhập số',
            'trainingRegulations.required' => 'Quy chế đào tạo không được để trống',
            // 'users.required' => 'File danh sách sinh viên không được để trống',
        ]);

        if ($validator->fails()) {
            return redirect(url()->previous())->withErrors($validator)->withInput();
        }

        // init data
        $termId = $request->term_id;
        $term = Term::find($request->term_id);

        DB::beginTransaction();
        try {
            $term = Term::where('term_name', $term->term_name)->first();
            $getOrdering1 = GraduationCampaign::where('term_id', $term->id)->where('Legal_entity', $request->Legal_entity)->count();
            $getOrdering = GraduationCampaign::where('term_id', $term->id)->where('Legal_entity', $request->Legal_entity)->max('ordering');

            $getOrdering1 += 1;
            if ($getOrdering != null) {
                $getOrdering += 1;
            } else {
                $getOrdering = 0;
            }

            $getOrdering = max($getOrdering, $getOrdering1);
            $graduationCampaign = GraduationCampaign::create([
                'term_name' => $term->term_name,
                'term_id' => $term->id,
                'ordering' => $getOrdering,
                'Legal_entity' => $request->Legal_entity,
                'training_regulations' => $request->trainingRegulations
            ]);

            // Kiểm tra file để up quyết định
            if ($request->file('file') != null) {
                $listDataAdd = $request->only([
                    'so_quyet_dinh',
                    'nguoi_ky',
                    'drop_out_date',
                    'quyet_dinh_tu'
                ]);
                // Kiểm tra lại dữ liệu của quyết định
                foreach ($listDataAdd as $field => $dataField) {
                    if (trim($dataField) == "" || $dataField == null) {
                        DB::rollBack();
                        return redirect(url()->previous())->withErrors([$field . ' không được bỏ trống'])->withInput();
                    }
                }
                $fileDecision = $this->uploadFileDecisionPdf($request->file('file'), $request->so_quyet_dinh, $term->term_name);
                $dataDecision = Decision::where('term_id', $term->term_id)
                    ->where('type', $request->decision_type)
                    ->where('term_id', $term->id)
                    ->where('decision_num', $request->so_quyet_dinh)
                    ->first();

                if (!$dataDecision) {
                    $dataDecision = Decision::create([
                        'name' => $request->file('file')->getClientOriginalName(),
                        'decision_num' => $request->so_quyet_dinh,
                        'term_id' => $term->id,
                        'from' => $request->quyet_dinh_tu,
                        'type' => 8,
                        'signer' => $request->nguoi_ky,
                        'sign_day' => $request->drop_out_date,
                        'effective_time' => $request->date_affected,
                        'file' => $fileDecision,
                        'file_status' => 1,
                        'note' => ($request->note ?? ""),
                        'created_by' => (auth()->user()->user_login ?? null),
                    ]);
                }

                $array = $request->all();
                $file = $request->file('file') != null ? $this->uploadFilePdf($request->file('file'), $array['so_quyet_dinh']) : "";
                $array['so_quyet_dinh'] = $array['so_quyet_dinh'] . '/' . $array['quyet_dinh_tu'];
                $array['file_name'] = $file;
                $array['drop_type'] = 'TNG';
                $array['drop_user'] = auth()->user()->user_login;
                $array['date_created'] = now();
                $array['note'] = $array['note'] ?? '';
                $array['drop_out_date'] = $array['drop_out_date'] ?? now();
                // $dropout = AcademicDecisionManagement::create($array);
                $dropout = DropOut::create($array);
                $graduationCampaign->dropout_id = $dropout->id;
                $graduationCampaign->decision_id = $dataDecision->id;
            }

            $graduationCampaign->save();
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::debug($exception);
            return redirect(url()->previous())
                ->withErrors($exception->getMessage())
                ->withInput();
        }

        return back()->withSuccess('Thêm mới quyết định thành công');
    }

    public function uploadFilePdf($file, $no)
    {
        $fileName = $no . '-' . now()->format('d_m_y_h_i_s') . '.' . $file->getClientOriginalExtension();
        $this->uploadFile('drop_out', $file, $fileName);

        return $fileName;
    }

    public function uploadFileDecisionPdf($file, $no, $termName)
    {
        $fileName = $termName . '-' . $no . '-' . now()->format('d_m_y_h_i_s') . '.' . $file->getClientOriginalExtension();
        $this->uploadFile(('decision'), $file, $fileName);

        return $fileName;
    }

    public function changeStatusStudent($campaignId)
    {
        // Lấy campaign hiện tại
        $currentCampaign = GraduationCampaign::find($campaignId);
        if (!$currentCampaign) {
            return;
        }

        // lấy danh sách sinh viên
        $listUser = GraduationCampaignUser::where('campaign_id', $campaignId)->get();
        $listStatus = config('status')->trang_thai_hoc;

        $statusTng = null;
        foreach ($listStatus as $key => $status) {
            if ($status['uid'] == 'TNG') {
                $statusTng = $status;
                break;
            }
        }

        // Lấy quyết định Dropout
        $dropout = DropOut::findOrFail($currentCampaign->dropout_id);
        $date_affected = Carbon::createFromDate($dropout->date_affected);

        DB::beginTransaction();
        try {
            foreach ($listUser as $key => $student) {
                $studentObject = User::where('user_login', $student->user_login)->first();

                $studentObject->study_status = $statusTng['id'];
                $studentObject->study_status_code = $statusTng['uid'];
                $studentObject->save();

                /* ============ Thêm vào quản lý quyết định ============ */
                DecisionUser::create([
                    'decision_id' => $currentCampaign->decision_id,
                    'user_code' => $studentObject->user_code,
                ]);

                /* ============ Thêm log Thay đổi trạng thái ============ */
                // Thêm log lịch sử sinh viên
                MonthlyStatusReport::create([
                    'student_login' => $studentObject->user_login,
                    'student_code' => $studentObject->user_code,
                    'month' => $date_affected->month,
                    'year' => $date_affected->year,
                    'status_id' => $statusTng['id'],
                    'curriculum_id' => $studentObject->curriculum_id,
                    'date_created' => now(),
                    'locked' => 0,
                    'is_new' => 0,
                    'second_status_id' => 0,
                    'startday' => now(),
                    'endday' => now(),
                ]);

                // Thêm log lịch sử sinh viên
                TermlyStatusReport::create([
                    'student_login' => $studentObject->user_login,
                    'student_code' => $studentObject->user_code,
                    'term_id' => $dropout->term_id,
                    'status_id' => $statusTng['id'],
                    'curriculum_id' => $studentObject->curriculum_id,
                    'date_created' => now(),
                    'locked' => 0,
                    'is_new' => 0,
                    'second_status_id' => 0,
                    'term_no' => 0,
                ]);

                // Thêm log lịch sử sinh viên
                StudentHistory::create([
                    'student_login' => $studentObject->user_login,
                    'action_name' => $statusTng['uid'],
                    'date_taken' => now(),
                    'actor' => auth()->user()->user_login,
                    'description' => $statusTng['uid'],
                    'old_status' => 0,
                    'current_status' => $statusTng['id'],
                    'student_code' => '',
                    'term_id' => $dropout->term_id,
                    'cusfield1' => '',
                ]);

                // Thêm log
                SystemLog::create([
                    'actor' => auth()->user()->user_login,
                    'object_name' => 'Đã tốt nghiệp',
                    'log_time' => now(),
                    'action' => 'Đã tốt nghiệp',
                    'description' => 'Đã tốt nghiệp cho sinh viên ' . $studentObject->user_login,
                    'relation_login' => $studentObject->user_login,
                    'object_id' => 0,
                    'relation_id' => 0,
                    'brief' => '',
                    'from_ip' => request()->ip(),
                    'nganh_cu' => '',
                    'nganh_moi' => '',
                    'ky_chuyen_den' => 0,
                    'ky_thu_cu' => 0,
                ]);
            }

            DB::commit();
        } catch (\Exception $ex) {
            Log::error($ex);
            DB::commit();
        }
        // Thêm vào quản lý quết định + dropuot
    }

    public function createTranscipt($request)
    {
        // ResponseBuilder
        ini_set('max_execution_time', -1);
        $campaignId = $request->get('campaign_id', null);
        $campaign = GraduationCampaign::findOrFail($campaignId);
        if (!$campaign) {
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng báo lại cán bộ IT');
        }

        $check = $this->createTranscript($campaignId);
        if ($check) {
            return ResponseBuilder::Success([], 'Tạo bảng điểm thành công');
        } else {
            return ResponseBuilder::Fail('Có lỗi xảy ra, vui lòng báo lại cán bộ IT');
        }
    }

    public function createTranscript($campaign_id)
    {
        $campaign = GraduationCampaign::findOrFail($campaign_id);
        // Lấy danh sách sinh viên
        $listUser = GraduationCampaignUser::where('campaign_id', $campaign->id)->get();
        $userError = null;
        DB::beginTransaction();
        try {
            // Lấy danh sách
            $listNewTranscipt = [];
            foreach ($listUser as $key => $user) {
                // Lấy transcipt
                $userError = $user->user_login;
                $transcipt = Transcript::where('user_login', $user->user_login)->first();
                $transcipt = Transcript::where('user_login', $user->user_login)->first();
                // nếu đã có điểm thì bỏ qua
                if ($transcipt) {
                    continue;
                }

                // tạo bảng điểm
                $transcipt = new Transcript();
                $transcipt->campaign_id = $campaign->id;
                $transcipt->user_login = $transcipt->user_login;
                $transcipt->user_code = $transcipt->user_code;
                $transcipt->curriculum_id = $transcipt->curriculum_id;
                $transcipt->version = $transcipt->version;
                $transcipt->study_status = $transcipt->study_status;
                $transcipt->save();

                // lấy dữ liệu từng môn
                $listSubject = TranscriptDetail::where('transcript_id', $transcipt->id)->get();
                foreach ($listSubject as $subject) {
                    $listNewTranscipt[] = [
                        'transcript_id' => $transcipt->id,
                        'point' => $subject->point,
                        'subject_code' => $subject->subject_code,
                        'subject_name' => $subject->subject_name,
                        'subject_id' => $subject->subject_id,
                        'skill_code' => $subject->skill_code,
                        'type' => $subject->type,
                        'status' => $subject->status,
                        'num_of_credit' => $subject->num_of_credit,
                        'period_subject_id' => $subject->period_subject_id,
                        'subject_code_replace' => $subject->subject_code_replace,
                        'subject_name_replace' => $subject->subject_name_replace,
                        'skill_code_replace' => $subject->skill_code_replace,
                        'term_name' => $subject->term_name,
                        'luot_hoc' => $subject->luot_hoc,
                        'ki_thu' => $subject->ki_thu,
                        'created_at' => $subject->created_at,
                        'updated_at' => now(),
                        'subject_code_pass' => $subject->subject_code_pass,
                        'subject_name_pass' => $subject->subject_name_pass,
                    ];
                }

                if (count($listNewTranscipt) > 100) {
                    TranscriptDetail::insert($listNewTranscipt);
                    $listNewTranscipt = [];
                }
            }

            if (count($listNewTranscipt) > 100) {
                TranscriptDetail::insert($listNewTranscipt);
            }

            DB::commit();
            return true;
        } catch (\Exception $th) {
            DB::rollback();
            Log::error('Lỗi tạo bảng điểm: ' . $userError);
            Log::error($th);
            return false;
        }
    }

    //Hạ loại tốt nghiệp
    public function downRankGraduateStudent($request)
    {
        $listTypeGraduation = GraduationCampaignUser::getlistTypeGraduation();
        try {
            $user = (object) $request->user;
            // lấy loại tốt nghiệp hiện tại của sinh viên
            $userTypeGraduate = $user->loai_tot_nghiep;
            // Tiến hành hạ loại
            $tot_nghiep_old = $listTypeGraduation['old']['vi'];
            $tot_nghiep_en_old = $listTypeGraduation['old']['en'];
            $tot_nghiep_new = $listTypeGraduation['new']['vi'];
            $tot_nghiep_en_new = $listTypeGraduation['new']['en'];
            $key = 0;
            $graduateCampaign = GraduationCampaign::find($user->campaign_id);
            if ($graduateCampaign->Legal_entity == 1) {
                if ($graduateCampaign->training_regulations === 'QĐ179A/QĐ176') {
                    $key = array_search($userTypeGraduate, $tot_nghiep_old);
                    GraduationCampaignUser::where('user_code', $user->user_code)->update([
                        'loai_tot_nghiep' => $tot_nghiep_old[$key + 1],
                        'loai_tot_nghiep_en' => $tot_nghiep_en_old[$key + 1],
                        'mark_down_rank' => 1
                    ]);
                } else if ($graduateCampaign->training_regulations === 'QĐ415') {
                    $key = array_search($userTypeGraduate, $tot_nghiep_new);
                    GraduationCampaignUser::where('user_code', $user->user_code)->update([
                        'loai_tot_nghiep' => $tot_nghiep_new[$key + 1],
                        'loai_tot_nghiep_en' => $tot_nghiep_en_new[$key + 1],
                        'mark_down_rank' => 1
                    ]);
                }
            } else {
                $key = array_search($userTypeGraduate, $tot_nghiep_old);
                GraduationCampaignUser::where('user_code', $user->user_code)->update([
                    'loai_tot_nghiep' => $tot_nghiep_old[$key + 1],
                    'loai_tot_nghiep_en' => $tot_nghiep_en_old[$key + 1],
                    'mark_down_rank' => 1
                ]);
            }

            return true;
        } catch (\Exception $th) {
            Log::error('Lỗi hạ loại tốt nghiệp: ' . $user);
            Log::error($th);
            return false;
        }
    }
}
