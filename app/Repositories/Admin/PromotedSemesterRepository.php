<?php

namespace App\Repositories\Admin;

use App\Exports\PromotedSemesterStudentExport;
use App\Http\Lib;
use App\Imports\PromotedSemesterImport;
use App\Models\Dra\CurriCulum;
use App\Models\Dra\StudentHistory;
use App\Models\Fu\Term;
use Carbon\Carbon;
use App\Models\Fu\User;
use App\Models\HistoryLenky;
use Illuminate\Support\Facades\Storage;
use App\Repositories\BaseRepository;
use App\Utils\ResponseBuilder;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\SystemLog;

class PromotedSemesterRepository extends BaseRepository
{

    public function getModel()
    {
        return HistoryLenky::class;
    }

    /**
     * get dữ liệu options cho filter
     *
     * @return void
     */
    public function getOptions()
    {
        try {
            $grade_creates = CurriCulum::groupBy('khoa')->orderBy('id', 'DESC')->pluck('khoa');
            $brands = CurriCulum::orderBy('version', 'desc')->get([
                'chuyen_nganh as brand_detail',
                'brand_code'
            ]);
            $terms = Term::orderBy('endday', 'desc')->get([
                'id',
                'term_name'
            ]);
            return ResponseBuilder::Success([
                'grade_creates' => $grade_creates,
                'brands' => $brands,
                'terms' => $terms
            ]);
        } catch (\Throwable $th) {
            Log::error("PromotedSemesterRepository - getOptions: " . $th->getFile() . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage());
        }
    }

    /**
     * get dữ liệu cho bảng danh sách sinh viên lên kỳ
     *
     * @param  mixed $request
     * @return mixed $response
     */
    public function getListPromotedSemesterStudent(Request $request)
    {
        try {
            $list_query = HistoryLenky::join('user', 'user.user_code', '=', 'history_lenky.user_code')
                ->join('curriculum', 'user.curriculum_id', '=', 'curriculum.id');
            $list_query->where('user.user_level', 3);
            if (isset($request->grade_create) && ($request->grade_create > 0)) {
                $list_query->where('user.grade_create', 'LIKE', $request->grade_create . '%');
            }
            if (isset($request->brand_code) && ($request->brand_code != "") && ($request->brand_code != null)) {
                $list_query->where('curriculum.brand_code', '=', $request->brand_code);
            }
            if (isset($request->brand_code) && ($request->brand_code != "") && ($request->brand_code != null)) {
                $list_query->where('curriculum.brand_code', '=', $request->brand_code);
            }
            if (isset($request->term_id) && ($request->term_id > 0)) {
                $list_query->where('history_lenky.term_id', '=', $request->term_id);
            }
            if (isset($request->keyword) && ($request->keyword != "") && ($request->keyword != null)) {
                $keyword = $request->keyword;
                $list_query->where(function ($query) use ($keyword) {
                    $query->where('user.user_surname', '=', $keyword)
                        ->orWhere('user.user_middlename', '=', $keyword)
                        ->orWhere('user.user_givenname', '=', $keyword)
                        ->orWhere('user.user_code', '=', $keyword);
                });
            }
            $paginate_number = 10;
            if (isset($request->paginate_number) && ($request->paginate_number > 0)) {
                $paginate_number = $request->paginate_number;
            }
            $data = $list_query->select([
                'user.user_code as student_code',
                DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as student_name'),
                DB::raw('SUBSTRING(user.grade_create, 1, 4) AS grade_create'),
                'user.study_status',
                'curriculum.nganh as brand',
                'curriculum.chuyen_nganh as brand_detail',
                'history_lenky.term_id',
                'history_lenky.term_name',
                'history_lenky.kythu as semester'
            ])
                ->orderBy('history_lenky.id', 'DESC')
                ->paginate($paginate_number);
            return ResponseBuilder::Success($data);
        } catch (\Throwable $th) {
            Log::error('getListPromotedSemesterStudent: ' . $th->getFile() . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage(), [], 500);
        }
    }

    /**
     * upload file import dữ liệu lên kỳ
     *
     * @param  mixed $request
     * @return mixed $response
     */
    public function uploadImportData(Request $request)
    {
        try {
            ini_set("pcre.backtrack_limit", "20000000");
            ini_set('max_execution_time', '7200');
            ini_set('memory_limit', '-1');
            DB::beginTransaction();
            $theCollection = Excel::toArray(new PromotedSemesterImport, $request->file('file'))[0];
            $total_record = count($theCollection);
            $term = Term::where('id', '=', $request->term_id)
                ->select([
                    'id',
                    'term_name'
                ])
                ->firstOrFail();
            $data_import = [];
            $list_user_code = array_column($theCollection, 'mssv');
            $list_user = [];
            foreach (array_chunk($list_user_code, 500) as $arr ){
                $result = User::whereIn('user_code', $arr)
                    ->select([
                        'user_code',
                        'user_login',
                    ])
                    ->get()->toArray();
                foreach ($result as $key => $value) {
                    array_push($list_user, $value);
                }
            }
            $list_user_code_system = array_column($list_user, 'user_code');

            if (count(array_unique($list_user_code)) != count($list_user_code)) {
                DB::rollBack();
                return ResponseBuilder::Fail('Dữ liệu nhập vào có trùng mã sinh viên', [], 400);
            }

            if (count($list_user_code) > count($list_user_code_system)) {
                $diff = implode(', ', array_diff($list_user_code, $list_user_code_system));
                DB::rollBack();
                return ResponseBuilder::Fail('Không tìm được sinh viên trong hệ thống', $diff, 400);
            }

            foreach ($theCollection as $row) {
                $user_login = null;
                foreach ($list_user as $key => $user) {
                    if($user['user_code'] == trim($row['mssv'])){
                        $user_login = $user['user_login'];
                        break;
                    }
                }
                if (!$user_login) {
                    DB::rollBack();
                    throw new \Exception('Dữ liệu import không khớp với dữ liệu trong hệ thống ' . $row['mssv']);
                    return ResponseBuilder::Fail('Dữ liệu import không khớp với dữ liệu trong hệ thống', $row['mssv'], 400);
                }
                $data_import[] = [
                    'user_code' => trim($row['mssv']),
                    'student_login' => $user_login,
                    'term_id' => $request->term_id,
                    'term_name' => $term->term_name,
                    'kythu' => trim($row['ky_thu']),
                ];
            }
            foreach (array_chunk($data_import, 1000) as $array_data ){
                HistoryLenky::upsert($array_data, ['user_code', 'term_id'], ['student_login', 'kythu', 'term_name']);
            }

            foreach ($data_import as $student) {
                $description = "Lên kỳ cho sinh viên: lên kỳ thứ " . $student['kythu'] . " vào kỳ " . $student['term_name'] . " bởi " . Auth::user()->user_login . ".";
                $this->logDraStudentHistory($student['student_login'], 'Lên kỳ', $description, $student['user_code'], $student['term_id'], 1, 1);
                $this->systemLog('Lên kỳ', 'Lên kỳ', $description, $student['student_login']);
                User::where('user_code', '=', $student['user_code'])->update([
                    'kithu' => $student['kythu'],
                    'term_id_lenky' => $student['term_id']
                ]);
            }

            DB::commit();
            return ResponseBuilder::Success($data_import, 'Import thành công ' . $total_record . ' bản ghi');
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error('uploadImportData: ' . $th->getFile() . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage(), [], 500);
        }
    }

    public function exportPromotedSemesterStudentList(Request $request)
    {
        try {
            ini_set("pcre.backtrack_limit", "20000000");
            ini_set('max_execution_time', '7200');
            ini_set('memory_limit', '-1');

            $list_query = HistoryLenky::join('user', 'user.user_code', '=', 'history_lenky.user_code')
                ->join('curriculum', 'user.curriculum_id', '=', 'curriculum.id');
            $list_query->where('user.user_level', 3);
            if (isset($request->grade_create) && ($request->grade_create > 0) && ($request->grade_create != "null")) {
                $list_query->where('user.grade_create', 'LIKE', $request->grade_create . '%');
            }
            if (isset($request->brand_code) && ($request->brand_code != "") && ($request->brand_code != null) && ($request->brand_code != "null")) {
                $list_query->where('curriculum.brand_code', '=', $request->brand_code);
            }
            if (isset($request->brand_code) && ($request->brand_code != "") && ($request->brand_code != null) && ($request->brand_code != "null")) {
                $list_query->where('curriculum.brand_code', '=', $request->brand_code);
            }
            if (isset($request->term_id) && ($request->term_id > 0) && ($request->term_id != "null")) {
                $list_query->where('history_lenky.term_id', '=', $request->term_id);
            }
            if (isset($request->keyword) && ($request->keyword != "") && ($request->keyword != null)) {
                $keyword = $request->keyword;
                $list_query->where(function ($query) use ($keyword) {
                    $query->where('user.user_surname', '=', $keyword)
                        ->orWhere('user.user_middlename', '=', $keyword)
                        ->orWhere('user.user_givenname', '=', $keyword)
                        ->orWhere('user.user_code', '=', $keyword);
                });
            }

            $data = $list_query->select([
                'user.user_code as student_code',
                DB::raw('CONCAT(user.user_surname," ",user.user_middlename," ", user.user_givenname) as student_name'),
                DB::raw('SUBSTRING(user.grade_create, 1, 4) AS grade_create'),
                'user.study_status',
                'curriculum.nganh as brand',
                'curriculum.chuyen_nganh as brand_detail',
                'history_lenky.term_id',
                'history_lenky.term_name',
                'history_lenky.kythu as semester'
            ])
                ->orderBy('history_lenky.id', 'DESC')->toBase()->get();
            foreach ($data as &$row) {
                $row->study_status_code = config('status')->trang_thai_hoc[$row->study_status]['uid'];
            }
            return Excel::download(new PromotedSemesterStudentExport($data), 'Danh_sach_sinh_vien_len_ky.xlsx');
        } catch (\Throwable $th) {
            Log::error('getListPromotedSemesterStudent: ' . $th->getFile() . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
            return ResponseBuilder::Fail($th->getMessage(), [], 500);
        }
    }
}
