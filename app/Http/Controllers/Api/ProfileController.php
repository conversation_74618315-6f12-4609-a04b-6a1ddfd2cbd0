<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Admin\TranscriptController;
use App\Models\Fu\Group;
use App\Models\Fu\Term;
use App\Http\Controllers\Controller;
use App\Models\Fu\Activity;
use App\Models\Fu\Course;
use App\Models\Fu\Decision;
use App\Models\Fu\Slot;
use App\Models\SessionType;
use App\Models\Sms\Account;
use App\Models\SystemLog;
use App\Models\T7\CourseGrade;
use App\Models\T7\CourseResult;
use App\Models\T7\Discipline;
use App\Models\T7\Grade;
use App\Models\T7\SyllabusPlan;
use App\Models\TranferT7Course;
use App\Models\Transcript;
use App\Models\TranscriptDetail;
use App\Models\Fu\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\MienGiamTapTrung;
use App\Models\T7\GradeGroup;


class ProfileController extends Controller
{
    public function lichHoc(Request $request)
    {
        $days = [
            ['name' => '7 ngày tới', 'value' => 7],
            ['name' => '14 ngày tới', 'value' => 14],
            ['name' => '30 ngày tới', 'value' => 30],
            ['name' => '60 ngày tới', 'value' => 60],
            ['name' => '90 ngày tới', 'value' => 90],
            ['name' => '7 ngày trước', 'value' => -7],
            ['name' => '14 ngày trước', 'value' => -14],
            ['name' => '30 ngày trước', 'value' => -30],
            ['name' => '60 ngày trước', 'value' => -60],
            ['name' => '90 ngày trước', 'value' => -90],
        ];
        $day = $request->day;
        $user_id = $request->user_login;
        $accept_days = [7, 14, 30, 60, 90, -7, -14, -30, -60, -90];
        if (empty($day)) {
            $day = 7;
        } else if (!in_array($day, $accept_days)) {
            $day = 7;
        }

        $start_day = Carbon::now();
        $end_day = Carbon::now();
        $end_day->day = $end_day->day + $day;
        if ($end_day->lessThan($start_day)) {
            $temp = $end_day;
            $end_day = $start_day;
            $start_day = $temp;
        }

        $slots = Slot::get();
        $schedules = Activity::join('subject', 'activity.psubject_code', 'subject.subject_code')
            ->join('activity_groups', 'activity.id', 'activity_groups.activity_id')
            ->join('group_member', 'activity_groups.groupid', 'group_member.groupid')
            ->select(
                'activity.id',
                'activity.psubject_code',
                'group_member.groupid',
                'activity.group_name',
                'activity.area_name',
                'activity.day',
                'activity.course_slot',
                'activity.course_id',
                'activity.room_name',
                'subject.subject_name',
                'activity.leader_login as activity_leader_login',
                'activity.slot',
                'activity.start_time',
                'activity.end_time',
                'activity.noi_dung',
                'activity.nv_sinh_vien',
                'activity.hoc_lieu_mon',
                'activity.nv_giang_vien',
                'activity.tai_lieu_tk',
                'activity.tu_hoc',
                'activity.tl_buoi_hoc',
                'activity.muc_tieu',
                'group_member.IsCancelled',
                'activity.psyllabus_id'
            )
            ->where('day', '>=', $start_day->format('Y-m-d'))
            ->where('day', '<=', $end_day->format('Y-m-d'))
            ->where('group_member.member_login', $user_id)
            ->orderBy('day')
            ->orderBy('slot')
            ->get();
        foreach ($schedules as $schedule) {
            $schedule->day = Carbon::createFromDate($schedule->day);
            $msg = null;
            if ($schedule->course_slot >= 200 && $schedule->course_slot != 250) {
                $msg = "(" . "if not passed" . ")";
            }
            if ($schedule->course_slot == 250) {
                $msg = "(" . "Thi giữa kỳ" . ")";
            }
            $schedule->msg = $msg;
            $schedule->slotInfo = $slots->firstWhere('id', $schedule->slot);
            if (!$schedule->slotInfo || $schedule->slotInfo == null) {
                $schedule->slotInfo = [
                    'slot_start' => '',
                    'slot_end' => ''
                ];
            }
            $schedule->day_name = $schedule->day->dayName;
            $schedule->day_transform = $schedule->day->format('d/m/Y');
        }

        return $this->res('success', 'get data success', $schedules);
    }

    public function diemDanh(Request $request)
    {
        $student_id = $request->user_login;
        $term_id = $request->term_id;
        $lists = CourseResult::join('subject', 'subject.subject_code', 't7_course_result.psubject_code')
            ->select('course_id', 'psubject_name', 'groupid', 'psubject_code', 'pgroup_name', 'attendance_detail', 'attendance_absent')
            ->where('term_id', $term_id)
            ->where('student_login', $student_id)
            ->orderBy('subject_id', 'asc')
            ->get();
        $session_type = SessionType::orderBy('id')->get();
        $types = [];
        if ($session_type) {
            foreach ($session_type as $item) {
                $types[$item->id] = $item->session_type;
            }
        }
        foreach ($lists as $list) {
            $list->course = Course::find($list->course_id);
            $list->plans = SyllabusPlan::select([
                't7_syllabus_plan.*',
                'session_type.is_exam'
            ])
                ->leftJoin('session_type', 't7_syllabus_plan.session_type', '=', 'session_type.id')
                ->where('syllabus_id', $list->course->syllabus_id)
                ->orderBy('course_session')->get();
            $list->activities = Activity::select([
                'activity.*',
                'session_type.is_exam'
            ])
            ->leftJoin('session_type', 'activity.session_type', '=', 'session_type.id')
            ->where('groupid', $list->groupid)->where('course_id', $list->course_id)->get();
        }

        $day = date("Y-m-d");
        foreach ($lists as $list) {
            $ds_activity_date = [];
            foreach ($list->plans as $plan) {
                foreach ($list->activities as $activity) {
                    if ($activity->course_slot == $plan->course_session) {
                        $ds_activity_date[$plan->course_session] = $activity->day;
                    }
                }
            }

            $ds_attend_date = [];
            $ds_attend_slot = [];
            $ds_attend_teacher = [];
            $ds_attend_val = [];
            $ds_attend_comment = [];
            $attendance_array = explode(",", $list->attendance_detail);
            $total_session = 0;
            $total_to_now = 0;
            $attendance_absent = 0;
            foreach ($list->plans as $plan) {
                if ($plan->is_exam == 0) {
                    $total_session++;
                    if (isset($ds_activity_date[$plan->course_session]) && $ds_activity_date[$plan->course_session] <= $day) {
                        $total_to_now++;
                    }
                }

                foreach ($attendance_array as $item) {
                    if ($item) {
                        $str_attend = $item;
                        $day_attend_detail = explode(":", $str_attend);
                        try {
                            if ($day_attend_detail[1] == $plan->course_session) {
                                $ds_attend_date[$plan->course_session] = $day_attend_detail[0];
                                $ds_attend_slot[$plan->course_session] = $day_attend_detail[2];
                                $ds_attend_teacher[$plan->course_session] = $day_attend_detail[3];
                                $ds_attend_val[$plan->course_session] = $day_attend_detail[4];
                                $ds_attend_comment['session_' . $plan->course_session] = $day_attend_detail[5];
                            }
                        } catch (\Exception $e) {
                            //                                    dd($attendance_array);
                        }
                    } else {
                        continue;
                    }
                }

                if (isset($ds_activity_date[$plan->course_session])) {
                    $plan_day = $ds_activity_date[$plan->course_session];
                } else {
                    $plan_day = $ds_attend_date[$plan->course_session] ?? null;
                }

                $plan_day = $plan_day ? Carbon::createFromDate($plan_day) : null;
                $plan->plan_day = $plan_day ? $plan_day->format('d/m/Y') . ' - ' . $plan_day->englishDayOfWeek : null;
                $plan->plan_slot = isset($ds_attend_slot[$plan->course_session]) ? $ds_attend_slot[$plan->course_session] : null;
                $plan->plan_teacher = isset($ds_attend_teacher[$plan->course_session]) ? $ds_attend_teacher[$plan->course_session] : null;
                $plan->session_type = $types[$plan->session_type];
                if (isset($ds_attend_val[$plan->course_session])) {
                    if ($ds_attend_val[$plan->course_session] == 1) {
                        $status = ['style' => 'green', 'value' => 'Present'];
                    } else {
                        $status = ['style' => 'red', 'value' => 'Absent'];
                        $attendance_absent++;
                    }
                } else {
                    if (!isset($ds_activity_date[$plan->course_session])) {
                        $status = ['style' => 'green', 'value' => 'Asume present'];
                    } else if ($ds_activity_date[$plan->course_session] == $day) {
                        $status = ['style' => 'green', 'value' => 'Not yet'];
                    } else {
                        if ($day >= $ds_activity_date[$plan->course_session]) {
                            $status = ['style' => 'green', 'value' => 'Asume present'];
                        } else {
                            $status = ['style' => '', 'value' => 'Future'];
                        }
                    }
                }

                if ($attendance_absent) {
                    $t_session = round($attendance_absent * 100 / $total_session);
                    $t_now = round($attendance_absent * 100 / $total_to_now);
                } else {
                    $t_session = 0;
                    $t_now = 0;
                }

                $plan->plan_status = $status;
                $plan->plan_total = $attendance_absent . '/' . $total_session . ' ' . $t_session . '%';
                $plan->plan_current = $attendance_absent . '/' . $total_to_now . ' ' . $t_now . '%';
            }

            $list->ds_activity_date = $ds_activity_date;
            $list->ds_attend_date = $ds_attend_date;
            $list->ds_attend_slot = $ds_attend_slot;
            $list->ds_attend_teacher = $ds_attend_teacher;
            $list->ds_attend_val = $ds_attend_val;
            $list->ds_attend_comment = $ds_attend_comment;
        }

        return $this->res('success', 'get data success', $lists);
    }

    public function cacMonDangHoc(Request $request)
    {
        try {
            $user_login = $request->user_login;
            $course_result = CourseResult::where('student_login', $user_login)
            ->orderBy('start_date', 'DESC')
            ->get();
            
            foreach ($course_result as $item) {
                $flag_group_virtual= Group::where('id', $item->groupid)->value('is_virtual');
                // $dateOfEndTerm = Term::find($item->term_id);
                $str = '';
                $grade_detail = collect([]);
                if (!empty($item->grade_detail)) {
                    $array = explode('$', $item->grade_detail);
                    foreach ($array as $temp_grade) {
                        $item_array = explode(':', $temp_grade);
                        if (sizeof($item_array) < 2) continue;
                        $grade_detail->add(['name' => $item_array[0], 'value' => $item_array[2], 'percent' => $item_array[1], 'comment' => null]);
                    }
                }

                $item->grade_info = $grade_detail;
                $item->attendance_absent = $item->attendance_absent ? $item->attendance_absent : 0;
                $item->done_activity = $item->done_activity ? $item->done_activity : 0;
                if ($item->total_session) {
                    $item->attendance_absent_percent = round(($item->attendance_absent * 100) / $item->total_session);
                    if (((($item->attendance_absent) * 100 / $item->total_session) > (100 - $item->attendance_cutoff)) && $item->attendance_absent > 0) {
                        $str = "Bị cấm thi do nghỉ quá số buổi";
                    }
                    if ($str == "" && ((($item->attendance_absent + 1) * 100 / $item->total_session) > (100 - $item->attendance_cutoff)) && $item->attendance_absent > 0) {
                        $str = "Bị cấm thi nếu nghỉ thêm 1 buổi";
                    }
                } else {
                    $item->attendance_absent_percent = 0;
                }

                if (($item->val == -1) || ($item->val == -13 && $item->is_finish != 0) || $item->is_finish == 2 || ($item->val == 1 && $item->is_finish == 1)) {
                    $item->is_history = 1;
                } else {
                    $item->is_history = 0;
                }

                $item->description = $str;
                $final_date = date_add(date_create($item->end_date), date_interval_create_from_date_string("5 days"));
                $checkingStatus = $this->checkingKindOfFail($item->syllabus_id, $item->groupid, $item->student_login);

                if($item->start_date <= now()->format('Y-m-d') && $final_date->format('Y-m-d') >= now()->format('Y-m-d')) {
                    $status = ['style' => 'info', 'value' => 'Đang học'];
                } else {
                    if($item->val == 0 ) {
                        if($flag_group_virtual == 0) {
                            if($item->start_date <= now()->format('Y-m-d') && $final_date->format('Y-m-d') >= now()->format('Y-m-d')) {
                                $status = ['style' => 'info', 'value' => 'Đang học'];
                            } else if($item->start_date > now()->format('Y-m-d')) {
                                $status = ['style' => 'warning', 'value' => 'Chưa bắt đầu'];
                            } else if($final_date->format('Y-m-d') < now()->format('Y-m-d')) {
                                $status = $checkingStatus;
                            }
                        } else {
                            if($item->val > 0) {
                                $status = ['style' => 'success', 'value' => 'Đạt'];
                            } else {
                                $status = ['style' => 'danger', 'value' => 'Chưa đạt'];
                            }
                        }
                    } else if ($item->val == 1) {
                        // $status = ['style' => 'success', 'value' => 'Đạt'];
                        $status = ['style' => 'success', 'value' => 'Đạt'];
                    } else if ($item->val == -1) {
                        // $status = ['style' => 'danger', 'value' => 'Trượt điểm danh'];
                        $status = ['style' => 'danger', 'value' => 'Trượt điểm danh'];
                    }
                }

                $item->status_info = $status;
            }

            return $this->res('success', 'get data success', $course_result);
        }

        catch (\Exception $e) {
            Log::error("Error Check history table profile: " . $e->getLine() . " err: ".$e->getMessage());
            return $e->getMessage();
        }
    }

    private function checkingKindOfFail($syllabus_id, $group_id, $student_login)
    {
        /**
         * 2. ktra điểm thành phần
         */
        //lấy nhóm đầu điểm thành phần
        $result_grade_group = GradeGroup::select(['id', 'grade_group_name', 'weight', 'minimum_required'])
            ->where('syllabus_id', '=', $syllabus_id)
            ->pluck('id');
        //lấy tất cả các đầu điểm thành phần theo nhóm đầu điểm
        $result_grade_group_detail = Grade::select(['id', 'minimum_required', 'grade_name'])
            ->where('is_final_exam', '=', 0) //trừ môn được đào tạo đánh dấu là kết thúc
            ->whereIn('t7_grade.grade_group_id', $result_grade_group)
            ->orderBy('t7_grade.id')
            ->get();

        //lấy tất cả điểm sinh viên đạt được
        $result_course_grade = CourseGrade::where('groupid', $group_id)
            ->where('login', $student_login)
            ->orderBy('t7_course_grade.grade_id')
            ->pluck('val', 'grade_id');

        $listStudentGrades = CourseGrade::select([
            'id',
            'val', 
            'grade_id',
            'groupid',
            'login',
            'grade_group_id'
        ])
        ->where('groupid', $group_id)
        ->orderBy('grade_id')
        ->get();
        //ktra điểm sinh viên đạt được vs điểm thành phần
        $ds_danger_point = [];
        $listGradeGroupChecks = [];
        foreach ($result_grade_group_detail as $item) {
            if (isset($result_course_grade[$item->id])) {
                if ($item->minimum_required > $result_course_grade[$item->id]) {
                    array_push($ds_danger_point, $item->grade_name);
                    break;
                }
            } else {
                array_push($ds_danger_point, $item->grade_name);
                break;
            }
        }
        // Lấy danh sách đầu điểm cần check 
        if (!isset($listGradeGroupChecks[$syllabus_id])) {
            $listGradeGroupChecks[$syllabus_id] = GradeGroup::query()
            ->whereRaw("syllabus_id = ? AND Id NOT IN (
                SELECT grade_group_id 
                FROM t7_grade WHERE syllabus_id = ?
                AND (
                    is_final_exam = 1	
                    OR t7_grade.bonus_type = 1
                )
            )", [$syllabus_id, $syllabus_id])->with('grades')->get();
        }

        $listGradeGroupCheck = $listGradeGroupChecks[$syllabus_id];
        // Duyệt nhóm đầu điểm
        foreach ($listGradeGroupCheck as $GradeGroup) {
            //lấy ds nhóm đầu điểm của nhóm đầu điểm
            $listGradeByGroup = $GradeGroup->grades;

            //lấy tất cả điểm quá trình sinh viên đạt được
            $listStudentGrade = $listStudentGrades->where('login', $student_login)
            ->where('grade_group_id', $GradeGroup->id);

            if(count($listStudentGrade) < 1) {
                return ['style' => 'danger', 'value' => 'Chưa đạt']; 
            }

            $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
            $groupGradeTotalPoint = 0;
            // Kiểm tra nhóm điểm
            foreach ($listGradeByGroup as $item) {
                if (isset($listStudentGradeArr[$item->id])) {
                    if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                        $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                        array_push($ds_danger_point, $message_reason);
                    }

                    //tính điểm theo nhóm cho sv
                    if ($GradeGroup->weight > 0) {
                        $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                        $groupGradeTotalPoint += $qt_detail_score;
                    }
                }
            }

            // Làm tròn nhóm đầu điểm để xét điều kiện
            $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
            if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {
                
                $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                array_push($ds_danger_point, $message_reason);
            }

           
        }
        if ($ds_danger_point != null && count($ds_danger_point) > 0) {
            return ['style' => 'danger', 'value' => 'Thiếu đầu đểm'];
        }

        return ['style' => 'danger', 'value' => 'Chưa đạt'];

    }

    public function lichSuHocChuyenCoSo(Request $request)
    {
        try {
            $user_login = $request->user_login;
            $course_result = TranferT7Course::where('student_login', $user_login)->orderBy('start_date', 'desc')->get();
            foreach ($course_result as $item) {
                if ($item->val == 0 && ($item->start_date <= now()->format('Y-m-d') && $item->end_date >= now()->format('Y-m-d'))) {
                    // $status = ['style' => 'info', 'value' => 'Đang học'];
                    $status = ['style' => 'info', 'value' => 'Đang học'];
                } else if ($item->val == 0) {
                    // $status = ['style' => 'danger', 'value' => 'Chưa đạt'];
                    $status = ['style' => 'danger', 'value' => 'Chưa đạt'];
                } else if ($item->val == 1) {
                    // $status = ['style' => 'success', 'value' => 'Đạt'];
                    $status = ['style' => 'success', 'value' => 'Đạt'];
                } else if ($item->val == -1) {
                    // $status = ['style' => 'danger', 'value' => 'Trượt điểm danh'];
                    $status = ['style' => 'danger', 'value' => 'Trượt điểm danh'];
                }
    
                $item->status_info = $status;
            }
            return $this->res('success', 'get data success', $course_result);
    
        } catch (\Throwable $th) {
            Log::error("lichSuHocChuyenCoSo - " . $th->getFile() . " - " . $th->getLine() . " - " . $th->getMessage());
            return false;
        }
        

    }

    public function bangDiem(Request $request)
    {
        try {
            $user_login = $request->user_login;
            $user = User::where('user_login', $user_login)->get();
            $res = TranscriptController::syncGradePoint($user);
            if ($res == -1) {
                return response()->json('Không thể đồng bộ điểm', 500);
            }
            $transcript = Transcript::where('user_login', $user_login)->first();
            $transcript->details = TranscriptDetail::where('transcript_id', $transcript->id)->orderBy('ki_thu', 'asc')->get();
            $status = config('status')->transcript_status;
            $status_style = config('status')->transcript_status_style;
            $exemptList = MienGiamTapTrung::where('student_login', $user_login)->where('type',2)->pluck('skill_code')->toArray();

            if ($transcript->details) {
                foreach ($transcript->details as $detail) {
                    $status_id = '';
                    $course_result = CourseResult::where('student_login', $user_login)
                    ->where('skill_code',$detail->skill_code)
                    ->orderBy('term_id', 'DESC')
                    ->orderBy('id', 'DESC')
                    ->first();
                    
                    // dump($dateOfEndTerm->endday);
                    if($detail->status == -4) {
                        $status_id = 'Retest';
                    } else if(($detail->status < 0 && $detail->status !== -4 && $detail->status !== -2)) {
                        $status_id = 'Relearn';
                    }                          

                    if(in_array($detail->skill_code,$exemptList))
                    {
                        $detail->status_info = [
                            'value' => $status['mg'],
                            'style' => $status_style['mg'],
                            'status_id' => $status_id
                        ];
                        continue;

                    }
                    if($course_result != null) {
                        if($detail->status == -3) {
                            $status_info = $this->checkingKindOfFail($course_result->syllabus_id, $course_result->groupid, $course_result->student_login);
                            $status_info['status_id'] = $status_id;
                            $detail->status_info = $status_info;
                            continue;
                        } 
                    }
                    
                    $detail->status_info = [
                        'value' => $status[$detail->status],
                        'style' => $status_style[$detail->status],
                        'status_id' => $status_id
                    ];
                }
            }

            return $this->res('success', 'get data success', $transcript);
        } catch (\Exception $e) {
            Log::error("Error Check Grade table profile: " . __METHOD__ . " - " . $e->getLine() . " err: ".$e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ],500);
        }
    }

    public function kyLuatKhenThuong(Request $request)
    {
        $user_login = $request->user_login;
        $user_code = $request->user_code;
        $union = Decision::select([
            DB::raw('(
                CASE decision.type
                    WHEN ' . Decision::TYPE_KHEN_THUONG . ' THEN 
                        1
                    WHEN ' . Decision::TYPE_KY_LUAT . ' THEN 
                        2
                    ELSE
                        0
                    END
            ) AS "type"'),
            'term.id as term_id',
            'term.term_name',
            'decision.sign_day as date_affected',
            'decision.note',
            'decision.decision_num as decision_no',
            'decision.signer as signee',
            DB::raw('decision_user.note as reason'),
        ])
        ->join('term', 'decision.term_id', 'term.id')
        ->join('decision_user', 'decision.id', '=', 'decision_user.decision_id')
        ->where(function ($q) use ($user_login, $user_code) {
            $q->where('decision_user.user_code', $user_login)
            ->orWhere('decision_user.user_code', $user_code);
        })
        ->whereIn('decision.type', [Decision::TYPE_KHEN_THUONG, Decision::TYPE_KY_LUAT]);

        $lists = Discipline::select([
                't7_discipline.type',
                'term.id as term_id',
                'term.term_name',
                't7_discipline.date_affected',
                't7_discipline.note',
                't7_discipline.decision_no',
                't7_discipline.signee',
                't7_discipline_student.reason',
            ])
            ->join('t7_discipline_student', 't7_discipline.id', 't7_discipline_student.discipline_id')
            ->join('term', 't7_discipline.term_id', 'term.id')
            ->where('t7_discipline_student.student_login', $user_login)
            ->orWhere('t7_discipline_student.student_login', $user_code)
            ->union($union)
            ->get();

        $lists = $lists->sortByDesc("term_id")->values();
        foreach ($lists as $list) {
            $list->date_affected = Carbon::createFromDate($list->date_affected)->format('d-m-Y');
        }

        return $this->res('success', 'get data success', $lists);
    }

    public function sms(Request $request)
    {
        $user_login = $request->user_login;
        $lists = Account::select('id', 'phone', 'owner_type', 'owner_name', 'is_active', 'telco', 'balance', 'created_on')
            ->where('student_login', $user_login)
            ->orderBy('created_on', 'desc')
            ->get();
        $sms_status = config('status')->sms_status;
        foreach ($lists as $list) {
            if ($list->owner_type === null) {
                $list->owner_type = "Chưa rõ";
            } else if ($list->owner_type == 0) {
                $list->owner_type = "Sinh viên";
            } else {
                $list->owner_type = "Phụ huynh";
            }
            $list->is_active = (object) $sms_status[$list->is_active];
        }

        return $this->res('success', 'get data success', $lists);
    }

    public function histories(Request $request)
    {
        try {
            $user_login = $request->user_login;
            $histories = SystemLog::where('relation_login', $user_login)->orderBy('id', 'DESC')->get();
            foreach ($histories as $history) {
                if ($history->object_name == 'group') {
                    $group = Group::select('id', 'group_name', 'psubject_code')->where('list_group.is_virtual', 0)->find($history->object_id);
                    if ($group) {
                        $history->group_name = $group->group_name;
                        $history->subject_code = $group->psubject_code;
                    }
                }
            }

            return $this->res('success', 'get data success', $histories);
        } catch (\Exception $e) {
            Log::error("Error Check Grade table profile: " . $e->getLine() . " err: ".$e->getMessage());
            return $e->getMessage();
        }
    }

    public function bangDiemChiTiet(Request $request)
    {
        $lich_su = CourseResult::select('syllabus_id', 'pgroup_name', 'val', 'grade', 'psubject_name', 'psubject_code', 'grade_detail')
            ->where('groupid', $request->group_id)
            ->where('student_login',$request->user_login)
            ->first();

        $syllabus_id = $lich_su->syllabus_id;
        $course_grade = Grade::query()
            ->select([
                'id',
                'grade_name',
                'weight'
            ])
            ->where('syllabus_id', $syllabus_id)
            ->get();

        $arr_grade = [];
        for ($i = 0; $i < count($course_grade); $i++) {
            $grade = $course_grade[$i];
            $arr_grade[$grade->id]["grade_name"] = $grade->grade_name;
            $arr_grade[$grade->id]["grade_weight"] = $grade->weight;
            $arr_grade[$grade->id]["grade_value"] = "Chưa có";
        }

        $grade_list = explode("$", $lich_su['grade_detail']);
        if ($lich_su['grade_detail'] == "") {
            $arr_grade = [];
        } else {
            for ($i = 0; $i < count($grade_list); $i++) {
                $this_grade_detail = $grade_list[$i];
                $arr = explode(":", $this_grade_detail);
                $arr_grade[$arr[3]]["grade_value"] = ($arr[2] ?? "");
            }
        }

        return $this->res('success', 'get data success', $arr_grade);
    }
}
