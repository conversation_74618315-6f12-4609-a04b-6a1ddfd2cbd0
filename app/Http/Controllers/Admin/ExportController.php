<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use App\Models\Fu\Term;
use App\Models\Fu\Block;
use App\Models\Fu\Subject;
use App\Models\Dra\CurriCulum;
use App\Models\T7\CourseResult;
use App\Models\Fu\User;
use Carbon\Carbon;
use App\Models\T7\Grade;
use App\Models\T7\CourseGrade;
use App\Models\T7\GradeGroup;
use Illuminate\Support\Facades\Log;



class ExportController extends Controller
{
    public function DetailedLessons(Request $request)
    {
        $listBranch = CurriCulum::where('khoa', '>=', '15.3')->orderBy('id', 'DESC')->get();
        $terms = Term::query()->orderBy('id', 'desc')->get();
        return view(env('THEME_ADMIN') . '.export.detailed_lesson', [
            'terms' => $terms,
            'branches' => $listBranch,
        ]);
    }

    public function GetFormDataEnglish(Request $request)
    {
        return view(env('THEME_ADMIN') . '.export.data_english', []);
    }

    /**
     *
     * Xuất lượt học chi tiết
     *
     * <AUTHOR>
     * @since 20/09/2021
     * @version 1.0
     * @param Request $request
     */
    public function exportDetailedLessons(Request $request)
    {
        \Debugbar::disable();
        ini_set("pcre.backtrack_limit", "20000000");
        ini_set('max_execution_time', '7200');
        ini_set('memory_limit', '-1');
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="export_'.time().'.csv";');
        config()->set('database.connections.strict', false);
        $blockCheck = Block::query();
        if ($request->block_id == 1) {
            $blockCheck = $blockCheck->where('block_name', 'block 1');
        } elseif ($request->block_id == 2) {
            $blockCheck = $blockCheck->where('block_name', 'block 2');
        }

        $blockCheck = $blockCheck->where('term_id', $request->term_id)->first();
        $listBranch = CurriCulum::where('khoa', '>=', '15.3')->orderBy('id', 'DESC')->get()->pluck('name', 'id')->toArray();
        $listSubject = Subject::orderBy('id', 'DESC')->get()->pluck('skill_code', 'subject_code')->toArray();
        $listDataCurriculum = DB::select("SELECT
                curriculum.id,
                period.ordering,
                period_subject.subject_code,
                period_subject.skill_code
            FROM period
            LEFT JOIN period_subject on period.id = period_subject.period_id
            LEFT JOIN curriculum on curriculum.id = period_subject.curriculum_id
            WHERE curriculum.khoa >= 13
            ORDER BY
                curriculum.id,
                period.ordering"
        );

        $dataCurriculum = call_user_func(function () use ($listDataCurriculum) {
            $res = [];
            foreach ($listDataCurriculum as $value) {
                $res[$value->id][$value->skill_code] = $value->ordering;
            }

            return $res;
        });

        $strCodition = (isset($request->curriculum_id) && $request->curriculum_id != -1) ? " AND user.curriculum_id = $request->curriculum_id " : null;
        $strCoditionBlock = ($request->block_id != -1 && ($blockCheck)) ? " AND g.block_id = $blockCheck->id " : null;
        $data = DB::select("SELECT
            UPPER ( RIGHT ( b.student_login, 7 ) ),
            b.groupid,
            b.student_login,
            b.pgroup_name,
            b.skill_code,
            b.psubject_code,
            b.block,
            b.attendance_absent,
            b.total_session,
            b.attendance_cutoff,
            b.grade,
            b.val,
            b.start_date,
            b.end_date,
            b.is_finish,
            (case WHEN (b.val >= 0) then b.final_result else '-' end) as 'final_result',
            (case WHEN (b.total_exam > 0 && b.resit_taken > 0) then b.resit_result else '-' end) as 'resit_result',
            a.luot_hoc,
            a.is_virtual,
            b.study_status,
            b.curriculum_id,
            b.kithu,
            b.grade_detail,
            b.syllabus_id
        FROM
            ( SELECT
                    t7_course_result.student_login,
                    t7_course_result.psubject_code,
                    t7_course_result.skill_code,
                    TRIM(t7_course_result.skill_code) as skill_code2,
                    list_group.is_virtual,
                    COUNT( * ) AS luot_hoc
                FROM
                    t7_course_result
                -- LEFT JOIN user on user.user_login = t7_course_result.student_login
                LEFT JOIN list_group on list_group.id = t7_course_result.groupid
                WHERE 1 = 1
                AND t7_course_result.term_id <= $request->term_id
                -- AND list_group.is_virtual = 0
                AND student_login IN (
                    SELECT group_member.member_login FROM group
                    LEFT JOIN group_member  ON list_group.id = group_member.groupid
                    WHERE list_group.pterm_id = $request->term_id
                    AND group_member.member_login IS NOT NULL
                    GROUP BY group_member.member_login
                )
                GROUP BY
                    t7_course_result.student_login,
                    t7_course_result.skill_code
                HAVING
                    COUNT( * ) > 0
                -- ORDER BY
                --     RIGHT ( t7_course_result.student_login, 7 ) ASC
                ) a
            RIGHT JOIN ( SELECT
                    cr.student_login,
                    cr.pgroup_name,
                    cr.skill_code,
                    cr.groupid,
                    cr.psubject_code,
                    cr.block,
                    cr.grade_detail,
                    cr.syllabus_id,
                    cr.attendance_absent,
                    cr.total_session,
                    cr.attendance_cutoff,
                    cr.grade,
                    cr.val,
                    cr.start_date,
                    cr.end_date,
                    cr.is_finish,
                    cr.final_result,
                    cr.total_exam,
                    cr.resit_taken,
                    cr.resit_result,
                    g.block_name,
                    user.study_status,
                    user.curriculum_id,
                    user.kithu
                FROM
                    t7_course_result cr,
                    group_member gm,
                    course c,
                    list_group g,
                    user
                WHERE
                    cr.groupid = g.id
                    AND g.id = gm.groupid
                    AND g.body_id = c.id
                    -- AND g.is_virtual = 0
                    AND cr.student_login = gm.member_login
                    AND c.term_id = $request->term_id
                    AND user.user_login = cr.student_login
                    -- AND user.study_status NOT IN (8, 14, 4)
                    $strCodition
                    $strCoditionBlock
                -- ORDER BY
                --     RIGHT ( cr.student_login, 7 ) ASC,
                --     cr.end_date
            ) b
        ON
            a.student_login = b.student_login
            -- AND a.psubject_code = b.psubject_code
            AND a.skill_code2 = b.skill_code
            ORDER BY student_login");

        $header = [
            'STT',
            'Mã',
            'Tên lớp',
            'Mã chuyển đổi',
            'Mã môn',
            'Học phần',
            'Số buổi nghỉ',
            'Tổng số buổi',
            'Tỷ lệ phải đi học',
            'Điểm',
            'Trạng thái',
            'Ngày đầu',
            'Ngày cuối',
            'Tình trạng lớp',
            'Điểm thi đi',
            'Điểm thi lại',
            'Ngành-Ngành hẹp',
            'Kỳ hiện tại',
            'Trạng thái hiện tại',
            'Kỳ tương ứng (của môn): 1-7, * nếu nằm ngoài khung',
            'Trạng thái môn (học đi: 1/ học lại: 2-n lần)'
        ];

        $statusStudyName = [
            1 => "DH (Đang học)",
            2 => "HL (Học lại)",
            3 => "TN (Tạm nghỉ)",
            4 => "BH (Bỏ học)",
            5 => "CXL (Chờ xếp lớp)",
            6 => "CTN (Chờ tốt nghiệp)",
            7 => "TNG (Đã tốt nghiệp)",
        ];
        $f = fopen('php://output', 'w');
        fputcsv($f, $header);
        foreach ($data as $key => $line) {

            fputcsv($f, [
                ($key + 1),
                $line->student_login,
                $line->pgroup_name,
                $line->skill_code,
                $line->psubject_code,
                $line->block,
                $line->attendance_absent,
                $line->total_session,
                $line->attendance_cutoff,
                $line->grade,
                $this->getStatusSubject($line),
                $line->start_date,
                $line->end_date,
                $line->is_finish,
                $line->final_result,
                $line->resit_result,
                ($listBranch[$line->curriculum_id] ?? ""),
                $line->kithu,
                ($statusStudyName[$line->study_status] ?? ""),
                ($dataCurriculum[$line->curriculum_id][$listSubject[$line->psubject_code]] ?? ""),
                $line->luot_hoc
            ]);
        }
    }

    public function getStatusSubject($item) {


        $flag_group_virtual= $item->is_virtual;
        $grade_detail = collect([]);
        $final_date = date_add(date_create($item->end_date), date_interval_create_from_date_string("5 days"));
        if ($item->grade_detail != null || $item->grade_detail != '' || !empty($item->grade_detail)) {
            $array = explode('$', $item->grade_detail);
            foreach ($array as $temp_grade) {
                $item_array = explode(':', $temp_grade);
                if (sizeof($item_array) < 2) continue;
                $grade_detail->add(['name' => $item_array[0], 'value' => $item_array[2], 'percent' => $item_array[1], 'comment' => null]);
            }
        }

        $status = '';

        if (($item->val >= 0) && ($item->start_date <= now()->format('Y-m-d') && $final_date->format('Y-m-d') >= now()->format('Y-m-d'))) {
            $status = 'Đang học';
        } else {
            if ($item->val == 0  || $item->val == '0') {
                if ($flag_group_virtual == 0) {
                    if ($item->start_date <= now()->format('Y-m-d') && $final_date->format('Y-m-d') >= now()->format('Y-m-d')) {
                        $status = 'Đang học';
                    } else if ($final_date->format('Y-m-d') < now()->format('Y-m-d')) {
                        $status = $this->checkingKindOfFail($item->syllabus_id, $item->groupid, $item->student_login) == 0 ? 'Chưa đạt' : 'Thiếu đầu đểm';
                    }  else if ( now()->format('Y-m-d') < $item->start_date) {
                        $status = "Chưa bắt đầu";
                    }
                } else {
                    if ($item->val >0) {
                        $status = 'Đạt';
                    } else {
                        $status = 'Chưa đạt';
                    }
                }
            } elseif ($item->val == 1 || $item->val == "1" ) {
                $status = 'Đạt';
            } elseif ($item->val == -1 || $item->val == "-1") {
                $status = 'Trượt điểm danh';
            }
        }

        return $status;
    }

    private function checkingKindOfFail($syllabus_id, $group_id, $student_login)
    {
        /**
         * 2. ktra điểm thành phần
         */
        //lấy nhóm đầu điểm thành phần
        $result_grade_group = GradeGroup::select(['id', 'grade_group_name', 'weight', 'minimum_required'])
            ->where('syllabus_id', '=', $syllabus_id)
            ->pluck('id');
        //lấy tất cả các đầu điểm thành phần theo nhóm đầu điểm
        $result_grade_group_detail = Grade::select(['id', 'minimum_required', 'grade_name'])
            ->where('is_final_exam', '=', 0) //trừ môn được đào tạo đánh dấu là kết thúc
            ->whereIn('t7_grade.grade_group_id', $result_grade_group)
            ->orderBy('t7_grade.id')
            ->get();

        //lấy tất cả điểm sinh viên đạt được
        $result_course_grade = CourseGrade::where('groupid', $group_id)
            ->where('login', $student_login)
            ->orderBy('t7_course_grade.grade_id')
            ->pluck('val', 'grade_id');

        $listStudentGrades = CourseGrade::select([
            'id',
            'val',
            'grade_id',
            'groupid',
            'login',
            'grade_group_id'
        ])
        ->where('groupid', $group_id)
        ->orderBy('grade_id')
        ->get();
        //ktra điểm sinh viên đạt được vs điểm thành phần
        $ds_danger_point = [];
        $listGradeGroupChecks = [];
        foreach ($result_grade_group_detail as $item) {
            if (isset($result_course_grade[$item->id])) {
                if ($item->minimum_required > $result_course_grade[$item->id]) {
                    array_push($ds_danger_point, $item->grade_name);
                    break;
                }
            } else {
                array_push($ds_danger_point, $item->grade_name);
                break;
            }
        }
        // Lấy danh sách đầu điểm cần check
        if (!isset($listGradeGroupChecks[$syllabus_id])) {
            $listGradeGroupChecks[$syllabus_id] = GradeGroup::query()
            ->whereRaw("syllabus_id = ? AND Id NOT IN (
                SELECT grade_group_id
                FROM t7_grade WHERE syllabus_id = ?
                AND (
                    is_final_exam = 1
                    OR t7_grade.bonus_type = 1
                )
            )", [$syllabus_id, $syllabus_id])->with('grades')->get();
        }

        $listGradeGroupCheck = $listGradeGroupChecks[$syllabus_id];
        // Duyệt nhóm đầu điểm
        foreach ($listGradeGroupCheck as $GradeGroup) {
            //lấy ds nhóm đầu điểm của nhóm đầu điểm
            $listGradeByGroup = $GradeGroup->grades;

            //lấy tất cả điểm quá trình sinh viên đạt được
            $listStudentGrade = $listStudentGrades->where('login', $student_login)
            ->where('grade_group_id', $GradeGroup->id);

            if(count($listStudentGrade) < 1) {
                return 0;
            }

            $listStudentGradeArr = $listStudentGrade->pluck('val', 'grade_id');
            $groupGradeTotalPoint = 0;
            // Kiểm tra nhóm điểm
            foreach ($listGradeByGroup as $item) {
                if (isset($listStudentGradeArr[$item->id])) {
                    if ($listStudentGradeArr[$item->id] < $item->minimum_required) {
                        $message_reason = "Đầu điểm [$GradeGroup->grade_group_name][$item->grade_name]: " . $listStudentGradeArr[$item->id] . " < " . $item->minimum_required . " - Chưa làm tròn: " . $listStudentGradeArr[$item->id];
                        array_push($ds_danger_point, $message_reason);
                    }

                    //tính điểm theo nhóm cho sv
                    if ($GradeGroup->weight > 0) {
                        $qt_detail_score = ($item->weight / $GradeGroup->weight) * $listStudentGradeArr[$item->id];
                        $groupGradeTotalPoint += $qt_detail_score;
                    }
                }
            }

            // Làm tròn nhóm đầu điểm để xét điều kiện
            $groupGradeTotalPointCheck = round($groupGradeTotalPoint, 1);
            if ($groupGradeTotalPointCheck < $GradeGroup->minimum_required) {

                $message_reason = "Nhóm điểm [$GradeGroup->grade_group_name]: " . $groupGradeTotalPointCheck . " < " . $GradeGroup->minimum_required . " - Chưa làm tròn: " . $groupGradeTotalPointCheck;
                array_push($ds_danger_point, $message_reason);
            }


        }
        if ($ds_danger_point != null && count($ds_danger_point) > 0) {
            return 1;
        }

        return 0;

    }

    public function getDataEnglish(Type $var = null)
    {
        ini_set('max_execution_time', -1);
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="export_'.time().'.csv";');

        $listBranch = CurriCulum::orderBy('id', 'DESC')->get()->pluck('brand_code', 'id')->toArray();
        $statusName = [
            1   => 'Đạt',
            -1  => 'Trượt điểm danh',
            -2  => 'Đang học',
            -3  => 'Trượt quiz',
            -4  => 'Thi lại',
            2   => 'Miễn giảm',
        ];
        $statusStudyName = [
            1 => "DH (Đang học)",
            2 => "HL (Học lại)",
            3 => "TN (Tạm nghỉ)",
            4 => "BH (Bỏ học)",
            5 => "CXL (Chờ xếp lớp)",
            6 => "CTN (Chờ tốt nghiệp)",
            7 => "TNG (Đã tốt nghiệp)",
        ];

        $listEnglish = $data = DB::select("SELECT
                user.user_code,
                user.kithu,
                user.curriculum_id,
                user.note,
                user.study_status,
                english_details.level_1,
                english_details.level_2,
                english_details.level_3,
                english_details.level_4
            FROM
                user
            RIGHT JOIN english_details on english_details.user_login = user.user_login
            WHERE
                user.study_status NOT IN (8, 14, 4)
                AND user.user_level = 3");

        $header = ['STT', 'MSSV', 'NGÀNH', 'TRẠNG THÁI', 'KỲ THỨ', 'Lv1.1', 'Lv1.2', 'Lv2.1', 'Lv2.2', 'Ghi chú'];
        $f = fopen('php://output', 'w');
        fputcsv($f, $header);
        foreach ($data as $key => $line) {
            fputcsv($f, [
                ($key + 1),
                $line->user_code,
                ($statusStudyName[$line->study_status] ?? ""),
                $line->kithu,
                ($listBranch[$line->curriculum_id] ?? ""),
                ($statusName[$line->level_1] ?? ""),
                ($statusName[$line->level_2] ?? ""),
                ($statusName[$line->level_3] ?? ""),
                ($statusName[$line->level_4] ?? ""),
                $line->note
            ]);
        }
    }

    public function exportDetailGroup(Request $request)
    {
        try {
        ini_set("pcre.backtrack_limit", "20000000");
        ini_set('max_execution_time', '7200');
        ini_set('memory_limit', '-1');
        $termObj = Term::find($request->get('term_id', -1));
        if (!$termObj) return;

        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="danh_sach_lop_mon_'.time().'.csv";');
        $statusStudyName = [
            1 => "DH (Đang học)",
            2 => "HL (Học lại)",
            3 => "TN (Tạm nghỉ)",
            4 => "BH (Bỏ học)",
            5 => "CXL (Chờ xếp lớp)",
            6 => "CTN (Chờ tốt nghiệp)",
            7 => "TNG (Đã tốt nghiệp)",
        ];

        $datas = DB::select("SELECT
                user.user_code,
                CONCAT(user.user_surname,' ',user.user_middlename,' ', user.user_givenname) as full_name,
                user.kithu,
                user.study_status,
                group_member.member_login,
                curriculum.brand_code,
                curriculum.id as curriculum_id,
                list_group.id,
                list_group.slot,
                list_group.group_name,
                list_group.psubject_code,
                list_group.skill_code,
                list_group.start_date,
                t2.pterm_name,
                COUNT(group.skill_code) as number_learn
            FROM
                group_member
            LEFT JOIN list_group ON group_member.groupid = list_group.id
            LEFT JOIN user ON user.user_login = group_member.member_login
            LEFT JOIN curriculum on curriculum.id = user.curriculum_id
            LEFT JOIN (
                SELECT
                    group_member.member_login,
                    list_group.id,
                    list_group.psubject_code,
                    list_group.skill_code,
                    pterm_id,
                    pterm_name
                FROM
                    group_member
                    LEFT JOIN list_group ON group_member.groupid = list_group.id
                WHERE
                    list_group.pterm_id <= ?
                    AND group_member.member_login IN (
                        SELECT group_member.member_login
                        FROM group
                        LEFT JOIN group_member ON group_member.groupid = list_group.id
                        WHERE list_group.pterm_id = ?
                        AND list_group.is_virtual = 0
                        GROUP BY group_member.member_login
                    )
                    AND list_group.is_virtual = 0
            ) t2 ON (t2.member_login = group_member.member_login AND t2.skill_code = list_group.skill_code)
            WHERE
                list_group.pterm_id = ?
                AND group_member.member_login <> ''
                AND list_group.is_virtual = 0
            GROUP BY
                group_member.member_login,
                list_group.skill_code,
                list_group.group_name", [$termObj->id, $termObj->id, $termObj->id]);


            // Thêm môn theo kỳ
            $listCurriculumData = [];
            $header = [
                'STT',
                'Mã sinh viên',
                'Họ và tên',
                'Kì thứ',
                'Trạng thái',
                'Tên đăng nhập',
                'Mã Ngành',
                'ID lớp',
                'Tên lớp',
                'Ca học',
                'Mã môn',
                'kỳ theo khung',
                'Ngày bắt đầu',
                'Số lần học',
                'Trạng Thái'
            ];
            $f = fopen('php://output', 'w');
            fputcsv($f, $header);
            foreach ($datas as $key => $line) {
                if (!isset($listCurriculumData[$line->curriculum_id]) && $line->curriculum_id != null) {
                    $dataSubject = DB::select("SELECT
                        -- curriculum.id,
                        -- curriculum.brand_code,
                        -- curriculum.`name`,
                        -- period.id,
                        period.ordering,
                        -- period_subject.subject_code,
                        period_subject.skill_code
                        FROM
                        curriculum
                        JOIN period ON curriculum.id = period.curriculum_id
                        JOIN period_subject ON period.id = period_subject.period_id
                        WHERE curriculum.id = $line->curriculum_id");
                    foreach ($dataSubject as $value) {
                        $dataAddCurriculum[$value->skill_code] = $value->ordering;
                    }

                    $listCurriculumData[$line->curriculum_id] = $dataAddCurriculum;
                }

                $realernType = "Học lần đầu";
                if ($line->number_learn > 1) {
                    $check = CourseResult::where('student_login', $line->member_login)
                    ->where('skill_code', $line->skill_code)
                    ->where('term_id', '<', $termObj->id)
                    ->where('val', 1)
                    ->count();

                    if ($check > 0) {
                        $realernType = "Học đã qua";
                    } else {
                        $realernType = "Học lại";
                    }
                }

                fputcsv($f, [
                    ($key + 1),
                    $line->user_code,
                    $line->full_name,
                    $line->kithu,
                    ($statusStudyName[$line->study_status] ?? ""),
                    $line->member_login,
                    $line->brand_code,
                    $line->group_name,
                    $line->id,
                    $line->slot,
                    $line->psubject_code,
                    ($listCurriculumData[$line->curriculum_id][$line->skill_code] ?? 'không xác định'),
                    $line->start_date,
                    $line->number_learn,
                    $realernType
                ]);
            }
        } catch (\Throwable $th) {
            Log::error("--------------start err exportDetailGroup--------------");
            Log::error($th->getFile() . ' - ' . __METHOD__ . ' - ' . $th->getLine() . ' - ' . $th->getMessage());
            Log::error("--------------end err exportDetailGroup--------------");
        }
    }
}
