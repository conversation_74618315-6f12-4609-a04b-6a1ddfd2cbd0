<?php

namespace App\View\Components;

use App\Services\ChatMeService;
use Illuminate\View\Component;

class ChatMe extends Component
{
    public $school;
    public $config;

    /**
     * Create a new component instance.
     *
     * @param string|null $school
     */
    public function __construct($school = null)
    {
        $chatMeService = new ChatMeService();
        
        // Auto-detect school if not provided
        $this->school = $school ?: $chatMeService->detectSchool();
        
        // Get configuration
        $this->config = $chatMeService->getConfig($this->school);
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.chatme');
    }

    /**
     * Generate the ChatMe script
     *
     * @return string
     */
    public function getScript()
    {
        $chatMeService = new ChatMeService();
        return $chatMeService->generateScript($this->school);
    }
}
