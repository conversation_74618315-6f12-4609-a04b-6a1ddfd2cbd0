<?php

namespace App\Providers;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Blade;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
	if (env('APP_ENV') !== 'local') {
		URL::forceScheme('https');
	}
        View::share('theme_admin', env('MONTER_THEME'));
        Paginator::useBootstrap();
        $_SERVER['SERVER_NAME'] = gethostname();

        // Register ChatMe component
        Blade::component('chatme', \App\View\Components\ChatMe::class);
    }
}
