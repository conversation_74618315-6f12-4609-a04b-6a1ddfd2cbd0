<?php

if (!function_exists('chatme_script')) {
    /**
     * Generate ChatMe script for the specified school
     *
     * @param string|null $school
     * @return string
     */
    function chatme_script($school = null)
    {
        $service = new \App\Services\ChatMeService();
        return $service->generateScript($school);
    }
}

if (!function_exists('chatme_config')) {
    /**
     * Get ChatMe configuration for the specified school
     *
     * @param string|null $school
     * @return array|null
     */
    function chatme_config($school = null)
    {
        $service = new \App\Services\ChatMeService();
        return $service->getConfig($school);
    }
}
