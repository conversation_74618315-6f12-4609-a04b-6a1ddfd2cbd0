<?php

namespace App\Exports\Edu;

use App\Models\Fu\Course;
use App\Models\Fu\Group;
use App\Models\T7\Grade;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;

class ImportDiemSheet1 implements FromView, ShouldAutoSize, WithTitle
{
    private $course_id;

    public function __construct($course_id)
    {
        $this->course_id = $course_id;
    }

    public function view(): View
    {
        $course = Course::find($this->course_id);

        // Sử dụng logic tương tự như trong EducateRepository để đảm bảo thứ tự đúng
        $group_grades = \App\Models\T7\GradeGroup::where('syllabus_id', $course->syllabus_id)
            ->orderBy('id')
            ->get();
        $grades = Grade::where('syllabus_id', $course->syllabus_id)
            ->orderBy('grade_name')
            ->get();

        // Tạo grade_filter theo logic giống EducateRepository
        $grade_filter = [];
        foreach ($group_grades as $group_grade) {
            foreach ($grades as $grade) {
                if ($group_grade->id == $grade->grade_group_id) {
                    $grade_filter[] = $grade;
                }
            }
        }

        $groups = Group::where('body_id', $course->id)->where('list_group.is_virtual', 0)->get();

        return view('excel.edu.import_diem_sheet1', [
            'grades' => $grade_filter,
            'groups' => $groups,
        ]);
    }

    public function getSubject()
    {
        return Course::find($this->course_id);

    }

    public function title(): string
    {
        $course = $this->getSubject();

        return $course->psubject_code;
    }
}
