<?php

namespace App\Exports\Edu;

use App\Models\Fu\Course;
use App\Models\Fu\Group;
use App\Models\T7\Grade;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;

class ImportDiemSheet1 implements FromView, ShouldAutoSize, WithTitle
{
    private $course_id;

    public function __construct($course_id)
    {
        $this->course_id = $course_id;
    }

    public function view(): View
    {
        $course = Course::find($this->course_id);
        $grades = Grade::where('syllabus_id', $course->syllabus_id)->orderBy('id')->get();
        $groups = Group::where('body_id', $course->id)->where('list_group.is_virtual', 0)->get();

        return view('excel.edu.import_diem_sheet1', [
            'grades' => $grades,
            'groups' => $groups,
        ]);
    }

    public function getSubject()
    {
        return Course::find($this->course_id);

    }

    public function title(): string
    {
        $course = $this->getSubject();

        return $course->psubject_code;
    }
}
