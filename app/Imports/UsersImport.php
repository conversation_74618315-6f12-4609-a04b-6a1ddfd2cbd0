<?php

namespace App\Imports;

use App\Models\Fu\User;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Carbon\Carbon;

class UsersImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnFailure
{
    use SkipsFailures;

    private $errors = [];
    private $rowNumber = 0;

    public function onError(array $row, string $field, string $message)
    {
        $this->errors[] = [
            'row' => $this->rowNumber,
            'field' => $field,
            'message' => $message,
            'row_data' => $row,
        ];
    }

    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $this->rowNumber++;

        // Kiểm tra nếu người dùng đã tồn tại với user_login hoặc user_code hoặc email
        $existingCodeUser = User::where('user_code', $row['ma_sinh_vien'])->first();
        $existingEmailUser = User::where('user_email', $row['email'])->first();

        if ($existingCodeUser) {
            $this->onError($row, "ma_sinh_vien", "Mã tài khoản '{$row['ma_sinh_vien']}' đã tồn tại trong hệ thống.");
            return null;
        }

        if ($existingEmailUser) {
            $this->onError($row, "email", "Email '{$row['email']}' đã tồn tại trong hệ thống.");
            return null;
        }

        $dob = null;
        if (!empty($row['ngay_sinh'])) {
            $dobValue = trim($row['ngay_sinh']);

            // Kiểm tra định dạng ngày sinh strict
            $validFormats = [
                'd-m-Y',    // 02-04-2009
                'd/m/Y',    // 02/04/2009
                'Y-m-d',    // 2009-04-02
                'd-m-y',    // 02-04-09
                'd/m/y',    // 02/04/09
            ];

            $isValidFormat = false;
            $parsedDate = null;

            foreach ($validFormats as $format) {
                $date = \DateTime::createFromFormat($format, $dobValue);
                if ($date && $date->format($format) === $dobValue) {
                    $parsedDate = $date;
                    $isValidFormat = true;
                    break;
                }
            }

            if (!$isValidFormat) {
                $this->onError($row, "ngay_sinh", "Ngày sinh '{$dobValue}' không đúng định dạng. Vui lòng sử dụng định dạng: dd-mm-yyyy, dd/mm/yyyy hoặc yyyy-mm-dd");
                return null;
            }

            try {
                $dob = $parsedDate->format('Y-m-d');
                $dobCarbon = Carbon::createFromFormat('Y-m-d', $dob);

                // Kiểm tra ngày sinh không được trong tương lai
                if ($dobCarbon->isFuture()) {
                    $this->onError($row, "ngay_sinh", "Ngày sinh '{$dobValue}' không thể là ngày trong tương lai.");
                    return null;
                }

                // Kiểm tra ngày sinh hợp lý (không quá 120 tuổi)
                if ($dobCarbon->diffInYears(Carbon::now()) > 120) {
                    $this->onError($row, "ngay_sinh", "Ngày sinh '{$dobValue}' không hợp lý (quá 120 tuổi).");
                    return null;
                }

            } catch (\Exception $e) {
                $this->onError($row, "ngay_sinh", "Ngày sinh '{$dobValue}' không hợp lệ.");
                return null;
            }
        }

        // Xử lý gender (gioi_tinh)
        $gender = 1; // Mặc định là Nam
        if (isset($row['gioi_tinh'])) {
            $gt = strtolower(trim($row['gioi_tinh']));
            if ($gt === 'Nam' || $gt === 'male' || $gt === '1' || $gt === 1) {
                $gender = 1;
            } elseif ($gt === 'Nữ' || $gt === 'nu' || $gt === 'female' || $gt === '0' || $gt === 0) {
                $gender = 0;
            } else {
                // Nếu giá trị không hợp lệ thì mặc định là Nam
                $gender = 1;
            }
        }

        return new User([
            'user_login'      => $row['ma_sinh_vien'],
            'user_pass'       => Hash::make('123@123'),
            'user_email'      => $row['email'],
            'user_code'       => $row['ma_sinh_vien'],
            'user_surname'    => $row['ho'] ?? '',
            'user_middlename' => $row['ten_dem'] ?? '',
            'user_givenname'  => $row['ten'] ?? '',
            'user_DOB'        => $dob,
            'user_telephone'  => $row['so_dien_thoai'] ?? '',
            'user_address'    => $row['dia_chi'] ?? '',
            'gender'          => $gender,
            'dantoc'          => $row['dan_toc'] ?? 'Kinh',
            'cmt'             => $row['cccd'] ?? '',
            'noicap'          => $row['noi_cap'] ?? '',
            'study_status'    => 0,
            'study_status_code' => '',
            'grade_create'    => $row['khoa_nhap_hoc'] ?? null,
            'user_level'      => $row['quyen_truy_cap'] ?? 3, // Mặc định là học sinh
            'created_at'      => Carbon::now(),
            'updated_at'      => Carbon::now(),
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'ma_sinh_vien' => 'required|regex:/^[a-zA-Z0-9_]+$/',
            'email'     => 'required|email',
            'ho'        => 'required',
            'ten_dem'   => 'required',
            'ten'       => 'required',
            'ngay_sinh' => 'required',
            'so_dien_thoai' => 'required',
            'dia_chi'   => 'nullable',
            'quyen_truy_cap' => 'required|in:1,2,3,4',
            'gioi_tinh' => 'required|in:0,1',
            'dan_toc'   => 'required',
            'khoa_nhap_hoc' => 'required',
        ];
    }

    /**
     * Custom validation messages
     */
    public function customValidationMessages()
    {
        return [
            'ma_sinh_vien.required' => 'Mã sinh viên là bắt buộc.',
            'ma_sinh_vien.regex' => 'Mã sinh viên chỉ được chứa chữ cái, số và dấu gạch dưới.',
            'email.required' => 'Email là bắt buộc.',
            'email.email' => 'Email không đúng định dạng.',
            'ho.required' => 'Họ là bắt buộc.',
            'ten_dem.required' => 'Tên đệm là bắt buộc.',
            'ten.required' => 'Tên là bắt buộc.',
            'ngay_sinh.required' => 'Ngày sinh là bắt buộc.',
            'so_dien_thoai.required' => 'Số điện thoại là bắt buộc.',
            'quyen_truy_cap.required' => 'Quyền truy cập là bắt buộc.',
            'quyen_truy_cap.in' => 'Quyền truy cập phải là 1, 2, 3 hoặc 4.',
            'gioi_tinh.required' => 'Giới tính là bắt buộc.',
            'gioi_tinh.in' => 'Giới tính phải là 0 (Nữ) hoặc 1 (Nam).',
            'dan_toc.required' => 'Dân tộc là bắt buộc.',
            'khoa_nhap_hoc.required' => 'Khóa nhập học là bắt buộc.',
        ];
    }


}
