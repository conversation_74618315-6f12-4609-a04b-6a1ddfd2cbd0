<?php

namespace App\Services;

class ChatMeService
{
    /**
     * Get ChatMe configuration for current school
     *
     * @param string|null $school
     * @return array
     */
    public function getConfig($school = null)
    {
        if (!config('chatme.enabled')) {
            return null;
        }

        // If school is specified, use school-specific config
        if ($school && isset(config('chatme.schools')[$school])) {
            $schoolConfig = config('chatme.schools')[$school];
            return array_merge($this->getDefaultConfig(), $schoolConfig);
        }

        // Use default configuration
        return $this->getDefaultConfig();
    }

    /**
     * Get default ChatMe configuration
     *
     * @return array
     */
    private function getDefaultConfig()
    {
        return [
            'id' => config('chatme.id'),
            'position' => config('chatme.position'),
            'offset' => config('chatme.offset'),
            'button_size' => config('chatme.button_size'),
            'height' => config('chatme.height'),
            'width' => config('chatme.width'),
            'custom_icon' => config('chatme.custom_icon'),
        ];
    }

    /**
     * Generate ChatMe script HTML
     *
     * @param string|null $school
     * @return string
     */
    public function generateScript($school = null)
    {
        $config = $this->getConfig($school);

        if (!$config || empty($config['id'])) {
            return '';
        }

        $attributes = [
            'src="https://sdk.chatme.vn/cm.js"',
            'data-chatme-id="' . $config['id'] . '"',
        ];

        // Add optional attributes
        if (!empty($config['position'])) {
            $attributes[] = 'position="' . $config['position'] . '"';
        }

        if (!empty($config['offset'])) {
            $offsetAttr = $config['position'] === 'left' ? 'left' : 'right';
            $attributes[] = $offsetAttr . '="' . $config['offset'] . '"';
        }

        if (!empty($config['button_size'])) {
            $attributes[] = 'button-size="' . $config['button_size'] . '"';
        }

        if (!empty($config['height'])) {
            $attributes[] = 'height="' . $config['height'] . '"';
        }

        if (!empty($config['width'])) {
            $attributes[] = 'width="' . $config['width'] . '"';
        }

        if (!empty($config['custom_icon'])) {
            $attributes[] = 'image-svg=\'' . $config['custom_icon'] . '\'';
        }

        return '<script ' . implode(' ', $attributes) . '></script>';
    }

    /**
     * Determine school from current domain or configuration
     *
     * @return string|null
     */
    public function detectSchool()
    {
        // You can implement logic to detect school based on:
        // - Domain name
        // - Environment variable
        // - Database setting
        // - etc.

        $domain = request()->getHost();
        
        if (str_contains($domain, 'vov')) {
            return 'vov';
        }
        
        if (str_contains($domain, 'mtu')) {
            return 'mtu';
        }

        // Check environment variable
        $schoolCode = env('SCHOOL_CODE');
        if ($schoolCode && isset(config('chatme.schools')[$schoolCode])) {
            return $schoolCode;
        }

        return null;
    }
}
