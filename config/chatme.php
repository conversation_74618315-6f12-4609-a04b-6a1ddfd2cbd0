<?php

return [
    /*
    |--------------------------------------------------------------------------
    | ChatMe Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for ChatMe SDK integration.
    | You can customize the appearance and behavior of the chat widget.
    |
    */

    // Enable/disable ChatMe widget
    'enabled' => env('CHATME_ENABLED', false),

    // ChatMe ID for different schools
    'id' => env('CHATME_ID', ''),

    // Widget position: 'left' or 'right'
    'position' => env('CHATME_POSITION', 'right'),

    // Offset from left/right edge (supports px, vw, rem, em, %)
    'offset' => env('CHATME_OFFSET', '30px'),

    // Button size
    'button_size' => env('CHATME_BUTTON_SIZE', '48px'),

    // Widget dimensions
    'height' => env('CHATME_HEIGHT', '500px'),
    'width' => env('CHATME_WIDTH', '300px'),

    // Custom SVG icon (optional)
    'custom_icon' => env('CHATME_CUSTOM_ICON', ''),

    // School-specific configurations
    'schools' => [
        'vov' => [
            'id' => '6c3fcbbe40a995a08d357fb537e6451a',
            'position' => 'right',
            'offset' => '30px',
        ],
        'mtu' => [
            'id' => 'ac226d59f0a8a8e64e07cbf457b85b5s',
            'position' => 'right',
            'offset' => '30px',
        ],
        'default' => [
            'id' => env('CHATME_ID', ''),
            'position' => env('CHATME_POSITION', 'right'),
            'offset' => env('CHATME_OFFSET', '30px'),
        ],
    ],
];
