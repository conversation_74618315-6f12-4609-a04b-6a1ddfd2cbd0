# ChatMe Integration Guide

## Tổng quan
ChatMe đã được tích hợp vào hệ thống quản lý đào tạo với khả năng hỗ trợ nhiều trường khác nhau.

## Cấu hình

### 1. File .env
Thêm các cấu hình sau vào file `.env`:

```env
# ChatMe Configuration
CHATME_ENABLED=true
CHATME_ID=e945870302a5552f0f7ca63cefec6868
CHATME_POSITION=right
CHATME_OFFSET=30px
CHATME_BUTTON_SIZE=48px
CHATME_HEIGHT=500px
CHATME_WIDTH=300px

# School Code for multi-tenant support
SCHOOL_CODE=default
```

### 2. C<PERSON>u hình cho từng trường

#### Trường VOV
```env
SCHOOL_CODE=vov
CHATME_ID=6c3fcbbe40a995a08d357fb537e6451a
```

#### Trường MTU
```env
SCHOOL_CODE=mtu
CHATME_ID=ac226d59f0a8a8e64e07cbf457b85b5s
```

### 3. Tùy chỉnh giao diện

#### Position (Vị trí)
- `left`: Hiển thị bên trái
- `right`: Hiển thị bên phải (mặc định)

#### Offset (Khoảng cách)
- Hỗ trợ các đơn vị: `px`, `vw`, `rem`, `em`, `%`
- Ví dụ: `30px`, `5vw`, `2rem`

#### Kích thước
- `CHATME_BUTTON_SIZE`: Kích thước nút chat
- `CHATME_HEIGHT`: Chiều cao widget
- `CHATME_WIDTH`: Chiều rộng widget

## Sử dụng

### 1. Tự động
Widget ChatMe sẽ tự động hiển thị trên tất cả các trang admin sau khi đăng nhập.

### 2. Thủ công
Bạn có thể thêm widget vào bất kỳ trang nào bằng cách:

```blade
{{-- Sử dụng component --}}
<x-chatme />

{{-- Hoặc chỉ định trường cụ thể --}}
<x-chatme school="vov" />
```

### 3. Sử dụng Helper function
```php
// Trong Controller hoặc View
echo chatme_script(); // Tự động detect trường
echo chatme_script('vov'); // Chỉ định trường VOV

// Lấy config
$config = chatme_config('mtu');
```

## Cấu hình nâng cao

### 1. Thêm trường mới
Chỉnh sửa file `config/chatme.php`:

```php
'schools' => [
    'new_school' => [
        'id' => 'your_chatme_id_here',
        'position' => 'right',
        'offset' => '30px',
    ],
    // ... các trường khác
],
```

### 2. Tùy chỉnh icon
```env
CHATME_CUSTOM_ICON='<svg>...</svg>'
```

### 3. Tắt ChatMe
```env
CHATME_ENABLED=false
```

## Troubleshooting

### 1. Widget không hiển thị
- Kiểm tra `CHATME_ENABLED=true`
- Kiểm tra `CHATME_ID` có đúng không
- Xóa cache: `php artisan config:clear`

### 2. Sai trường
- Kiểm tra `SCHOOL_CODE` trong .env
- Kiểm tra domain detection trong `ChatMeService::detectSchool()`

### 3. Lỗi component
- Chạy: `php artisan view:clear`
- Kiểm tra component đã được đăng ký trong `AppServiceProvider`

## Files liên quan

- `config/chatme.php` - Cấu hình chính
- `app/Services/ChatMeService.php` - Logic xử lý
- `app/View/Components/ChatMe.php` - Blade component
- `resources/views/components/chatme.blade.php` - Template
- `resources/views/layouts/admin_v1/main.blade.php` - Layout chính
